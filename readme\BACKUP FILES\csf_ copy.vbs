' Function to convert BLF files to CSV in a separate thread
Sub ConvertBLFToCSV(instanceFolder, configPath, csvFolder, App)
    ' Set App = CreateObject("CANoe.Application")
    App.Visible = True ' Set the CANoe application to be visible
    ' Load config
    App.Open configPath
    ' WScript.Echo "Opened Config: " & configPath
    WScript.Sleep 500

    Dim blf_folder, src_file, dst_file
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set blf_folder = fso.GetFolder(instanceFolder)

    For Each blf_file In blf_folder.Files
        If LCase(fso.GetExtensionName(blf_file.Name)) = "blf" Then
            src_file = instanceFolder & "\" & blf_file.Name
            dst_file = csvFolder & "\" & fso.GetBaseName(blf_file.Name) & ".csv"
            ' MsgBox src_file & " " & dst_file
            If App.Measurement.Running Then
                App.Measurement.Stop
            End If

            ' Create a new logging collection for each file
            Set Logging = App.Configuration.OnlineSetup.LoggingCollection(1)
            Set Exporter = Logging.Exporter

            With Exporter.Sources
                .Clear
                .Add(src_file)
            End With

            With Exporter.Destinations
                .Clear
                .Add(dst_file)
            End With
            Exporter.Save True
            ' Parent of blf_folder
            blfFolderParent = fso.GetParentFolderName(blf_folder)
            ' Move blf file to COMPLETED folder in the script directory
            fso.MoveFile src_file, blfFolderParent & "\COMPLETED\" & blf_file.Name
        End If
    Next

    ' ' Quit the CANoe application after converting all files
    ' App.Quit
    ' Set App = Nothing
End Sub

' Retrieve the arguments passed to the script
Dim instanceFolder, configPath, csvFolder
instanceFolder = WScript.Arguments.Item(0)
configPath = WScript.Arguments.Item(1)
csvFolder = WScript.Arguments.Item(2)
' Create a shell object
Set WshShell = CreateObject("WScript.Shell")

' Get the process IDs of the already running CANoe instances
Set WMI = GetObject("winmgmts://./root/cimv2")
' Retrieve the process IDs of the running CANoe instances
Set ProcessList = WMI.ExecQuery("SELECT * FROM Win32_Process WHERE Name='CANoe64.exe'")
' Loop through the processes and find a non-busy instance
' Print process list
' For Each Process In ProcessList
'     WScript.Echo Process.ProcessId & " " & Process.CommandLine
' Next

Dim processID, appFound
For Each Process In ProcessList
    ' Get the command line of the process
    cmdLine = Process.CommandLine
    ' Check if the command line contains the configPath
    If InStr(1, cmdLine, configPath, vbTextCompare) > 0 Then
        ' Attempt to attach to the CANoe instance
        On Error Resume Next
        Set App = GetObject(, "CANoe.Application." & Process.ProcessId)
        If Err.Number = 0 Then
            ' App object successfully created, check if App.Measurement is available and not running
            If Not App.Measurement.Running Then
                appFound = True
                ' Call the ConvertBLFToCSV subroutine with the provided arguments
                ConvertBLFToCSV instanceFolder, configPath, csvFolder, App
                Exit For
            End If
        End If
        On Error GoTo 0
    End If
Next

If Not appFound Then
    ' No non-busy instance found, attempt to create a new instance
    On Error Resume Next
    WScript.Echo "No available CANoe instances found. Creating a new instance."
    Set App = CreateObject("CANoe.Application")
    If Err.Number = 0 Then
        ' App object successfully created, open the configPath
        App.Open(configPath)
        ' Call the ConvertBLFToCSV subroutine with the provided arguments
        ' ConvertBLFToCSV instanceFolder, configPath, csvFolder, App
    Else
        WScript.Echo "No available CANoe instances found."
    End If
    On Error GoTo 0
End If

' ' Call the function to convert the BLF files to CSV
' ConvertBLFToCSV instanceFolder, configPath, csvFolder, App
' ' Quit the CANoe application after converting all files

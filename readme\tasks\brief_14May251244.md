## Queue Management using Python

### Set-Up
- Google sheet id `12kQ7cEw2Ix94pCNs8n8YJ5VGgWkjgjuuFDoxsO_jvX4`
- Sheet Name: `MAIN`
- Sheet header:
    - file_name
    - blf_link
    - converted_link
    - queue_number
    - queue_status
- Drive Id : `1BFbahFfHlndjov7TYVM5yvU8r5reMEje`
- Folder Structure:
    - BLF_FILES/
        - gen_1/
            - vin_test.blf
            ...
        - gen_2_3/
            ...
        - s1x/ 
            ...
        - s1x_plus/ 
            ...
    - CSV_FILES/
        ...
    - ASCII_FILES/
        ...

### Available Scripts
- Folder : `@drive/`
- Files : `auth.json` and `token.pickle` are present in the folder
- Files : `main.py` is present in the folder

### Required Functionality `@src/queue_manager/*`
- <PERSON><PERSON><PERSON> will check the drive `BLF_FILES` folder every minute and compare with the local hash
- If there is any change in the drive `BLF_FILES` (i.e files are added) the local queue db (sqlite3) will be updated with the new files and for each file the queue number will be assigned
- Convertion will be done using the `convert_single.vbs` script (check tests/call_single_convert.py)
- After convertion the converted file will be moved to the `CSV_FILES` folder and queue number will be updated in the local queue db
- The spreadsheet can be updated every 15 minutes with the new status of the files

import os
import io
import re
import pickle
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.http import MediaIoBaseDownload
from io import BytesIO

# yester_yyyy_mm_dd = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
folder_name = "24h "+ input('FOLDER NAME: ')
# folder_name = "24h " + yester_yyyy_mm_dd
print(f"folder_name: {folder_name}")
print("""
██████╗  ██████╗ ██╗    ██╗███╗   ██╗██╗      ██████╗  █████╗ ██████╗ 
██╔══██╗██╔═══██╗██║    ██║████╗  ██║██║     ██╔═══██╗██╔══██╗██╔══██╗
██║  ██║██║   ██║██║ █╗ ██║██╔██╗ ██║██║     ██║   ██║███████║██║  ██║
██║  ██║██║   ██║██║███╗██║██║╚██╗██║██║     ██║   ██║██╔══██║██║  ██║
██████╔╝╚██████╔╝╚███╔███╔╝██║ ╚████║███████╗╚██████╔╝██║  ██║██████╔╝
╚═════╝  ╚═════╝  ╚══╝╚══╝ ╚═╝  ╚═══╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═════╝ 
                                                                      
""")
drive_id = "1pdDggrQXTyIFXPBRiDTXc_KBTIqbgGcW"
input_folder = os.path.join(os.getcwd(), 'input')
# Google Drive API credentials
client_id = '206788518451-8tkc5n67tl9i1s5hjir1mcmlc15pmnfa.apps.googleusercontent.com'
client_secret = '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'

# Scope for accessing Google Drive files
SCOPES=['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']


def authenticate_google_drive():
    creds = None
    token_path = 'drive/token.pickle'

    if os.path.exists(token_path):
        with open(token_path, 'rb') as token:
            creds = pickle.load(token)
    
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                'drive/auth.json', SCOPES)
            creds = flow.run_local_server(port=0)
        
        with open(token_path, 'wb') as token:
            pickle.dump(creds, token)
    
    return creds

# Function to get the file ID
def get_file_id(service, folder_id, file_name):
    query = f"'{folder_id}' in parents and name = '{file_name}'"
    results = service.files().list(q=query).execute()
    items = results.get('files', [])
    
    if items:
        return items[0]['id']
    else:
        print(f"File '{file_name}' not found in folder '{folder_id}'")
        return None
    
def download_files_with_name(service, folder_id, file_name):
    pattern = re.compile(rf".*{re.escape(file_name)}.*", re.IGNORECASE)

    results = service.files().list(
        q=f"mimeType != 'application/vnd.google-apps.folder' and '{folder_id}' in parents",
        fields="files(id, name)",
        pageSize=1000
    ).execute()

    files = results.get('files', [])
    if not files:
        print("No files found.")
        return
    
    matching_files = [file for file in files if pattern.match(file['name'])]
    if not matching_files:
        print("No matching files found.")
        return
    
    for file in matching_files:
        file_id = file['id']
        file_name = file['name']
        request = service.files().get_media(fileId=file_id)
        file_path = os.path.join("input", file_name)  # Specify the destination folder
        fh = io.FileIO(file_path, 'wb')
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while done is False:
            status, done = downloader.next_chunk()
            print(f"Downloaded {int(status.progress() * 100)}%: {file_name}")
    
    print("Download complete.")

def list_all_files(drive_inst, folder_id):
    all_files = []
    page_token = None
    while True:
        response = drive_inst.files().list(
            q=f"'{folder_id}' in parents and mimeType='text/csv'",
            spaces='drive',
            fields='nextPageToken, files(id, name)',
            pageToken=page_token
        ).execute()
        
        all_files.extend(response.get('files', []))
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    # return all_files unique
    return all_files

def download_csv_files(drive_inst, folder_id):
    # Create the date_folder within drive_id if it doesn't exist
    folder_metadata = {
        'name': folder_name,
        'mimeType': 'application/vnd.google-apps.folder',
        'parents': [drive_id]
    }
    folder_query = f"name='{folder_name}' and '{drive_id}' in parents and mimeType='application/vnd.google-apps.folder'"
    existing_folders = drive_inst.files().list(q=folder_query).execute().get('files', [])
    if existing_folders:
        folder_id = existing_folders[0]['id']
        print(f"Date folder already exists with ID: {folder_id}")
    else:
        print('Folder does not exist. Exiting...')
        exit()

    files = list_all_files(drive_inst, folder_id)
    print(f"Found {len(files)} files within the folder")
    # os.listdir(input_folder) filter csv_files
    csv_files = [file for file in os.listdir(input_folder) if file.endswith('.csv')]
    # breakpoint()
    for i, file in enumerate(files):
        # breakpoint()
        file_name = file['name']
        file_name = re.sub(r':', '_', file_name)
        # List of special characters not allowed in Windows file naming convention
        special_chars = r'[<>"/\\|?*\n()]'
        # Replace special characters with '_'
        file_name = re.sub(special_chars, '', file_name)
        file_path = os.path.join(input_folder, file_name)
        if os.path.exists(file_path):
            print(f"File {file_name} already exists")
        else:
            print(f"Downloading {file_name}...")
            request = drive_inst.files().get_media(fileId=file['id'])
            fh = io.FileIO(file_path, 'wb')
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
                print(f"Download {int(status.progress() * 100)}%.")
    # remove all files which are empty
    csv_files = [file for file in os.listdir(input_folder) if file.endswith('.csv')]
    for file in csv_files:
        file_path = os.path.join(input_folder, file)
        if os.path.getsize(file_path) == 0:
            print(f"Removing empty file: {file}")
            breakpoint()
            os.remove(file_path)


# Function to download a file from Google Drive
def download_file(service, file_id, destination_folder, destination_file_name):
    request = service.files().get_media(fileId=file_id)
    fh = BytesIO()
    downloader = MediaIoBaseDownload(fh, request)
    
    done = False
    while done is False:
        status, done = downloader.next_chunk()
        print(f"Download {int(status.progress() * 100)}%")
    
    fh.seek(0)
    with open(os.path.join(destination_folder, destination_file_name), 'wb') as f:
        f.write(fh.read())
    print(f"Downloaded '{destination_file_name}' to '{destination_folder}'")

def main():
    # Create the destination folder if it doesn't exist
    destination_folder = "input"
    os.makedirs(destination_folder, exist_ok=True)
    creds = authenticate_google_drive()
    service = build('drive', 'v3', credentials=creds)
    download_csv_files(service, drive_id)

if __name__ == '__main__':
    main()

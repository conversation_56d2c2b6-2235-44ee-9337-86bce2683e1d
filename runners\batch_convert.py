import os
import threading
import time
from pathlib import Path

from dotenv import load_dotenv
from loguru import logger

from runners.uploader import upload_csv_and_update_flag
from src.queue_manager.core import QueueManager
from src.queue_manager.interfaces.convert import run_conversion

load_dotenv()  # Load environment variables from .env file

# Logging setup
LOG_DIR = os.path.join(os.getcwd(), 'logs', 'batch_convert')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

# Config mapping for different subdirectories
CONFIG_MAPPING = {
    'gen_1': os.path.join(os.getcwd(), 'config', 'gen_1', 'config.cfg'),
    'gen_2_3': os.path.join(os.getcwd(), 'config', 'gen_2_3', 'config.cfg'),
    's1x': os.path.join(os.getcwd(), 'config', 's1x', 'config.cfg'),
    's1x_plus': os.path.join(os.getcwd(), 'config', 's1x_plus', 'config.cfg')
}

LOCAL_BLF_DIR = os.path.join(os.getcwd(), 'blf')
LOCAL_CSV_DIR = os.path.join(os.getcwd(), 'output', 'csv')

def get_config_for_subfolder(subfolder):
    if subfolder in CONFIG_MAPPING:
        return CONFIG_MAPPING[subfolder]
    return CONFIG_MAPPING['gen_1']

def async_upload(item_to_upload, queue_manager_instance): # Renamed for clarity
    """Wrapper for asynchronous upload, expects the full item dictionary."""
    try:
        # upload_csv_and_update_flag expects the item dictionary and the queue manager instance
        upload_csv_and_update_flag(item_to_upload, queue_manager_instance)
    except Exception as e:
        # Log which item failed, using its drive_filename or another identifier
        failed_item_name = item_to_upload.get('drive_filename', 'Unknown item')
        logger.error(f"Async upload failed for {failed_item_name}: {e}", exc_info=True)

def process_queue():
    queue = QueueManager()
    # Get files that are downloaded but not yet converted.
    # The get_files_to_convert method already filters by downloaded=1, converted=0, status='pending'
    pending_conversion = queue.get_files_to_convert()
    
    if not pending_conversion:
        logger.info("No files pending conversion.")
        return

    for item in pending_conversion:
        drive_filename = item['drive_filename']
        local_filename = item['local_filename'] # This is the BLF filename
        subfolder = item.get('subfolder', '') or 'gen_1' # Default to gen_1 if not specified
        # blf_link = item['blf_link'] # blf_link is the key for sheet updates

        # Construct paths
        # Ensure LOCAL_BLF_DIR and LOCAL_CSV_DIR are defined globally or passed appropriately
        local_blf_path = os.path.join(LOCAL_BLF_DIR, subfolder, local_filename)
        # Ensure local_filename (BLF) is used to derive CSV name
        csv_filename = local_filename.replace('.blf', '.csv').replace('.BLF', '.csv')
        local_csv_path = os.path.join(LOCAL_CSV_DIR, subfolder, csv_filename)
        
        config_path = get_config_for_subfolder(subfolder)

        # Ensure output directory for CSV exists
        Path(local_csv_path).parent.mkdir(parents=True, exist_ok=True)

        # Normalize paths
        local_blf_path = str(Path(local_blf_path).resolve())
        local_csv_path = str(Path(local_csv_path).resolve())
        config_path = str(Path(config_path).resolve())

        logger.info(f"Processing {drive_filename} from subfolder '{subfolder}'. BLF: {local_blf_path}, Target CSV: {local_csv_path}")

        # Check if BLF file actually exists before attempting conversion
        if not os.path.exists(local_blf_path):
            logger.warning(f"BLF file missing at {local_blf_path} for {drive_filename}. Skipping and marking error.")
            # Mark as error_converting because the source file is missing
            item = queue.update_status(item, 'error_converting') # Pass the whole item, capture updated
            continue

        # Check if CSV already exists and is valid (e.g. > 0 bytes)
        # This check is important to avoid re-conversion if not needed.
        if os.path.exists(local_csv_path) and os.path.getsize(local_csv_path) > 0:
            logger.info(f"CSV already exists and is valid for {drive_filename} at {local_csv_path}. Updating status to 'converted'.")
            # Update status to 'converted' and provide the existing CSV path
            # QueueManager's update_status will handle setting converted=1 flag and sheet update
            # Pass local_csv_path to the new local_csv_path_val parameter
            updated_item = queue.update_status(item, 'converted', local_csv_path_val=local_csv_path)
            # Trigger async upload for the existing CSV
            logger.info(f"Existing CSV file ready for upload: {local_csv_path}. Triggering async upload.")
            # Pass the updated_item and the 'queue' instance to async_upload
            threading.Thread(target=async_upload, args=(updated_item, queue), daemon=True).start()
            continue
        
        # Update status to 'converting' before starting the conversion process
        logger.info(f"Setting status to 'converting' for {drive_filename} ({subfolder})")
        item = queue.update_status(item, 'converting') # Pass the whole item, capture updated

        logger.info(f"Attempting conversion for {drive_filename} using config {config_path}")
        conversion_successful = run_conversion(config_path, local_blf_path, local_csv_path)

        if conversion_successful:
            # run_conversion now verifies file existence and non-emptiness
            logger.info(f"Successfully converted {drive_filename} to {local_csv_path}")
            # QueueManager's update_status will set converted=1 and update sheet
            # Pass local_csv_path to the new local_csv_path_val parameter
            updated_item = queue.update_status(item, 'converted', local_csv_path_val=local_csv_path)
            
            logger.info(f"File ready for upload: {local_csv_path}. Triggering async upload.")
            # Pass the updated_item and the 'queue' instance to async_upload
            threading.Thread(target=async_upload, args=(updated_item, queue), daemon=True).start()
        else:
            # run_conversion failed, or the output file was not created/valid
            logger.error(f"Failed to convert {drive_filename} or output CSV is invalid/missing at {local_csv_path}.")
            # QueueManager's update_status will update sheet
            item = queue.update_status(item, 'error_converting') # Pass the whole item, capture updated

def main():
    while True:
        process_queue()
        time.sleep(60)  # Wait 1 minute before checking again

if __name__ == '__main__':
    main() 
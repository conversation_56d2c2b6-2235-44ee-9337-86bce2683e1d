import hashlib
import logging
from typing import List

logging.basicConfig(level=logging.INFO)

def compute_file_list_hash(file_list: List[str]) -> str:
    """Compute a hash for a list of file names to detect changes."""
    file_list_sorted = sorted(file_list)
    hash_obj = hashlib.sha256()
    for fname in file_list_sorted:
        hash_obj.update(fname.encode('utf-8'))
    digest = hash_obj.hexdigest()
    logging.debug(f"Computed hash for file list: {digest}")
    return digest

def next_queue_number(existing_numbers: List[int]) -> int:
    """Return the next available queue number."""
    if not existing_numbers:
        return 1
    next_num = max(existing_numbers) + 1
    logging.debug(f"Next queue number: {next_num}")
    return next_num 
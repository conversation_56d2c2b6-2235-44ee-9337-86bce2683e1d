VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: Tester VCU Fast_Charger Slow_Charger Tester_Tool BMS00


BO_ 1283 Battery_Parameter_1: 8 VCU
 SG_ Pack_SOC_00 : 16|16@1+ (0.01,0) [0|100] "%"  Fast_Charger
 SG_ Effective_Battery_Temperature_00 : 0|16@1+ (0.1,-50) [-50|78] "degC"  Fast_Charger

BO_ 1971 SOM_Diagnostic_Response: 8 VCU
 SG_ SOM_Diag_Reponse : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester

BO_ 1950 SOM_Diagnostic_Request: 8 Tester
 SG_ SOM_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1328 EV_Keep_Alive_Message: 8 VCU
 SG_ Vehicle_Alive : 0|1@1+ (1,0) [0|1] "-"  Fast_Charger

BO_ 1329 Fast_Charger_Keep_Alive_Message: 8 Fast_Charger
 SG_ Fast_Charger_Alive : 0|1@1+ (1,0) [0|1] "-"  VCU

BO_ 1330 EV_Unlatch_Request: 8 VCU
 SG_ Request_to_Unlatch : 0|1@1+ (1,0) [0|1] "-"  Fast_Charger

BO_ 1331 EV_VIN_Publish: 8 VCU
 SG_ Vehicle_VIN_number_Data_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Fast_Charger

BO_ 1332 Fast_Charger_Authentication_Request: 8 VCU
 SG_ Random_Number_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Fast_Charger

BO_ 1347 Fast_Charger_Authentication_Response: 8 Fast_Charger
 SG_ Message_Authentication_Code_MAC_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  VCU

BO_ 1333 VCU_Authentication_Request: 8 Fast_Charger
 SG_ Random_Number_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  VCU

BO_ 1363 VCU_Authentication_Response: 8 VCU
 SG_ Message_Authentication_Code_MAC_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Fast_Charger

BO_ 1334 EV_Signature_Request: 8 Fast_Charger
 SG_ Random_Number_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  VCU

BO_ 1379 EV_Signature_Response: 8 VCU
 SG_ Digital_Signature_Length_Over_ISOTP : 32|32@1+ (1,0) [0|4294967295] "-"  Fast_Charger
 SG_ Digital_Signature_of_EV_Over_ISOTP : 0|32@1+ (1,0) [0|4294967295] "-"  Fast_Charger

BO_ 1280 EV_Parameter_1: 8 VCU
 SG_ Energy_transfer_system_error : 0|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_overvoltage : 1|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_undervoltage : 2|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_current_deviation_error : 3|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ High_battery_temperature : 4|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_voltage_deviation_error : 5|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Reserved_3 : 6|2@1+ (1,0) [0|3] ""  Fast_Charger
 SG_ EV_charging_enabled : 8|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_contactor_status : 9|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_charging_position : 10|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_charging_stop_control : 11|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Wait_request_to_delay_energy_transfer : 12|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Digital_communication_toggle : 13|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Reserved_4 : 14|2@1+ (1,0) [0|3] ""  Fast_Charger
 SG_ requested_DC_output_current : 16|16@1+ (0.1,0) [0|200] "A"  Fast_Charger
 SG_ DC_output_voltage_target_parameter : 32|16@1+ (0.1,0) [0|120] "V"  Fast_Charger
 SG_ DC_output_voltage_limit_parameter : 48|16@1+ (0.1,0) [0|120] "V"  Fast_Charger

BO_ 1281 EV_Parameter_2: 8 VCU
 SG_ Control_protocol_number : 0|8@1+ (1,0) [0|254] ""  Fast_Charger
 SG_ Charging_rate : 8|8@1+ (1,0) [0|100] "b"  Fast_Charger
 SG_ Maximum_charging_time : 16|16@1+ (1,0) [0|65534] "min"  Fast_Charger
 SG_ Estimated_charging_time : 32|16@1+ (1,0) [0|65534] "min"  Fast_Charger
 SG_ Reserved_5 : 48|16@1+ (1,0) [0|65535] ""  Fast_Charger

BO_ 1282 EV_Voltage_Control: 8 VCU
 SG_ EV_Voltage_Control : 0|1@1+ (1,0) [0|1] ""  Fast_Charger

BO_ 1408 EV_Identification_Prm_1: 8 VCU
 SG_ EV_identification_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1409 EV_Identification_Prm_2: 8 VCU
 SG_ EV_identification_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1410 EV_Protocol_1: 8 VCU
 SG_ Protocol_identifier_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1411 EV_Protocol_2: 8 VCU
 SG_ Protocol_identifier_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1288 Fast_Charger_Parameter_1: 8 Fast_Charger
 SG_ Charging_system_error : 0|1@1+ (1,0) [0|1] ""  VCU
 SG_ EV_supply_equipment_malfunction : 1|1@1+ (1,0) [0|1] ""  VCU
 SG_ EV_incompatibility : 2|1@1+ (1,0) [0|1] ""  VCU
 SG_ Reserved_6 : 3|5@1+ (1,0) [0|31] ""  VCU
 SG_ EV_supply_equipment_stop_control : 8|1@1+ (1,0) [0|1] ""  VCU
 SG_ EV_supply_equipment_status : 9|1@1+ (1,0) [0|1] ""  VCU
 SG_ Vehicle_connector_latched : 10|1@1+ (1,0) [0|1] ""  VCU
 SG_ EV_supply_equipment_ready : 11|1@1+ (1,0) [0|1] ""  VCU
 SG_ Waiting_state_before_charging_start : 12|1@1+ (1,0) [0|1] ""  VCU
 SG_ EVSE_Emergency_Stop : 13|2@1+ (1,0) [0|1] ""  VCU
 SG_ rated_DC_output_voltage : 16|16@1+ (0.1,0) [0|120] "V"  VCU
 SG_ Available_DC_output_current : 32|16@1+ (0.1,0) [0|200] "A"  VCU
 SG_ Confirmed_DC_output_voltage_limit : 48|16@1+ (0.1,0) [0|120] "V"  VCU

BO_ 1289 Fast_Charger_Parameter_2: 8 Fast_Charger
 SG_ Control_protocol_number : 0|8@1+ (1,0) [0|254] ""  VCU
 SG_ Available_DC_output_power : 8|8@1+ (50,0) [0|12700] "W"  VCU
 SG_ Output_voltage : 16|16@1+ (0.1,0) [0|250] "V"  VCU
 SG_ DC_output_current : 32|16@1+ (0.1,0) [0|200] "A"  VCU
 SG_ Remaining_charging_time : 48|16@1+ (1,0) [0|65534] "min"  VCU

BO_ 1296 FC_Voltage_Control: 8 Fast_Charger
 SG_ Voltage_Control_Option : 0|1@1+ (1,0) [0|1] ""  VCU

BO_ 1412 EV_Identification_1: 8 Fast_Charger
 SG_ EVSE_identification_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1413 EV_Identification_2: 8 Fast_Charger
 SG_ EVSE_identification_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1414 Protocol_1: 8 Fast_Charger
 SG_ Protocol_identifier_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1415 Protocol_2: 8 Fast_Charger
 SG_ Protocol_identifier_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 64 MBMS_WakeUp_Reason: 8 VCU
 SG_ MBMS_Wake_Up_Reason : 0|3@1+ (1,0) [0|7] ""  Slow_Charger

BO_ 128 VCU_to_Slow_Charger_Authentication_Request: 8 VCU
 SG_ VCU_To_SC_Seed : 0|32@1+ (1,0) [0|4294967295] ""  Slow_Charger

BO_ 131 Slow_Charger_to_VCU_Authentication_Response: 8 Slow_Charger
 SG_ SC_Key : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 129 VCU_to_Slow_Charger_Authentication_Status: 8 VCU
 SG_ VCU_Auth_Response_to_SC : 0|1@1+ (1,0) [0|1] ""  Slow_Charger

BO_ 11 Slow_Charger_to_battery_wakeup: 8 Slow_Charger
 SG_ Battery_Wakeup_Request : 0|2@1+ (1,0) [0|3] ""  VCU
 SG_ Serial_Number : 8|32@1+ (1,0) [0|4294967295] ""  VCU
 SG_ Firmware_version : 40|4@1+ (1,0) [0|15] ""  VCU
 SG_ Hardware_version : 44|4@1+ (1,0) [0|15] ""  VCU
 SG_ Manufacturer_Code : 48|4@1+ (1,0) [0|15] ""  VCU
 SG_ Bootloader_version : 52|4@1+ (1,0) [0|15] ""  VCU

BO_ 336 BMS00_Parameter_3: 8 VCU
 SG_ Bus_Voltage_00 : 0|16@1+ (0.001,0) [0|65] "V"  Slow_Charger
 SG_ Delta_Voltage_00 : 16|16@1+ (0.001,0) [0|65] "V"  Slow_Charger
 SG_ Available_Capacity_00 : 32|8@1+ (1,0) [0|100] "%"  Slow_Charger
 SG_ Pack_SoH : 40|8@1+ (1,0) [0|100] "%"  Slow_Charger

BO_ 288 Overall_Battery_Status_Info: 8 VCU
 SG_ FC_contactor_Positive_supply_command : 51|1@1+ (1,0) [0|1] ""  Slow_Charger
 SG_ Overall_Battery_Current : 0|16@1+ (0.01,-300) [-300|300] "Amp"  Slow_Charger
 SG_ Overall_SOC : 16|16@1+ (0.01,0) [0|100] "%"  Slow_Charger
 SG_ Overall_Charge_Voltage_limit : 32|16@1+ (0.001,0) [0|65] "V"  Slow_Charger
 SG_ Charger_Mode_Request : 48|3@1+ (1,0) [0|7] ""  Slow_Charger

BO_ 531 Charger_Status: 8 Slow_Charger
 SG_ Boost_Charge_Fault_Information : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Charger_Status_Signal : 0|1@1+ (1,0) [0|1] ""  VCU
 SG_ Charger_Mode : 1|3@1+ (1,0) [0|7] ""  VCU
 SG_ Charging_Voltage_Available_range : 4|16@1+ (0.1,0) [0|120] "V"  VCU
 SG_ Charging_Current_Available_range : 20|16@1+ (0.1,0) [0|500] "Amp"  VCU
 SG_ Slow_Charger_Fault_Information : 36|2@1+ (1,0) [0|3] ""  VCU
 SG_ Charger_output_Short_circuit_error : 40|1@1+ (1,0) [0|1] ""  VCU
 SG_ Charger_under_temperature_error : 41|1@1+ (1,0) [0|1] ""  VCU
 SG_ Charger_over_temperature_error : 42|1@1+ (1,0) [0|1] ""  VCU
 SG_ Low_AC_voltage_error : 43|1@1+ (1,0) [0|1] ""  VCU
 SG_ Rectifier_hardware_error : 44|1@1+ (1,0) [0|1] ""  VCU
 SG_ Authentication_Error : 45|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Parameter_Time_out_error : 46|1@1+ (1,0) [0|1] ""  VCU
 SG_ Data_Corruption_error : 47|1@1+ (1,0) [0|1] ""  VCU
 SG_ Charge_control_message_timeout_error : 48|1@1+ (1,0) [0|1] ""  VCU

BO_ 289 Overall_Battery_Current_Limit: 8 VCU
 SG_ Overall_Charge_current_limit : 0|16@1+ (0.01,0) [0|600] "Amp"  Slow_Charger
 SG_ Overall_Discharge_current_limit : 16|16@1+ (0.01,-400) [-400|0] "Amp"  Slow_Charger
 SG_ Overall_Regen_Power_Limit : 32|16@1+ (1,0) [0|65535] "Watts"  Slow_Charger
 SG_ Overall_Discharge_Power_Limit : 48|16@1+ (1,-50000) [-50000|0] "Watts"  Slow_Charger

BO_ 1982 SC_Diagnostic_Request: 8 VCU
 SG_ Request_Type : 0|8@1+ (1,0) [0|255] ""  Slow_Charger
 SG_ Data : 8|56@1+ (1,0) [0|7.20575940379279E+016] ""  Slow_Charger

BO_ 1975 SC_Diagnostic_Response: 8 Slow_Charger
 SG_ Response_Type : 0|8@1+ (1,0) [0|255] ""  VCU
 SG_ Data : 8|56@1+ (1,0) [0|7.20575940379279E+016] ""  VCU

BO_ 1638 Future_Requirement_1: 8 Slow_Charger
 SG_ Reserved_1 : 0|8@1+ (1,0) [0|255] ""  VCU

BO_ 1639 Future_Requirement_2: 8 Slow_Charger
 SG_ Reserved_2 : 0|8@1+ (1,0) [0|255] ""  VCU



BA_DEF_ SG_  "GenSigInactiveValue" INT 0 100000;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","OnChangeAndIfActive","OnChangeAndIfActiveWithRepetition","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 100000000000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 50000;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 1000;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 999999;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","Cyclic and OnChange","Cyclic and OnWrite","Cyclic and IfActive","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed","NotUsed";
BA_DEF_ BO_  "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_  "NmMessage" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 50000;
BA_DEF_ BU_  "NmNode" ENUM  "No","Yes";
BA_DEF_ BU_  "NmStationAddress" INT 0 63;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_  "DBName" STRING ;
BA_DEF_  "NWMWakeupAllowed" ENUM  "No","Yes";
BA_DEF_  "BusType" STRING ;
BA_DEF_ SG_  "SystemSignalLongSymbol" STRING ;
BA_DEF_ BO_  "SystemMessageLongSymbol" STRING ;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "NmMessage" "No";
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "NmNode" "No";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "NWMWakeupAllowed" "No";
BA_DEF_DEF_  "BusType" "CAN";
BA_DEF_DEF_  "SystemSignalLongSymbol" "";
BA_DEF_DEF_  "SystemMessageLongSymbol" "";
BA_ "BusType" "CAN";
BA_ "DBName" "S1_CAN2_Charger";
BA_ "GenMsgCycleTime" BO_ 1283 100;
BA_ "GenMsgSendType" BO_ 1971 8;
BA_ "GenMsgSendType" BO_ 1950 8;
BA_ "GenMsgCycleTime" BO_ 1328 100;
BA_ "GenMsgCycleTime" BO_ 1329 100;
BA_ "GenMsgSendType" BO_ 1330 8;
BA_ "GenMsgCycleTime" BO_ 1331 100;
BA_ "GenMsgSendType" BO_ 1332 8;
BA_ "GenMsgSendType" BO_ 1347 8;
BA_ "GenMsgSendType" BO_ 1333 8;
BA_ "GenMsgSendType" BO_ 1363 8;
BA_ "GenMsgSendType" BO_ 1334 8;
BA_ "GenMsgSendType" BO_ 1379 8;
BA_ "GenMsgCycleTime" BO_ 1280 100;
BA_ "GenMsgCycleTime" BO_ 1281 100;
BA_ "GenMsgCycleTime" BO_ 1282 100;
BA_ "GenMsgCycleTime" BO_ 1408 100;
BA_ "GenMsgCycleTime" BO_ 1409 100;
BA_ "GenMsgCycleTime" BO_ 1410 100;
BA_ "GenMsgCycleTime" BO_ 1411 100;
BA_ "GenMsgCycleTime" BO_ 1288 100;
BA_ "GenMsgCycleTime" BO_ 1289 100;
BA_ "GenMsgCycleTime" BO_ 1296 100;
BA_ "GenMsgCycleTime" BO_ 1412 100;
BA_ "GenMsgCycleTime" BO_ 1413 100;
BA_ "GenMsgCycleTime" BO_ 1414 100;
BA_ "GenMsgCycleTime" BO_ 1415 100;
BA_ "GenMsgCycleTime" BO_ 64 100;
BA_ "GenMsgSendType" BO_ 128 8;
BA_ "GenMsgSendType" BO_ 131 8;
BA_ "GenMsgSendType" BO_ 129 8;
BA_ "GenMsgCycleTime" BO_ 11 100;
BA_ "GenMsgCycleTime" BO_ 336 100;
BA_ "GenMsgCycleTime" BO_ 288 100;
BA_ "GenMsgCycleTime" BO_ 531 100;
BA_ "GenMsgCycleTime" BO_ 289 100;
BA_ "GenMsgSendType" BO_ 1982 8;
BA_ "GenMsgSendType" BO_ 1975 8;
BA_ "GenMsgSendType" BO_ 1638 8;
BA_ "GenMsgSendType" BO_ 1639 8;
BA_ "GenSigStartValue" SG_ 1283 Effective_Battery_Temperature_00 500;
BA_ "GenSigSendType" SG_ 1971 SOM_Diag_Reponse 7;
BA_ "GenSigSendType" SG_ 1950 SOM_Diag_Request 7;
BA_ "GenSigStartValue" SG_ 1328 Vehicle_Alive 0;
BA_ "GenSigStartValue" SG_ 1329 Fast_Charger_Alive 0;
BA_ "GenSigSendType" SG_ 1330 Request_to_Unlatch 7;
BA_ "GenSigStartValue" SG_ 1330 Request_to_Unlatch 0;
BA_ "GenSigSendType" SG_ 1332 Random_Number_Over_ISOTP 7;
BA_ "GenSigSendType" SG_ 1347 Message_Authentication_Code_MAC_Over_ISOTP 7;
BA_ "GenSigSendType" SG_ 1333 Random_Number_Over_ISOTP 7;
BA_ "GenSigSendType" SG_ 1363 Message_Authentication_Code_MAC_Over_ISOTP 7;
BA_ "GenSigSendType" SG_ 1334 Random_Number_Over_ISOTP 7;
BA_ "GenSigSendType" SG_ 1379 Digital_Signature_Length_Over_ISOTP 7;
BA_ "GenSigSendType" SG_ 1379 Digital_Signature_of_EV_Over_ISOTP 7;
BA_ "GenSigStartValue" SG_ 1280 Energy_transfer_system_error 0;
BA_ "GenSigStartValue" SG_ 1280 Battery_overvoltage 0;
BA_ "GenSigStartValue" SG_ 1280 Battery_undervoltage 0;
BA_ "GenSigStartValue" SG_ 1280 Battery_current_deviation_error 0;
BA_ "GenSigStartValue" SG_ 1280 High_battery_temperature 0;
BA_ "GenSigStartValue" SG_ 1280 Battery_voltage_deviation_error 0;
BA_ "GenSigStartValue" SG_ 1280 EV_charging_enabled 0;
BA_ "GenSigStartValue" SG_ 1280 EV_contactor_status 1;
BA_ "GenSigStartValue" SG_ 1280 EV_charging_position 0;
BA_ "GenSigStartValue" SG_ 1280 EV_charging_stop_control 0;
BA_ "GenSigStartValue" SG_ 1280 Wait_request_to_delay_energy_transfer 0;
BA_ "GenSigStartValue" SG_ 1280 Digital_communication_toggle 0;
BA_ "GenSigStartValue" SG_ 1280 Reserved_4 0;
BA_ "GenSigStartValue" SG_ 1280 requested_DC_output_current 0;
BA_ "GenSigStartValue" SG_ 1280 DC_output_voltage_target_parameter 0;
BA_ "GenSigStartValue" SG_ 1280 DC_output_voltage_limit_parameter 0;
BA_ "GenSigStartValue" SG_ 1281 Charging_rate 255;
BA_ "GenSigStartValue" SG_ 1281 Maximum_charging_time 65535;
BA_ "GenSigStartValue" SG_ 1281 Estimated_charging_time 65535;
BA_ "GenSigStartValue" SG_ 1288 Charging_system_error 0;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_malfunction 0;
BA_ "GenSigStartValue" SG_ 1288 EV_incompatibility 0;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_stop_control 1;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_status 0;
BA_ "GenSigStartValue" SG_ 1288 Vehicle_connector_latched 0;
BA_ "GenSigStartValue" SG_ 64 MBMS_Wake_Up_Reason 0;
BA_ "GenSigSendType" SG_ 128 VCU_To_SC_Seed 7;
BA_ "GenSigStartValue" SG_ 128 VCU_To_SC_Seed 0;
BA_ "GenSigSendType" SG_ 131 SC_Key 7;
BA_ "GenSigStartValue" SG_ 131 SC_Key 0;
BA_ "GenSigSendType" SG_ 129 VCU_Auth_Response_to_SC 7;
BA_ "GenSigStartValue" SG_ 129 VCU_Auth_Response_to_SC 0;
BA_ "GenSigStartValue" SG_ 11 Battery_Wakeup_Request 0;
BA_ "GenSigStartValue" SG_ 11 Serial_Number 0;
BA_ "GenSigStartValue" SG_ 11 Firmware_version 0;
BA_ "GenSigStartValue" SG_ 11 Hardware_version 0;
BA_ "GenSigStartValue" SG_ 11 Manufacturer_Code 0;
BA_ "GenSigStartValue" SG_ 11 Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 336 Bus_Voltage_00 0;
BA_ "GenSigStartValue" SG_ 336 Delta_Voltage_00 0;
BA_ "GenSigStartValue" SG_ 336 Available_Capacity_00 0;
BA_ "GenSigStartValue" SG_ 336 Pack_SoH 0;
BA_ "GenSigStartValue" SG_ 288 Overall_Battery_Current 30000;
BA_ "GenSigStartValue" SG_ 288 Overall_SOC 0;
BA_ "GenSigStartValue" SG_ 288 Overall_Charge_Voltage_limit 0;
BA_ "GenSigStartValue" SG_ 288 Charger_Mode_Request 0;
BA_ "GenSigStartValue" SG_ 531 Charger_Status_Signal 0;
BA_ "GenSigStartValue" SG_ 531 Charger_Mode 0;
BA_ "GenSigStartValue" SG_ 531 Charging_Voltage_Available_range 0;
BA_ "GenSigStartValue" SG_ 531 Charging_Current_Available_range 0;
BA_ "GenSigStartValue" SG_ 531 Slow_Charger_Fault_Information 0;
BA_ "GenSigStartValue" SG_ 531 Charger_output_Short_circuit_error 0;
BA_ "GenSigStartValue" SG_ 531 Charger_under_temperature_error 0;
BA_ "GenSigStartValue" SG_ 531 Charger_over_temperature_error 0;
BA_ "GenSigStartValue" SG_ 531 Low_AC_voltage_error 0;
BA_ "GenSigStartValue" SG_ 531 Rectifier_hardware_error 0;
BA_ "GenSigStartValue" SG_ 531 Authentication_Error 0;
BA_ "GenSigStartValue" SG_ 531 Battery_Parameter_Time_out_error 0;
BA_ "GenSigStartValue" SG_ 531 Data_Corruption_error 0;
BA_ "GenSigStartValue" SG_ 531 Charge_control_message_timeout_error 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Charge_current_limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Discharge_current_limit 40000;
BA_ "GenSigStartValue" SG_ 289 Overall_Regen_Power_Limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Discharge_Power_Limit 50000;
BA_ "GenSigSendType" SG_ 1982 Request_Type 7;
BA_ "GenSigStartValue" SG_ 1982 Request_Type 0;
BA_ "GenSigSendType" SG_ 1982 Data 7;
BA_ "GenSigStartValue" SG_ 1982 Data 0;
BA_ "GenSigSendType" SG_ 1975 Response_Type 7;
BA_ "GenSigStartValue" SG_ 1975 Response_Type 0;
BA_ "GenSigSendType" SG_ 1975 Data 7;
BA_ "GenSigStartValue" SG_ 1975 Data 0;
BA_ "GenSigSendType" SG_ 1638 Reserved_1 7;
BA_ "GenSigStartValue" SG_ 1638 Reserved_1 0;
BA_ "GenSigSendType" SG_ 1639 Reserved_2 7;
BA_ "GenSigStartValue" SG_ 1639 Reserved_2 0;
VAL_ 1328 Vehicle_Alive 1 " EV Alive" 0 " SNA" ;
VAL_ 1329 Fast_Charger_Alive 1 " FC Alive" 0 " SNA" ;
VAL_ 1330 Request_to_Unlatch 1 " Request to Unlatch" 0 " SNA" ;
VAL_ 1280 Energy_transfer_system_error 1 " Error" 0 "Error Free" ;
VAL_ 1280 Battery_overvoltage 1 " Error" 0 "Error Free" ;
VAL_ 1280 Battery_undervoltage 1 " Error" 0 "Error Free" ;
VAL_ 1280 Battery_current_deviation_error 1 " Error" 0 "Error Free" ;
VAL_ 1280 High_battery_temperature 1 " Error" 0 "Error Free" ;
VAL_ 1280 Battery_voltage_deviation_error 1 " Error" 0 "Error Free" ;
VAL_ 1280 EV_charging_enabled 1 " Enabled" 0 "Disabled " ;
VAL_ 1280 EV_contactor_status 1 " EV contactor open before charging or welding detection finished " 0 "others" ;
VAL_ 1280 EV_charging_position 1 " Inappropriate Position" 0 "Appropriate Position" ;
VAL_ 1280 EV_charging_stop_control 1 " After Transmission" 0 "Before Transmission" ;
VAL_ 1280 Wait_request_to_delay_energy_transfer 1 " request to wait" 0 "No request" ;
VAL_ 1280 Digital_communication_toggle 1 " request to stop communication" 0 "Normal Condition during communication " ;
VAL_ 1280 DC_output_voltage_target_parameter 65535 "Default Value" ;
VAL_ 1280 DC_output_voltage_limit_parameter 65535 "Default Value" ;
VAL_ 1281 Control_protocol_number 0 " IEC61851" ;
VAL_ 1281 Charging_rate 255 "Default Value" ;
VAL_ 1281 Maximum_charging_time 65535 "Default Value" ;
VAL_ 1281 Estimated_charging_time 65535 "Default Value" ;
VAL_ 1282 EV_Voltage_Control 1 "Voltage Control Enabled" 0 "No Voltage control" ;
VAL_ 1288 Charging_system_error 1 " Error" 0 "Error Free" ;
VAL_ 1288 EV_supply_equipment_malfunction 1 " Error" 0 "Error Free" ;
VAL_ 1288 EV_incompatibility 1 " Incompatible" 0 "compatible" ;
VAL_ 1288 EV_supply_equipment_stop_control 1 " Shutdown or stop charging" 0 "Operating" ;
VAL_ 1288 EV_supply_equipment_status 1 " charging" 0 "Standby" ;
VAL_ 1288 Vehicle_connector_latched 1 " Latched" 0 "Unlatched" ;
VAL_ 1288 EV_supply_equipment_ready 1 "Ready" 0 "Not Ready" ;
VAL_ 1288 Waiting_state_before_charging_start 1 "Energy Transfer" 0 "Waiting" ;
VAL_ 1288 EVSE_Emergency_Stop 1 "True" 0 "False" ;
VAL_ 1296 Voltage_Control_Option 1 "Voltage control Enabled" 0 "No voltage control " ;
VAL_ 64 MBMS_Wake_Up_Reason 7 "EOL WakeUp" 6 "RTC Wakeup" 5 " SoC Push Button Wakeup " 4 " IMU Wakeup " 3 " Charger CAN Wakeup " 2 " Reserved" 1 " Ready to Sleep" 0 " SNA " ;
VAL_ 129 VCU_Auth_Response_to_SC 1 "Successful" 0 "Unsuccessful" ;
VAL_ 11 Battery_Wakeup_Request 1 " Wakeup" 0 " Sleep" ;
VAL_ 11 Firmware_version 1 " V1.4.5" 0 " Default" ;
VAL_ 11 Hardware_version 3 " PIC/GD for Destination Charger" 2 " PCS20001 with GD (GD32F103xx)" 1 " PCS20001 with PIC (PIC18F66K80)" 0 " Default" ;
VAL_ 11 Manufacturer_Code 2 " OLA" 1 " Steady Load" 0 " Default" ;
VAL_ 11 Bootloader_version 1 " V1.0" 0 " Default" ;
VAL_ 288 FC_contactor_Positive_supply_command 1 "ON" 0 "OFF" ;
VAL_ 288 Charger_Mode_Request 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 531 Charger_Status_Signal 1 "Ready" 0 "Not Ready" ;
VAL_ 531 Charger_Mode 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 531 Slow_Charger_Fault_Information 3 "Reserved" 2 "Reserved" 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_output_Short_circuit_error 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_under_temperature_error 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_over_temperature_error 1 "Error " 0 " No Error" ;
VAL_ 531 Low_AC_voltage_error 1 "Error " 0 " No Error" ;
VAL_ 531 Rectifier_hardware_error 1 "Error " 0 " No Error" ;
VAL_ 531 Authentication_Error 1 "Error " 0 " No Error" ;
VAL_ 531 Battery_Parameter_Time_out_error 1 "Error " 0 " No Error" ;
VAL_ 531 Data_Corruption_error 1 "Error " 0 " No Error" ;
VAL_ 531 Charge_control_message_timeout_error 1 "Error " 0 " No Error" ;


import os
import pickle # For OAuth token
import logging
from typing import List, Dict
from datetime import datetime

from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google_auth_oauthlib.flow import InstalledAppFlow # For OAuth flow
from google.auth.transport.requests import Request # For OAuth token refresh
from tenacity import retry, wait_exponential, stop_after_attempt, retry_if_exception_type

# Use the same scopes as drive.py if auth.json is shared, or define specifically for sheets.
# The drive.py scopes already include spreadsheets.
SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets',
    # 'https://www.googleapis.com/auth/drive' # Only if strictly needed by this module for some reason
]

# Define paths relative to this file, assuming standard project structure
# where this file is in src/queue_manager/interfaces/
# and drive credentials are in src/drive/
CWD = os.getcwd() # Get current working directory, assumes script is run from project root
TOKEN_PATH = os.path.join(CWD, 'src', 'drive', 'token.pickle')
AUTH_PATH = os.path.join(CWD, 'src', 'drive', 'auth.json') # This should be your OAuth client secrets file

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Environment variables for sheet details
SPREADSHEET_ID = os.environ.get('QUEUE_MANAGEMENT_SHEET_ID')
SHEET_NAME = os.environ.get('QUEUE_MANAGEMENT_DB_SHEET_NAME')

# Column Definitions (1-based for user display, use 0-based for list indexing if needed)
# Or use letters for ranges 'A', 'B', etc.
FILE_NAME_COL_LETTER = 'A'
BLF_LINK_COL_LETTER = 'B'  # Key for single item updates
CONVERTED_LINK_COL_LETTER = 'C'
QUEUE_NUM_COL_LETTER = 'D'
STATUS_COL_LETTER = 'E'
LAST_UPDATED_S_COL_LETTER = 'F' # New column for "Last Updated (Sheet)"

FIRST_DATA_ROW = 2 # Assuming header is in row 1

@retry(
    retry=retry_if_exception_type(HttpError), # Can add other retry-worthy exceptions like socket.timeout
    wait=wait_exponential(multiplier=1, min=4, max=60),
    stop=stop_after_attempt(5),
    reraise=True
)
def authenticate_sheets():
    """Authenticates with Google Sheets API using OAuth 2.0 flow (mirrors drive.py)."""
    if not SPREADSHEET_ID or not SHEET_NAME:
        logging.error("SPREADSHEET_ID or SHEET_NAME environment variables not set.")
        raise ValueError("Sheet ID or Sheet Name not configured in environment variables.")

    creds = None
    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH, 'rb') as token:
            creds = pickle.load(token)
    
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
            except Exception as e: # Catch potential errors during refresh
                logging.error(f"Error refreshing token: {e}. Will attempt full auth flow.")
                creds = None # Force re-authentication
        if not creds: # If still no creds (initial run or refresh failed)
            if not os.path.exists(AUTH_PATH):
                logging.error(f"OAuth client secrets file (auth.json) not found at {AUTH_PATH}")
                raise FileNotFoundError(f"OAuth client secrets file (auth.json) not found at {AUTH_PATH}")
            flow = InstalledAppFlow.from_client_secrets_file(AUTH_PATH, SCOPES)
            # Try to run local server, but have a fallback or clear instructions if headless
            try:
                creds = flow.run_local_server(port=0)
            except OSError as e: # Handle cases where port might be an issue or headless environment
                logging.error(f"Failed to run local server for OAuth: {e}. If running headless, pre-authorize and ensure token.pickle is present.")
                # For headless, consider `flow.run_console()` or pre-generating token.pickle
                raise
        
        with open(TOKEN_PATH, 'wb') as token:
            pickle.dump(creds, token)
            
    logging.info("Authenticated with Google Sheets API using OAuth 2.0 flow.")
    return build('sheets', 'v4', credentials=creds)

@retry(
    retry=retry_if_exception_type(HttpError),
    wait=wait_exponential(multiplier=1, min=4, max=60),
    stop=stop_after_attempt(5),
    reraise=True
)
def update_single_item_in_sheet(service, blf_link_key: str, new_row_data: List[str], sheet_id: str = None, sheet_name: str = None):
    """
    Updates a single row in the Google Sheet identified by blf_link_key.
    If the row is not found, it appends the new_row_data.
    new_row_data should be in the order: [drive_filename, blf_link, converted_link, queue_number, status, last_updated_timestamp_str]
    """
    current_sheet_id = sheet_id or SPREADSHEET_ID
    current_sheet_name = sheet_name or SHEET_NAME

    if not current_sheet_id or not current_sheet_name:
        logging.error("Sheet ID or Sheet Name not provided or found in environment variables for single item update.")
        return
    
    if not blf_link_key:
        logging.error("blf_link_key not provided for single item update.")
        return

    try:
        # Read the BLF Link column to find the row
        # Range for reading BLF_LINK_COL_LETTER (e.g., 'B2:B')
        blf_link_column_range = f"{current_sheet_name}!{BLF_LINK_COL_LETTER}{FIRST_DATA_ROW}:{BLF_LINK_COL_LETTER}"
        
        result = service.spreadsheets().values().get(
            spreadsheetId=current_sheet_id,
            range=blf_link_column_range
        ).execute()
        
        blf_links_in_sheet = result.get('values', [])
        row_number_to_update = -1

        for i, row_values in enumerate(blf_links_in_sheet):
            if row_values and row_values[0] == blf_link_key:
                row_number_to_update = i + FIRST_DATA_ROW  # 0-based index from result + FIRST_DATA_ROW
                break
        
        num_cols_to_write = len(new_row_data)
        if num_cols_to_write == 0:
            logging.warning(f"No data provided in new_row_data for blf_link_key: {blf_link_key}")
            return

        # Determine the end column letter based on the number of items in new_row_data
        # FILE_NAME_COL_LETTER is 'A'. If new_row_data has 6 items, end col is 'F'.
        end_col_letter = chr(ord(FILE_NAME_COL_LETTER) + num_cols_to_write - 1)

        if row_number_to_update != -1:
            # Row found, update it
            update_range = f"{current_sheet_name}!{FILE_NAME_COL_LETTER}{row_number_to_update}:{end_col_letter}{row_number_to_update}"
            body = {'values': [new_row_data]}
            service.spreadsheets().values().update(
                spreadsheetId=current_sheet_id,
                range=update_range,
                valueInputOption='USER_ENTERED', # Or 'RAW' if no parsing needed
                body=body
            ).execute()
            logging.info(f"Updated row {row_number_to_update} in sheet {current_sheet_name} for blf_link: {blf_link_key}.")
        else:
            # Row not found, append it
            logging.warning(f"Row not found for blf_link: {blf_link_key} in sheet {current_sheet_name}. Appending new row.")
            # Append range should cover all columns we intend to write, e.g., A:F
            append_range = f"{current_sheet_name}!{FILE_NAME_COL_LETTER}:{end_col_letter}"
            body = {'values': [new_row_data]}
            service.spreadsheets().values().append(
                spreadsheetId=current_sheet_id,
                range=append_range,
                valueInputOption='USER_ENTERED',
                insertDataOption='INSERT_ROWS', # Appends to the first empty row after the table
                body=body
            ).execute()
            logging.info(f"Appended new row for blf_link: {blf_link_key} to sheet {current_sheet_name}.")

    except HttpError as error:
        logging.error(f"An API error occurred during single item sheet update for blf_link {blf_link_key}: {error}")
        logging.error(f"Details: {error.resp.status}, {error._get_reason()}")
        raise
    except Exception as e:
        logging.error(f"An unexpected error occurred during single item sheet update for blf_link {blf_link_key}: {e}")
        raise
    # This was misplaced code from the previous service account attempt.
    # The actual authentication and service build happens in authenticate_sheets()
    # service = build('sheets', 'v4', credentials=creds)
    # logging.info("Google Sheets API authenticated successfully using service account.")
    # return service
    pass # This function body was incorrectly placed here. authenticate_sheets handles the return.


def update_sheet(service, data: List[List[str]], sheet_id: str = None, sheet_name: str = None):
    """
    Updates a Google Sheet by clearing existing content (from row FIRST_DATA_ROW downwards)
    and writing new data.
    Assumes the first row (row 1) is headers and should not be cleared.
    The 'data' parameter should include all columns, including the new 'Last Updated (Sheet)' column.
    """
    current_sheet_id = sheet_id or SPREADSHEET_ID
    current_sheet_name = sheet_name or SHEET_NAME

    if not current_sheet_id or not current_sheet_name:
        logging.error("Sheet ID or Sheet Name not provided or found in environment variables for full sheet update.")
        return

    try:
        # Determine the end column based on the new structure (A-F for 6 columns)
        # This assumes data[0] exists and reflects the number of columns to write.
        num_cols = len(data[0]) if data and data[0] else 6 # Default to 6 if no data or empty row
        end_col_letter_for_clear = chr(ord(FILE_NAME_COL_LETTER) + num_cols - 1) if num_cols > 0 else LAST_UPDATED_S_COL_LETTER

        # 1. Clear existing data (from FIRST_DATA_ROW downwards for all relevant columns)
        clear_range = f"{current_sheet_name}!{FILE_NAME_COL_LETTER}{FIRST_DATA_ROW}:{end_col_letter_for_clear}"
        service.spreadsheets().values().clear(
            spreadsheetId=current_sheet_id,
            range=clear_range,
            body={}
        ).execute()
        logging.info(f"Cleared data from sheet: {current_sheet_name} (range: {clear_range})")

        # 2. Write new data (from FIRST_DATA_ROW downwards)
        if data:
            body = {'values': data}
            # Data is written starting from FILE_NAME_COL_LETTER and FIRST_DATA_ROW.
            write_range = f"{current_sheet_name}!{FILE_NAME_COL_LETTER}{FIRST_DATA_ROW}"
            result = service.spreadsheets().values().update(
                spreadsheetId=current_sheet_id,
                range=write_range,
                valueInputOption='USER_ENTERED', # Use USER_ENTERED to allow Sheets to parse dates etc.
                body=body
            ).execute()
            logging.info(f"Successfully updated sheet: {current_sheet_name} with {len(data)} rows.")
            return result
        else:
            logging.info(f"No data provided to update sheet: {current_sheet_name}.")
            return None # Or an empty result
            
    except HttpError as error:
        logging.error(f"An API error occurred during full sheet update: {error}")
        logging.error(f"Details: {error.resp.status}, {error._get_reason()}")
        raise
    except Exception as e:
        logging.error(f"An unexpected error occurred during full sheet update: {e}")
        raise


def fetch_sheet_data(service, sheet_id: str = None, sheet_name: str = None) -> List[List[str]]:
    """Fetches data from the sheet, assuming data starts from FIRST_DATA_ROW and covers up to LAST_UPDATED_S_COL_LETTER."""
    current_sheet_id = sheet_id or SPREADSHEET_ID
    current_sheet_name = sheet_name or SHEET_NAME

    if not current_sheet_id or not current_sheet_name:
        logging.error("Sheet ID or Sheet Name not provided for fetching data.")
        return []

    # Range now includes the new "Last Updated (Sheet)" column
    range_to_fetch = f'{current_sheet_name}!{FILE_NAME_COL_LETTER}{FIRST_DATA_ROW}:{LAST_UPDATED_S_COL_LETTER}'
    try:
        result = service.spreadsheets().values().get(
            spreadsheetId=current_sheet_id,
            range=range_to_fetch
        ).execute()
        data = result.get('values', [])
        logging.info(f"Fetched {len(data)} rows from sheet {current_sheet_name} (range: {range_to_fetch}).")
        return data
    except HttpError as error:
        logging.error(f"An API error occurred while fetching sheet data: {error}")
        raise
    except Exception as e:
        logging.error(f"An unexpected error occurred while fetching sheet data: {e}")
        raise
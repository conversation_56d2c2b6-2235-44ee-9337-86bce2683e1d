# Plan: Batch Conversion Process Enhancements & Google Sheet Integration

Date: 2025-06-17

## Objective

Refine the batch BLF to CSV conversion process by:
1.  Ensuring the `converted` flag is set accurately only when the CSV file is created.
2.  Introducing more granular statuses: `pending`, `downloaded`, `converting`, `converted`, `uploading`, `uploaded`, `error_converting`, `error_uploading`.
3.  Updating these statuses and a "Last Updated (Sheet)" timestamp to a Google Sheet (`QUEUE_MANAGEMENT_DB_SHEET_NAME`) immediately upon relevant database changes, using `blf_link` as the unique row identifier.

## Overall Strategy

*   **Centralize Google Sheet Updates:** The `QueueManager` class in `src/queue_manager/core.py` will be responsible for triggering updates to the Google Sheet whenever an item's state is modified in the local SQLite database.
*   **Unique Sheet Row Identifier:** The `blf_link` (Google Drive File ID of the BLF file) will be used as the unique key to find and update rows in the 'BLF Link' column of the Google Sheet.
*   **Timestamping:** A new column, "Last Updated (Sheet)", will be added to the Google Sheet to record the time of the last update to that row.
*   **Comprehensive Updates:** Sheet updates will be triggered for all significant DB changes (add, download, status changes, errors).

## Phase 0: Google Sheet Preparation

1.  **Verify 'BLF Link' Column:**
    *   **Action:** Confirm that the 'BLF Link' column (likely Column B) in the Google Sheet specified by `QUEUE_MANAGEMENT_SHEET_ID` and `QUEUE_MANAGEMENT_DB_SHEET_NAME` consistently stores the unique Google Drive File ID of the original BLF file. This ID is crucial for row identification.
2.  **Add "Last Updated (Sheet)" Column:**
    *   **Action:** Manually add a new column header named "Last Updated (Sheet)" to your Google Sheet.
    *   **Position:** If current columns are A-E (`File Name`, `BLF Link`, `Converted Link`, `Queue #`, `Status`), this new column could be Column F. The implementation will assume this order.

## Phase 1: Core `QueueManager` and `interfaces/sheets.py` Modifications

### 1. Modify `src/queue_manager/interfaces/sheets.py`

*   **Adapt `update_single_item_in_sheet` function:**
    *   **Signature Change:** The function signature will be `def update_single_item_in_sheet(service, blf_link_key: str, new_row_data: list, sheet_id: str = None, sheet_name: str = None):`.
    *   **Row Matching Logic:** The function will find the row in the Google Sheet where the 'BLF Link' column's value (e.g., Column B) matches the provided `blf_link_key`.
    *   **Data Update Logic:** The `new_row_data` list passed to this function will be structured as: `[drive_filename, blf_link, converted_link, queue_number, status, last_updated_timestamp_str]`. If a matching row is found, its columns (A through F, or corresponding) will be updated. If no row is found, `new_row_data` will be appended as a new row.
    *   **Retry Logic:** The existing `@retry` decorator should be maintained for robustness.
*   **Modify `update_sheet` (full sync function):**
    *   This function (used by the periodic updater) must also accommodate the new "Last Updated (Sheet)" column. The `format_queue_for_sheet` function (in `sheet_updater.py`) will need adjustment to provide data for this new column during a full rewrite.

### 2. Modify `src/queue_manager/core.py` (`QueueManager`)

*   **Add Helper for Sheet Data Formatting:**
    *   Implement a private method, e.g., `_prepare_data_for_sheet(self, item_dict: Dict) -> List:`.
    *   This method takes an item dictionary (from a DB row) and formats it into the list structure expected by `update_single_item_in_sheet`.
    *   It will generate the current timestamp (e.g., using `datetime.now().isoformat()`) for the "Last Updated (Sheet)" field. The returned list should be in the order: `[drive_filename, blf_link, converted_link, queue_number, status, timestamp_str]`.
*   **Integrate Sheet Updates into DB Modifying Methods:**
    *   An authenticated `sheets_service` instance (from `interfaces.sheets.authenticate_sheets()`) will be needed within these methods or passed to them.
    *   **`add_file` method:** After successful SQLite insertion, fetch the new item's details (especially `blf_link`), prepare the sheet row data using `_prepare_data_for_sheet`, and call `update_single_item_in_sheet` with the `blf_link` and formatted data.
    *   **`update_status` method:** After successful SQLite update, fetch the updated item's details, prepare its sheet row data, and call `update_single_item_in_sheet` with the `blf_link` and formatted data.
    *   **`set_downloaded` method:** After successful SQLite update, fetch the updated item's details, prepare its sheet row data, and call `update_single_item_in_sheet` with the `blf_link` and formatted data.

## Phase 2: Runner Script Modifications & Conversion Verification

### 3. Modify `src/queue_manager/interfaces/convert.py`

*   **Enhance `run_conversion` function:**
    *   **Action:** After the `subprocess.run` call and its immediate logging, explicitly check for the output file's existence using `os.path.exists(output_path)`.
    *   **Logic:** If the file exists, return `True`. If not, log an error indicating the output file was not found despite script success, and return `False`. The existing `except subprocess.CalledProcessError` block should also ensure it returns `False`.
    *   **Rationale:** This makes the `converted` status contingent on the physical creation of the CSV file.

### 4. Modify `runners/batch_convert.py`

*   **Status Updates:** The existing logic for setting statuses (`converting`, `converted`, `error_converting`) by calling `queue.update_status(...)` will be maintained.
*   **Remove Direct Sheet Update Calls:** Any direct calls from this script to sheet update functions will be removed. The `QueueManager.update_status` method will now be responsible for triggering these updates.
*   **Conversion Success Check:** The `if success_conversion_script:` block (where `success_conversion_script` is the result of `run_conversion`) will now correctly reflect whether the file was created. Based on this, call `queue.update_status` with `'converted'` or `'error_converting'`.

### 5. Modify `runners/uploader.py`

*   **Status Updates:** The existing logic for setting statuses (`uploading`, `uploaded`, `error_uploading`) by calling `queue.update_status(...)` within `upload_csv_and_update_flag` will be maintained.
*   **Remove Direct Sheet Update Calls:** Any direct calls from this script to sheet update functions will be removed.
*   **Logic Flow:** Inside `upload_csv_and_update_flag`:
    1.  Call `queue.update_status(drive_filename, subfolder, 'uploading')`.
    2.  Attempt `upload_with_retry`.
    3.  On success, call `queue.update_status(drive_filename, subfolder, 'uploaded')`.
    4.  On failure, call `queue.update_status(drive_filename, subfolder, 'error_uploading')` in the exception handler.

## Phase 3: Review Periodic Sync and Final Touches

### 6. Review `src/queue_manager/sheet_updater.py` (Periodic Full Sync)

*   **Modify `format_queue_for_sheet` function:**
    *   This function prepares data for the full sheet rewrite. It must be updated to generate data for the new "Last Updated (Sheet)" column.
    *   The data list for each row should now include six items, matching the order: `[drive_filename, blf_link, converted_link, queue_number, queue_status, last_updated_sheet_timestamp_or_placeholder]`.
*   The `update_sheet` function in `interfaces/sheets.py` (which clears and rewrites all data rows) will now expect and write data for these six columns.

## Conceptual Workflow Diagram

```mermaid
graph TD
    A[Start: New File Info Added to System] --> B{DB: Add File Entry};
    B -- Success --> QM_AF[QueueManager.add_file Invoked];
    QM_AF -- Updates DB --> DB1[SQLite: status='pending', blf_link set];
    DB1 -- Triggers Internal Call --> SU_AF[Call update_single_item_in_sheet];
    SU_AF -- Uses blf_link --> GS1[Google Sheet: New Row Added/Updated with Timestamp];

    D[Downloader Process Runs] --> QM_SD[QueueManager.set_downloaded Invoked];
    QM_SD -- Updates DB --> DB2[SQLite: downloaded=1];
    DB2 -- Triggers Internal Call --> SU_SD[Call update_single_item_in_sheet];
    SU_SD -- Uses blf_link --> GS2[Google Sheet: Row Status & Timestamp Updated];

    BC[Batch Converter Process Runs for Downloaded File] --> QM_US_CVTNG[QueueManager.update_status('converting')];
    QM_US_CVTNG -- Updates DB --> DB3[SQLite: status='converting'];
    DB3 -- Triggers Internal Call --> SU_CVTNG[Call update_single_item_in_sheet];
    SU_CVTNG -- Uses blf_link --> GS3[Google Sheet: Row Status & Timestamp Updated];
    
    BC --> CVT[Conversion Script Execution];
    CVT -- Success & Output File Exists --> QM_US_CVTED[QueueManager.update_status('converted', converted_link=path)];
    QM_US_CVTED -- Updates DB --> DB4[SQLite: status='converted', converted=1, converted_link set];
    DB4 -- Triggers Internal Call --> SU_CVTED[Call update_single_item_in_sheet];
    SU_CVTED -- Uses blf_link --> GS4[Google Sheet: Row Status, Converted Link & Timestamp Updated];
    
    CVT -- Failure or Output File Missing --> QM_US_ERRCVT[QueueManager.update_status('error_converting')];
    QM_US_ERRCVT -- Updates DB --> DB5[SQLite: status='error_converting'];
    DB5 -- Triggers Internal Call --> SU_ERRCVT[Call update_single_item_in_sheet];
    SU_ERRCVT -- Uses blf_link --> GS5[Google Sheet: Row Status & Timestamp Updated];

    UP[Uploader Process Runs for Converted File] --> QM_US_UPLDNG[QueueManager.update_status('uploading')];
    QM_US_UPLDNG -- Updates DB --> DB6[SQLite: status='uploading'];
    DB6 -- Triggers Internal Call --> SU_UPLDNG[Call update_single_item_in_sheet];
    SU_UPLDNG -- Uses blf_link --> GS6[Google Sheet: Row Status & Timestamp Updated];

    UP --> UPLD_DRV[Attempt Upload to Google Drive];
    UPLD_DRV -- Success --> QM_US_UPLDED[QueueManager.update_status('uploaded')];
    QM_US_UPLDED -- Updates DB --> DB7[SQLite: status='uploaded', uploaded=1];
    DB7 -- Triggers Internal Call --> SU_UPLDED[Call update_single_item_in_sheet];
    SU_UPLDED -- Uses blf_link --> GS7[Google Sheet: Row Status & Timestamp Updated];

    UPLD_DRV -- Failure --> QM_US_ERRUPLD[QueueManager.update_status('error_uploading')];
    QM_US_ERRUPLD -- Updates DB --> DB8[SQLite: status='error_uploading'];
    DB8 -- Triggers Internal Call --> SU_ERRUPLD[Call update_single_item_in_sheet];
    SU_ERRUPLD -- Uses blf_link --> GS8[Google Sheet: Row Status & Timestamp Updated];

    subgraph Google Sheet Interaction
        direction LR
        GS1
        GS2
        GS3
        GS4
        GS5
        GS6
        GS7
        GS8
    end

    subgraph Local SQLite Database Interaction
        direction LR
        DB1
        DB2
        DB3
        DB4
        DB5
        DB6
        DB7
        DB8
    end
    
    style GS1 fill:#ccffcc,stroke:#333,stroke-width:1px
    style GS2 fill:#ccffcc,stroke:#333,stroke-width:1px
    style GS3 fill:#ccffcc,stroke:#333,stroke-width:1px
    style GS4 fill:#ccffcc,stroke:#333,stroke-width:1px
    style GS5 fill:#ffcccc,stroke:#333,stroke-width:1px
    style GS6 fill:#ccffcc,stroke:#333,stroke-width:1px
    style GS7 fill:#ccffcc,stroke:#333,stroke-width:1px
    style GS8 fill:#ffcccc,stroke:#333,stroke-width:1px
```

This plan centralizes Google Sheet updates within the `QueueManager`, uses `blf_link` for robust row identification, incorporates a "Last Updated (Sheet)" timestamp, and ensures sheet updates are triggered by all relevant database modifications.
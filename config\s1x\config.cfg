;CANoe Version |4|16|0|51764 config 
Version: 16.7.5 Build 5
32 PRO
5
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1.0.1
VGlobalConfiguration 1 Begin_Of_Object
18
VGlobalParameters 2 Begin_Of_Object
37
2
3,100,200,500
1000000 1.000000 0 1000 1 1 0 0 1 1 1 0 0 0 1 0 0 0
1
0
1 1
ResetSignalsOnMeasurementStart=1
VDatabaseContainerStreamer 3 Begin_Of_Object
9
1
<VFileName V9 QL> 1 "ESW_VEH_CAN_DBC_S1X_V02_Rev3_Rev3.78_Untruncated.dbc" 
ESW_VEH_CM_CAN_V01_S1X

1
0
1
000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001

1
1
0
0
0
0
End_Of_Object VDatabaseContainerStreamer 3
0
0
1
<VFileName V9 QL> 1 "config.cfg" 
0
0
0
0
VPersistentEnvarSelectionData 3 Begin_Of_Object
1
1 1 0 0
~
~
End_Of_Object VPersistentEnvarSelectionData 3
VPersistentExtensionData 3 Begin_Of_Object
3
VPersistentRelation 4 Begin_Of_Object
1
HookDLLActivations
1
1
End_Of_Object VPersistentRelation 4
End_Of_Object VPersistentExtensionData 3
VPersistentTreeStateInfo 3 Begin_Of_Object
1
Version
5
DialogBegin
1
75 75 605 580
SymbolExplorerDialogBegin
1
HistoryBegin
1 0
HistoryEnd
FiltersBegin
Begin
3 0 -1
0
SymbSelHeaderMgrBegin
1 6
0 1 200 0 0
1 1 100 0 0
2 0 100 0 0
3 0 75 1 1
5 1 80 0 0
6 1 200 0 0
15 1 80 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 -1
-1
SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
10 1 75 0 0
11 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 -1
-1
SymbSelHeaderMgrBegin
1 3
0 1 200 0 0
7 0 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End

FiltersEnd
0 0
SymbolExplorerDialogEnd

DialogEnd
End_Of_Object VPersistentTreeStateInfo 3
VPrintSettings 3 Begin_Of_Object
1
0 0 0 0 0
0 0 0 0 0 0 0 0 0 1
<VFileName V9 QL> 1 "" 
@@@@
Print page: {PAGE}    {DATE}  {TIME}
Licensename: {LICENSENAME}
Serial number: {LICENSENO}
@@@@
0

End_Of_Object VPrintSettings 3
<VFileName V9 QL> 1 "..\..\..\..\..\..\..\ProgramData\Vector\CANoe\15 (x64)\" 
1
VPortlinkConfigurationStreamer 3 Begin_Of_Object
1
1
0
0
0
END_OF_DRIVER
END_OF_PORT_CONFIGURATION_STREAM
End_Of_Object VPortlinkConfigurationStreamer 3
0
1
VWTP20ObsParameters-1:0:0:
FlexRayTP2ObsParameters: 2 0x2 36 VFrTPParams 2 0 1 0 1 1 0 0 8 255 0 :
FlexRayTP2ObsParametersEnd
VDoIPObserverParams 3 Begin_Of_Object
2
0
0
End_Of_Object VDoIPObserverParams 3
VISOTPParameters-1:active:onlyknown:interleave:stmin:seqnr:unexpected:sender:extended:baseaddr=1024:rxmask=255:storedata:maxlen=65536:pad=54
EOO
DiagnosticsSettingsV1.1
EOO
DiagnosticsSettingsV2.0
EOO
0
0
<VMultibusFilterDialogSettings> V2 275
<VWidthInfoContainer> 6
<VWidthInfo> V2 20 1 -1 36 
<VWidthInfo> V2 1 1 -1 47 
<VWidthInfo> V2 0 1 -1 40 
<VWidthInfo> V2 2 1 -1 47 
<VWidthInfo> V2 4 1 -1 33 
<VWidthInfo> V2 5 3 0 24 1 24 2 24 
<VBusWidthInfoSet> 6
1 0 <VWidthInfoContainer> 1
<VWidthInfo> V2 6 2 0 36 1 36 
1 1 <VWidthInfoContainer> 9
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 9 1 -1 30 
<VWidthInfo> V2 7 1 -1 45 
<VWidthInfo> V2 6 2 0 24 1 32 
<VWidthInfo> V2 32 1 -1 15 
<VWidthInfo> V2 35 1 -1 45 
<VWidthInfo> V2 37 9 0 60 1 60 2 60 3 60 4 60 5 60 6 60 7 60 8 60 
<VWidthInfo> V2 36 1 -1 45 
<VWidthInfo> V2 38 9 0 60 1 60 2 60 3 60 4 60 5 60 6 60 7 60 8 60 
1 2 <VWidthInfoContainer> 4
<VWidthInfo> V2 19 1 -1 45 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 22 1 -1 30 
<VWidthInfo> V2 6 2 0 24 1 32 
6 0 <VWidthInfoContainer> 7
<VWidthInfo> V2 9 1 -1 33 
<VWidthInfo> V2 7 1 -1 47 
<VWidthInfo> V2 15 1 -1 35 
<VWidthInfo> V2 27 2 0 24 1 24 
<VWidthInfo> V2 12 1 -1 37 
<VWidthInfo> V2 13 1 -1 37 
<VWidthInfo> V2 14 1 -1 40 
7 0 <VWidthInfoContainer> 2
<VWidthInfo> V2 23 1 -1 47 
<VWidthInfo> V2 29 1 -1 47 
9 0 <VWidthInfoContainer> 1
<VWidthInfo> V2 41 1 -1 47 
EndOf <VMultibusFilterDialogSettings>
VGlobalActionsStreamer 3 Begin_Of_Object
3
3
0
End_Of_Object VGlobalActionsStreamer 3
VEventSortingConfigStreamer 3 Begin_Of_Object
1
0
0
0
0
0
1
0
End_Of_Object VEventSortingConfigStreamer 3
FlexRayOptionParameters: 2 0 1 0 1 1 :0 :
FlexRayOptionParametersEnd
VCaplOptionsStreamer 3 Begin_Of_Object
2
17
1448
0
2001
1
2002
0
2005
0
2008
1
2013
1
2020
1
2032
1
2038
1
2039
0
2040
1
2041
1
2054
0
2055
1
2065
0
2135
1
2201
0
1
512
End_Of_Object VCaplOptionsStreamer 3
VSVConfigurationStreamer 3 Begin_Of_Object
1
73
﻿<?xml version="1.0" encoding="utf-8"?>
<systemvariables version="4" />
2
0
End_Of_Object VSVConfigurationStreamer 3
VOfflineBusStatisticSettings 3 Begin_Of_Object
1
1
1
1 1
1 500000
1 2
1 500000
0 3
1 0
0 4
1 0
0 5
1 0
0 6
1 0
0 7
1 0
0 8
1 0
0 9
1 0
0 10
1 0
0 11
1 0
0 12
1 0
0 13
1 0
0 14
1 0
0 15
1 0
0 16
1 0
0 17
1 0
0 18
1 0
0 19
1 0
0 20
1 0
0 21
1 0
0 22
1 0
0 23
1 0
0 24
1 0
0 25
1 0
0 26
1 0
0 27
1 0
0 28
1 0
0 29
1 0
0 30
1 0
0 31
1 0
0 32
1 0
0 33
1 0
0 34
1 0
0 35
1 0
0 36
1 0
0 37
1 0
0 38
1 0
0 39
1 0
0 40
1 0
0 41
1 0
0 42
1 0
0 43
1 0
0 44
1 0
0 45
1 0
0 46
1 0
0 47
1 0
0 48
1 0
0 49
1 0
0 50
1 0
0 51
1 0
0 52
1 0
0 53
1 0
0 54
1 0
0 55
1 0
0 56
1 0
0 57
1 0
0 58
1 0
0 59
1 0
0 60
1 0
0 61
1 0
0 62
1 0
0 63
1 0
0 64
1 0
End_Of_Object VOfflineBusStatisticSettings 3
VNETOptionsStreamer 3 Begin_Of_Object
5
0
<VFileName V9 QL> 0 "" 
1

0
1
End_Of_Object VNETOptionsStreamer 3
0
1
VUserFileMgrAnlyz 3 Begin_Of_Object
2
0
0
End_Of_Object VUserFileMgrAnlyz 3
VBasicDiagnosticStreamer 3 Begin_Of_Object
1
0
End_Of_Object VBasicDiagnosticStreamer 3
VCLibraryOptions 3 Begin_Of_Object
1
0
End_Of_Object VCLibraryOptions 3
NValueObjectDisplay::VNameDisplaySettings 3 Begin_Of_Object
3
13
0
4
1
4
2
4
3
4
4
1
5
1
6
4
7
4
8
4
9
6
10
4
11
4
12
4
0
13
0
0
1
0
2
0
3
0
4
0
5
0
6
0
7
0
8
0
9
0
10
0
11
0
12
0
13
0
1
1
3
2
3
3
3
4
1
5
1
6
1
7
1
8
1
9
1
10
1
11
1
12
3
13
0
511
1
511
2
511
3
511
4
511
5
511
6
511
7
511
8
511
9
128
10
511
11
511
12
511
0
End_Of_Object NValueObjectDisplay::VNameDisplaySettings 3
ConfigurationSavedByCANwBeginner 0
VGlobalExportAndLoggingSettings 3 Begin_Of_Object
9
2
1
0
0
6
1
0.10000000000000001
2
0
0.10000000000000001
2
19
0
1
3
1
0
::
,
.
<VFileName V9 QL> 1 "" 
410
0
0
1
6
0
6
0
0
0.20000000000000001
2
0
3
6
730
0
0.10000000000000001
0
0
1
VLoggingComment 4 Begin_Of_Object
1
1
VLoggingCommentAttribute 5 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 5
End_Of_Object VLoggingComment 4
End_Of_Object VGlobalExportAndLoggingSettings 3
0
VRTFilterOptions 3 Begin_Of_Object
3
0
0
0
0
0
End_Of_Object VRTFilterOptions 3
VRTTxBufferOptions 3 Begin_Of_Object
2
1
1
500
End_Of_Object VRTTxBufferOptions 3
VRTIRQReductionOptions 3 Begin_Of_Object
2
500
End_Of_Object VRTIRQReductionOptions 3
VPersistentDebuggerOptions 3 Begin_Of_Object
2
64
10000
End_Of_Object VPersistentDebuggerOptions 3
7
0
0
0
0
0
0
0
0
1
VAFDXGlobalSettings 3 Begin_Of_Object
4
1000
15
1
0
0
0
0
End_Of_Object VAFDXGlobalSettings 3
VRTCANErrorFrameOptions 3 Begin_Of_Object
1
1
1
End_Of_Object VRTCANErrorFrameOptions 3
1
ILConfiguration::VProxyManager 3 Begin_Of_Object
1
0
0
End_Of_Object ILConfiguration::VProxyManager 3
4
VGlobalVariableSettings 3 Begin_Of_Object
1
65001
End_Of_Object VGlobalVariableSettings 3
VGeneralLoggingBlockSettings 3 Begin_Of_Object
2
2000
1
0
3
End_Of_Object VGeneralLoggingBlockSettings 3
0
0
1
0
1
SecurityManager::VSecurityManager 3 Begin_Of_Object
4
0
0
NULL
0
End_Of_Object SecurityManager::VSecurityManager 3
VRTAutosarPDULayerMode 3 Begin_Of_Object
1
2
End_Of_Object VRTAutosarPDULayerMode 3
VErtOptions 3 Begin_Of_Object
2
0
0
End_Of_Object VErtOptions 3
0
0
471
VDistributedDebuggingSettings 3 Begin_Of_Object
1
0
2828
End_Of_Object VDistributedDebuggingSettings 3
VAdditionalLicenseOptions 3 Begin_Of_Object
1
0
End_Of_Object VAdditionalLicenseOptions 3
10
End_Of_Object VGlobalParameters 2
VDesktopManager 2 Begin_Of_Object
1
0
3
VDesktop 3 Begin_Of_Object
1
Trace
{71B85B8D-BF5C-4BCB-8EF4-173C03F46E59}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Configuration
{D67C646E-DD40-4D72-8040-F3F7BA5C21CD}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Analysis
{C05F2D99-D049-4E7C-8914-801141EB9ABD}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
4294967295
4294967295
4294967295
End_Of_Object VDesktopManager 2
0
VGBAnlyzBox 2 Begin_Of_Object
3
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 3 0 1 -1 -1 -1 -1 0 0 958 561

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 958 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 958 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 958 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 958 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{7FC0628B-2C8F-4993-BEBD-319CADDF6F3C}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
4
1
187
VDAOGBFunctionBlock 4 Begin_Of_Object
1
1
0
TABPredecessor:
0
TABSuccessor:
94
VOfflineSrcConfiguration 5 Begin_Of_Object
3
VMigratedGenericConfiguration<struct_VOfflineCfgData> 6 Begin_Of_Object
1
VOfflineCfgData 7 Begin_Of_Object
2
VReplayCfgBase 8 Begin_Of_Object
1
0
1
End_Of_Object VReplayCfgBase 8
VCfgBreakCondition 8 Begin_Of_Object
1
VDataBreakCondition 9 Begin_Of_Object
1
0
VEvCondBlock 10 Begin_Of_Object
1
VEvCondGroup 11 Begin_Of_Object
2
VEvCondPrimitive 12 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 12
1
0
0
End_Of_Object VEvCondGroup 11
End_Of_Object VEvCondBlock 10
End_Of_Object VDataBreakCondition 9
End_Of_Object VCfgBreakCondition 8
0
0
0
0
0
0
End_Of_Object VOfflineCfgData 7
End_Of_Object VMigratedGenericConfiguration<struct_VOfflineCfgData> 6
VChannelMapping 6 Begin_Of_Object
1
0
End_Of_Object VChannelMapping 6
End_Of_Object VOfflineSrcConfiguration 5
VDAOSwitch 5 Begin_Of_Object
1
94
0
TABPredecessor:
1
TABSuccessor:
95
VDAOGBHSStd 6 Begin_Of_Object
1
95
0
0 0
TABPredecessor:
94
TABSuccessor:
97
VDODynamicLine 7 Begin_Of_Object
1
96
0
0
VDOFRamification 8 Begin_Of_Object
1
97
0
TABPredecessor:
95
TABSuccessor:
99
13
VDORefinement 9 Begin_Of_Object
1
98
0
13
VDAOGBHSStd 10 Begin_Of_Object
1
99
0
0 0
TABPredecessor:
97
TABSuccessor:
101
VDODynamicLine 11 Begin_Of_Object
1
100
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
101
0
TABPredecessor:
99
TABSuccessor:
104
VNetMSControlConfiguration 13 Begin_Of_Object
1
VNETControlConfiguration 14 Begin_Of_Object
1
VConfigurationRoot 15 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 15
VNETControlBox 15 Begin_Of_Object
2
VUniqueBox 16 Begin_Of_Object
1
VBoxRoot 17 Begin_Of_Object
1
2
1 -1 0 9 0 0 0 0 40 40 540 440
CAN Statistics
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{603385A3-1317-44CD-8D50-D6E97299D589}
0
End_Of_Object VBoxRoot 17
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 16
0
1
End_Of_Object VNETControlBox 15
219
APPDIR Vector.CANalyzer.CAN.StatisticsMonitor.DLL
Vector.CANalyzer.CAN.StatisticsMonitor, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.StatisticsMonitor.StatisticsMonitor
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
App
2
UInt16
Channel
1
Array
States
3

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.UInt64
3
1
0
19
UInt64
ArrayElement
65536
UInt64
ArrayElement
65550
UInt64
ArrayElement
196623
UInt64
ArrayElement
65546
UInt64
ArrayElement
65537
UInt64
ArrayElement
65552
UInt64
ArrayElement
65540
UInt64
ArrayElement
65542
UInt64
ArrayElement
65549
UInt64
ArrayElement
65543
UInt64
ArrayElement
65538
UInt64
ArrayElement
65539
UInt64
ArrayElement
18
UInt64
ArrayElement
17
UInt64
ArrayElement
65545
UInt64
ArrayElement
65541
UInt64
ArrayElement
65548
UInt64
ArrayElement
65544
UInt64
ArrayElement
65547
UInt64
ArrayElement
65553
Array
ColumnWidths
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Int32
4
1
0
32
Int32
ArrayElement
160
Int32
ArrayElement
80
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
UInt64
TimerInterval
10
UInt64
TimerIntervalMs
1050
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
4
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETControlConfiguration 14
End_Of_Object VNetMSControlConfiguration 13
VDOLine 13 Begin_Of_Object
1
102
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
103
0
7
VDAOGBHSStd 10 Begin_Of_Object
1
104
0
0 0
TABPredecessor:
101
TABSuccessor:
106
VDODynamicLine 11 Begin_Of_Object
1
105
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
106
0
TABPredecessor:
104
TABSuccessor:
109
VTraceConfiguration 13 Begin_Of_Object
1
VTraceControlCfg 14 Begin_Of_Object
9
VTraceSearchCfg 15 Begin_Of_Object
1
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
End_Of_Object VTraceSearchCfg 15
VTraceFilterCfg 15 Begin_Of_Object
2
0
1
VTraceAnalysisFilterGroup 16 Begin_Of_Object
2
1
Filter Group 0
2
VTraceAnalysisSingleFilter 17 Begin_Of_Object
4
1
1
0
End_Of_Object VTraceAnalysisSingleFilter 17
VTraceAnalysisSingleFilter 17 Begin_Of_Object
4
0
0
0
End_Of_Object VTraceAnalysisSingleFilter 17
1
End_Of_Object VTraceAnalysisFilterGroup 16
VTraceSequenceFilter 16 Begin_Of_Object
1
1
1
1
VTraceAnalysisSingleFilter 17 Begin_Of_Object
4
0
0
0
End_Of_Object VTraceAnalysisSingleFilter 17
End_Of_Object VTraceSequenceFilter 16
End_Of_Object VTraceFilterCfg 15
1
0
0
0
41
0
0
1
1
14
ver=5: FT FT FT FT FT FT
End_Of_Serialized_Data 14
2
0
3
0
4
0
5
0
6
1
14
GFver=5;ver=5: FF TF TF FF FF FF FF FF FF TF;T F _Statistics;F T CANoe;F T _Security
End_Of_Serialized_Data 14
7
0
8
0
9
0
10
0
11
1
14
ver=5: FT FT FT FT
End_Of_Serialized_Data 14
12
0
13
0
14
0
15
0
16
0
17
0
18
0
19
0
20
0
21
0
22
0
23
0
24
0
25
0
26
0
27
0
28
0
29
0
30
0
31
0
32
0
33
0
34
0
35
0
36
0
37
0
38
0
39
0
40
0
0
1
VTraceColumnConfiguration 15 Begin_Of_Object
5
3
Initial
151
VTNColumnData 16 Begin_Of_Object
4
0
104
0
Time
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
1
37
1
Chn
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
2
45
2
ID
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
3
120
3
Name
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
4
170
-1
ID / Name
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
5
43
5
Dir
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
6
35
6
DLC
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
7
270
8
Data
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
8
37
-1
Attr
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
9
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
10
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
11
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
12
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
13
100
-1
Frame Duration
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
14
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
15
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
16
100
4
Event Type
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
17
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
18
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
19
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
20
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
21
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
22
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
23
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
24
100
-1
Bus Idle
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
25
100
-1
Bus Busy
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
26
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
27
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
28
110
-1
d:hh:mm:ss
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
29
85
-1
Diff Time
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
30
50
-1
Bustype
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
31
120
-1
Sender Node
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
32
50
-1
Bus
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
33
80
-1
Database
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
34
50
-1
Counter
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
35
100
-1
Start of Frame
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
36
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
37
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
38
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
39
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
40
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
41
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
42
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
43
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
44
50
-1
Arb. Field ID
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
45
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
46
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
47
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
48
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
49
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
50
150
-1
Data ASCII
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
51
100
-1
Comment
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
52
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
53
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
54
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
55
120
-1
Diff Time (Ref. Event)
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
56
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
57
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
58
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
59
120
-1
Date and Time
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
60
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
61
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
62
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
63
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
64
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
65
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
66
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
67
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
68
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
69
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
70
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
71
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
72
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
73
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
74
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
75
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
76
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
77
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
78
35
7
Data length
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
79
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
80
80
-1
XCP/CCP Command
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
81
80
-1
XCP/CCP Parameter
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
82
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
83
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
84
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
85
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
86
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
87
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
88
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
89
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
90
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
91
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
92
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
93
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
94
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
95
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
96
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
97
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
98
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
99
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
100
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
101
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
102
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
103
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
104
120
-1
Diff Time (last Occ.)
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
105
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
106
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
107
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
108
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
109
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
110
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
111
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
112
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
113
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
114
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
115
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
116
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
117
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
118
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
119
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
120
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
121
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
122
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
123
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
124
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
125
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
126
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
127
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
128
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
129
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
130
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
131
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
132
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
133
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
134
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
135
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
136
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
137
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
138
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
139
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
140
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
141
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
142
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
143
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
144
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
145
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
146
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
147
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
148
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
149
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
4
150
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 16
End_Of_Object VTraceColumnConfiguration 15
0
0
VTraceControlFixedModeExpansionItems 15 Begin_Of_Object
3
0
End_Of_Object VTraceControlFixedModeExpansionItems 15
14
C:\Users\<USER>\Documents\CODES\TEST\python_api_vector_canoe\input\config
End_Of_Serialized_Data 14
14
%s
End_Of_Serialized_Data 14
14

End_Of_Serialized_Data 14
0
2
1
1
14
VLogExportPersister 15 Begin_Of_Object
7
1416
11062253
%s
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 15

End_Of_Serialized_Data 14
1
0
0
290
0
150
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config" 
3
End_Of_Object VTraceControlCfg 14
VNETTraceControlBox 14 Begin_Of_Object
1
VNETControlBox 15 Begin_Of_Object
2
VUniqueBox 16 Begin_Of_Object
1
VBoxRoot 17 Begin_Of_Object
1
2
1 0 0 1 -1 -1 -1 -1 0 0 1916 561
Trace
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 1916 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{F288A8AE-C479-4AAA-9EF6-4C224D0CC5AF}
0
End_Of_Object VBoxRoot 17
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 16
0
0
End_Of_Object VNETControlBox 15
End_Of_Object VNETTraceControlBox 14
End_Of_Object VTraceConfiguration 13
VDOLine 13 Begin_Of_Object
1
107
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
108
0
4
VDAOGBHSStd 10 Begin_Of_Object
1
109
0
0 0
TABPredecessor:
106
TABSuccessor:
111
VDODynamicLine 11 Begin_Of_Object
1
110
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
111
0
TABPredecessor:
109
TABSuccessor:
114
VNETDataListControlHost 13 Begin_Of_Object
2
VConfigurationRoot 14 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 14
VNETDataBox 14 Begin_Of_Object
1
VNETControlBox 15 Begin_Of_Object
2
VUniqueBox 16 Begin_Of_Object
1
VBoxRoot 17 Begin_Of_Object
1
2
1 -1 0 1 -1 -1 -1 -1 958 0 1916 422
Data
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 958 0 1916 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 958 0 1916 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{DA5F5D31-FB24-4816-9DCF-6B48E25317AB}
0
End_Of_Object VBoxRoot 17
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 16
0
0
End_Of_Object VNETControlBox 15
End_Of_Object VNETDataBox 14
1
9
0
20 150 16 100 75 75 50 100 100 100 1
35 35
30
70 70 70 100
100
1 1 0 1 0 0 1 1 1 0 1
0 0
0
0 0 0 0
0
1 0
5000 0 10000 0 10000
1 0
VLogCfgData 14 Begin_Of_Object
14
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
1
1
1
1
0
2
0
0
14
VLogExportPersister 15 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 15

End_Of_Serialized_Data 14
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Data.mdf" 
0
1
0
30
80
410
1
1
0


<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\.mdf" 
1
1
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\{LoggingBlock}.mdf" 
0
7d855f57-c579-4e84-8300-668b5e69c08a
1
VLoggingComment 15 Begin_Of_Object
1
1
VLoggingCommentAttribute 16 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 16
End_Of_Object VLoggingComment 15
3
0
0
End_Of_Object VLogCfgData 14
1
[End_of_Control]
290
APPDIR Vector.CANalyzer.Data.DLL
Vector.CANalyzer.Data, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Data.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Int32
SelectedTabIndex
0
APPDIR Vector.CANalyzer.Data.DLL
Vector.CANalyzer.Data, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Data.TreeListColumnSerializationInfo
3
Column_0
3
TypeRef:3
Column_1
4
TypeRef:3
Column_2
5
TypeRef:3
Column_3
6
TypeRef:3
Column_4
7
TypeRef:3
Column_5
8
TypeRef:3
Column_6
9
TypeRef:3
Column_7
10
TypeRef:3
Column_8
11
TypeRef:3
Column_9
12
TypeRef:3
Column_10
13
TypeRef:3
Column_11
14
TypeRef:3
Column_12
15
Int32
ColumnsNum
13
Int32
NumConfiguredItems
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
16
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Int32
columnID
0
Int32
columnIndex
0
Boolean
columnIsVisible
True
Double
columnWidth
100
TypeRef:4
SerializationVersion
17
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:3
4
Int32
columnID
1
Int32
columnIndex
1
Boolean
columnIsVisible
True
Double
columnWidth
30
--TextFormatter: End of Object--
TypeRef:3
5
Int32
columnID
2
Int32
columnIndex
2
Boolean
columnIsVisible
False
Double
columnWidth
80
--TextFormatter: End of Object--
TypeRef:3
6
Int32
columnID
3
Int32
columnIndex
3
Boolean
columnIsVisible
False
Double
columnWidth
80
--TextFormatter: End of Object--
TypeRef:3
7
Int32
columnID
4
Int32
columnIndex
4
Boolean
columnIsVisible
False
Double
columnWidth
80
--TextFormatter: End of Object--
TypeRef:3
8
Int32
columnID
5
Int32
columnIndex
5
Boolean
columnIsVisible
True
Double
columnWidth
65
--TextFormatter: End of Object--
TypeRef:3
9
Int32
columnID
6
Int32
columnIndex
6
Boolean
columnIsVisible
False
Double
columnWidth
80
--TextFormatter: End of Object--
TypeRef:3
10
Int32
columnID
7
Int32
columnIndex
7
Boolean
columnIsVisible
False
Double
columnWidth
80
--TextFormatter: End of Object--
TypeRef:3
11
Int32
columnID
8
Int32
columnIndex
8
Boolean
columnIsVisible
True
Double
columnWidth
50
--TextFormatter: End of Object--
TypeRef:3
12
Int32
columnID
9
Int32
columnIndex
9
Boolean
columnIsVisible
True
Double
columnWidth
65
--TextFormatter: End of Object--
TypeRef:3
13
Int32
columnID
10
Int32
columnIndex
10
Boolean
columnIsVisible
False
Double
columnWidth
105
--TextFormatter: End of Object--
TypeRef:3
14
Int32
columnID
11
Int32
columnIndex
11
Boolean
columnIsVisible
True
Double
columnWidth
65
--TextFormatter: End of Object--
TypeRef:3
15
Int32
columnID
12
Int32
columnIndex
12
Boolean
columnIsVisible
False
Double
columnWidth
80
--TextFormatter: End of Object--
End_Of_Object VNETDataListControlHost 13
VDOLine 13 Begin_Of_Object
1
112
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
113
0
5
VDAOGBHSStd 10 Begin_Of_Object
1
114
0
0 0
TABPredecessor:
111
TABSuccessor:
116
VDODynamicLine 11 Begin_Of_Object
1
115
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
116
0
TABPredecessor:
114
TABSuccessor:
119
VGraphBoxConf 13 Begin_Of_Object
1
VNETGraphBox 14 Begin_Of_Object
1
VNETControlBox 15 Begin_Of_Object
2
VUniqueBox 16 Begin_Of_Object
1
VBoxRoot 17 Begin_Of_Object
1
2
1 -1 0 1 -1 -1 -1 -1 0 422 1916 844
Graphics
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 422 1916 844
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 422 1916 844
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{C9A9EFED-1869-41AA-91E2-FF0156EC0989}
0
End_Of_Object VBoxRoot 17
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 16
0
0
End_Of_Object VNETControlBox 15
End_Of_Object VNETGraphBox 14
81
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
Expanded
True
Int32
SplitterWidth
184
Int32
SplitterHeight
80
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.Position
3
LegendPosition
3
Int32
value__
0
--TextFormatter: End of Object--
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.GraphicCommandID
4
ScrollSignalsButton1Command
4
Int32
value__
29
--TextFormatter: End of Object--
TypeRef:4
ScrollSignalsButton2Command
5
Int32
value__
30
--TextFormatter: End of Object--
TypeRef:4
ScrollButton1Command
6
Int32
value__
35
--TextFormatter: End of Object--
TypeRef:4
ScrollButton2Command
7
Int32
value__
36
--TextFormatter: End of Object--
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
VSignalObjectStreamer 14 Begin_Of_Object
1
0
[GraphWindow:x_x_x_x_x_x_WindowBk_Grid_AxisBk_XAxisFr_YAxisFr_x_x_x_x_x_x]
0 100000 100000 200000 576000000 1 ffffff b2b2b2 ffffff 0 0 0 0 1 1 1 0
0 30 5000
0
0 100
0
16777215
0
2
0
1
41943040
-1
VLogExportPersister 15 Begin_Of_Object
7
1416
25200253
Graphics Window
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 15
12
20 38 62 20 18 22 22 24 29 33 44 13 
184
0
0
1
0
0
0
-11
0
0
0
0
0
0
0
400
0
Tahoma
0
1
0
0
0
-11
0
0
0
34
0
0
0
400
0
Tahoma
0
1
1
0
0
11711154
32768
0
0
0
0
0
0
0
0
0
-64
0 10
1
0
0
VLogCfgData 15 Begin_Of_Object
14
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
1
1
1
1
0
2
0
0
15
VLogExportPersister 16 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 16

End_Of_Serialized_Data 15
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Graphics.mdf" 
0
1
0
30
80
410
1
1
0


<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\.mdf" 
1
1
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\{LoggingBlock}.mdf" 
0
40cfb8e4-789f-433c-a7e1-d65470d87137
1
VLoggingComment 16 Begin_Of_Object
1
1
VLoggingCommentAttribute 17 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 17
End_Of_Object VLoggingComment 16
3
0
0
End_Of_Object VLogCfgData 15
0 128
0 0 0 0
1 40 20 15
0 -1
1
1
0
0
0
1 12
1 1 1 0 0 0 0 0 0 0 0 0 
0 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 
20 38 82 22 22 24 29 33 44 44 82 30 
184 80
0
0
30000000000
1
0
1
End_Of_Object VSignalObjectStreamer 14
End_Of_Object VGraphBoxConf 13
VDOLine 13 Begin_Of_Object
1
117
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
118
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
119
0
1 0
TABPredecessor:
116
TABSuccessor:
121
VDODynamicLine 11 Begin_Of_Object
1
120
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
121
0
TABPredecessor:
119
TABSuccessor:
123
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 380 166 1523 665
Logging
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{98001069-FEB0-419B-94D9-0F977329F31D}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
122
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
123
0
TABPredecessor:
121
TABSuccessor:
126
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\.blf" 
1
1
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\{LoggingBlock}.blf" 
0
f2bfe56f-4778-4ff5-8df8-9e02bf19f687
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
124
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
125
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
126
0
0 0
TABPredecessor:
123
TABSuccessor:
128
VDODynamicLine 11 Begin_Of_Object
1
127
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
128
0
TABPredecessor:
126
TABSuccessor:
130
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 2
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{4EACEEF4-ED52-4B8F-94FA-5EF243214791}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
129
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
130
0
TABPredecessor:
128
TABSuccessor:
133
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_2.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
5238d471-4748-41a4-a3ae-ba5cead76747
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
131
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
132
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
133
0
0 0
TABPredecessor:
130
TABSuccessor:
135
VDODynamicLine 11 Begin_Of_Object
1
134
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
135
0
TABPredecessor:
133
TABSuccessor:
137
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 3
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{4A8F9397-5E6A-47FB-8B68-FABA8D699A1F}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
136
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
137
0
TABPredecessor:
135
TABSuccessor:
140
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_3.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
0fa3c428-410d-45ae-a1db-7432c356febe
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
138
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
139
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
140
0
0 0
TABPredecessor:
137
TABSuccessor:
142
VDODynamicLine 11 Begin_Of_Object
1
141
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
142
0
TABPredecessor:
140
TABSuccessor:
144
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 4
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{E5ACE51C-FB92-4DE2-928A-BF497CB46084}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
143
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
144
0
TABPredecessor:
142
TABSuccessor:
147
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_4.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
98d8d4fc-634a-4bd5-952b-57a98d618bc1
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
145
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
146
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
147
0
0 0
TABPredecessor:
144
TABSuccessor:
149
VDODynamicLine 11 Begin_Of_Object
1
148
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
149
0
TABPredecessor:
147
TABSuccessor:
151
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 5
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{9F3EC630-7865-4B20-8E47-1DCE2DD08338}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
150
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
151
0
TABPredecessor:
149
TABSuccessor:
154
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "..\..\blf\gen_1\vin_test.blf" 
<VFileName V9 QL> 1 "..\..\csv\vin_test.csv" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_5.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
86ef9ee8-c38b-4ebb-9ea2-f33b12bbbd1a
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
152
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
153
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
154
0
0 0
TABPredecessor:
151
TABSuccessor:
156
VDODynamicLine 11 Begin_Of_Object
1
155
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
156
0
TABPredecessor:
154
TABSuccessor:
158
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 6
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{60259F0B-ADAF-49E1-9B7C-5CE439ABE081}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
157
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
158
0
TABPredecessor:
156
TABSuccessor:
161
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "..\..\blf\gen_1\vin_test.blf" 
<VFileName V9 QL> 1 "..\..\csv\vin_test.asc" 
<VFileName V9 QL> 1 "" 
2
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_6.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
848b412d-f911-4d00-bcb2-e3578ea3516a
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
159
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
160
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
161
0
0 0
TABPredecessor:
158
TABSuccessor:
163
VDODynamicLine 11 Begin_Of_Object
1
162
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
163
0
TABPredecessor:
161
TABSuccessor:
165
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 7
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{694768C5-B288-4985-A041-F2C7D27CB96C}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
164
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
165
0
TABPredecessor:
163
TABSuccessor:
168
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "..\..\blf\gen_1\vin_test.blf" 
<VFileName V9 QL> 1 "..\..\csv\vin_test.asc" 
<VFileName V9 QL> 1 "" 
2
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_7.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
e04627da-e361-440e-b9ba-c3dcf7d89505
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
166
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
167
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
168
0
0 0
TABPredecessor:
165
TABSuccessor:
170
VDODynamicLine 11 Begin_Of_Object
1
169
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
170
0
TABPredecessor:
168
TABSuccessor:
172
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 8
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{7BBED37B-CBE3-4877-856F-5CDD3E47F238}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
171
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
172
0
TABPredecessor:
170
TABSuccessor:
175
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "..\..\blf\gen_1\vin_test.blf" 
<VFileName V9 QL> 1 "..\..\csv\vin_test.asc" 
<VFileName V9 QL> 1 "" 
2
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_8.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
d28c2e98-cbec-4cec-b429-73facce401ba
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
173
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
174
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
175
0
0 0
TABPredecessor:
172
TABSuccessor:
177
VDODynamicLine 11 Begin_Of_Object
1
176
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
177
0
TABPredecessor:
175
TABSuccessor:
179
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1
1
5000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 384 188 1536 753
Logging 9
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 188 1536 753
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{A58ECCDD-F156-4EAC-97C9-7BE09A6AFF9B}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
178
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
179
0
TABPredecessor:
177
TABSuccessor:
0
VLoggingConfiguration 15 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
14
1
1
1
1
0
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "..\..\output\blf\gen_1\vin_test.blf" 
<VFileName V9 QL> 1 "..\..\output\csv\gen_1\vin_test.asc" 
<VFileName V9 QL> 1 "" 
2
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V9 QL> 1 "..\gen_1\Logging_9.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\gen_1\.blf" 
1
1
<VFileName V9 QL> 1 "..\gen_1\{LoggingBlock}.blf" 
0
414d323d-267e-40d0-9413-97e1019776fd
1
VLoggingComment 18 Begin_Of_Object
1
1
VLoggingCommentAttribute 19 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 19
End_Of_Object VLoggingComment 18
2
0
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
1
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
180
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
End_Of_Object VDOFRamification 8
End_Of_Object VDODynamicLine 7
End_Of_Object VDAOGBHSStd 6
End_Of_Object VDAOSwitch 5

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
VDAOGBFunctionBlock 4 Begin_Of_Object
1
89
0
TABPredecessor:
0
TABSuccessor:
90
VPlugConf 5 Begin_Of_Object
1
End_Of_Object VPlugConf 5
VDAOGBFunctionBlock 5 Begin_Of_Object
1
90
0
TABPredecessor:
89
TABSuccessor:
182
VChannelFilterConfiguration 6 Begin_Of_Object
3
VMigratedGenericConfiguration<class_VChannelFilterData> 7 Begin_Of_Object
1
VChannelFilterData 8 Begin_Of_Object
2
1
VBusChannelData 9 Begin_Of_Object
1
2
2
1
2
1
1
End_Of_Object VBusChannelData 9
End_Of_Channel_Data
1
End_Of_Object VChannelFilterData 8
End_Of_Object VMigratedGenericConfiguration<class_VChannelFilterData> 7
0
0
End_Of_Object VChannelFilterConfiguration 6
VDOCrossing 6 Begin_Of_Object
2
91
0
VDAOGBHSStd 7 Begin_Of_Object
1
92
0
0 0
TABPredecessor:
187
TABSuccessor:
94
VDODynamicLine 8 Begin_Of_Object
1
93
0
0
NULL
End_Of_Object VDODynamicLine 8
End_Of_Object VDAOGBHSStd 7
NULL
VDODynamicLine 7 Begin_Of_Object
1
181
1
20
VDAOGBHSStd 8 Begin_Of_Object
1
182
1
1 0
TABPredecessor:
90
TABSuccessor:
187
VDODynamicLine 9 Begin_Of_Object
1
183
1
0
VDODynamicLine 10 Begin_Of_Object
1
184
1
10
VDODynamicLine 11 Begin_Of_Object
1
185
0
20
VDOLine 12 Begin_Of_Object
1
186
0
10 0
VDAOGBFunctionBlock 13 Begin_Of_Object
1
187
0
TABPredecessor:
182
TABSuccessor:
92
VLoggingConfiguration 14 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 15 Begin_Of_Object
1
VLogCfgData 16 Begin_Of_Object
14
0
1
1
1
0
0
0
0
1024
60
0
0
0
3
1
1
1
1
0
2
1
0
16
VLogExportPersister 17 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 17

End_Of_Serialized_Data 16
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_PreFilter.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_PreFilter.blf" 
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_PreFilter.blf" 
1
1
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_PreFilter.blf" 
0
73d667f9-0bed-4c8d-8992-1d5bdeebd837
1
VLoggingComment 17 Begin_Of_Object
1
1
VLoggingCommentAttribute 18 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 18
End_Of_Object VLoggingComment 17
2
0
0
End_Of_Object VLogCfgData 16
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 15
5
End_Of_Object VLoggingConfiguration 14
NULL

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 13
End_Of_Object VDOLine 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDODynamicLine 9
End_Of_Object VDAOGBHSStd 8
End_Of_Object VDODynamicLine 7
4
End_Of_Object VDOCrossing 6

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
0
End_Of_Object VDOLocalInfoStruct 3
67305472
0
1
2
VDOLocalInfoStruct 3 Begin_Of_Object
End_Of_Object VDOLocalInfoStruct 3
End_Of_Serialized_Data 2
0.000000
0 0
End_Of_Object VGBAnlyzBox 2
VGBRealTimeBox 2 Begin_Of_Object
1
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 2 0 1 -1 -1 -1 -1 958 0 1916 561

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 958 0 1916 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 958 0 1916 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 958 0 1916 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 958 0 1916 561
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{B6360C62-7731-4A34-9291-291FB0305ED4}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
4
1
24
VDAOBus 4 Begin_Of_Object
1
1
0
0
TABPredecessor:
0
TABSuccessor:
2
VDAOGBFunctionBlock 5 Begin_Of_Object
1
2
0
TABPredecessor:
1
TABSuccessor:
0
VCardConf 6 Begin_Of_Object
1
End_Of_Object VCardConf 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
End_Of_Object VDAOBus 4
NULL
0
End_Of_Object VDOLocalInfoStruct 3
0.000000
0 0
1 1 0 59420 1 280 1 2882400001 967 1247 176 700 2882400002  0 0 0 0 0 0 0 2882400001 860 1060 420 620 2882400002  0 0 0 140727252561528 0 0   3 
SS_BEGIN_COMMON_INFO
1
0
SS_END_COMMON_INFO

EOF_MBSSDATA
1
CAN
1
1
1
2061435576928 1 0 1 0 0 0 0 0 0 2000 1 
SS_BEGIN_COMMON_INFO
1
2
Channels
1
Misc
1
SS_END_COMMON_INFO

EOF_BUSDATA
1
_Start_VPRBSManager 1 
0 0x32 0x1 
_End_VPRBSManager
NodeSignalPanelBustypeCount 0
EOF_BUS

EOF_MBSSDATA
End_Of_Object VGBRealTimeBox 2
VWriteBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 1 0 1 -1 -1 -1 -1 0 561 1916 844
Write
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 561 1916 844
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 561 1916 844
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 561 1916 844
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{1F088684-26EA-480E-AADC-CB64C3DB61E7}
0
End_Of_Object VBoxRoot 4
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 3
2
VWriteControlAdapter 3 Begin_Of_Object
2
VControlAdapter 4 Begin_Of_Object
1
End_Of_Object VControlAdapter 4
1
3
WListVer 2
<VFileName V9 QL> 1 "..\..\..\..\..\..\Public\Documents\Vector\CANoe\Sample Configurations 15.2.41" 
 0 1 1 1 1 0
 False 147 60 0 700 760 760 760
End_Of_Serialized_Data 3
End_Of_Object VWriteControlAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VWriteBox 2
VWinStore 2 Begin_Of_Object
1
22 2 3 -32000 -32000 -1 -1 452 138 1472 905
End_Of_Child_List
End_Of_Object VWinStore 2
VWinStore 2 Begin_Of_Object
1
22 2 3 -32000 -32000 -1 -1 -10000 -10000 -8980 -9233
End_Of_Child_List
End_Of_Object VWinStore 2
VChipMultibusConfig 2 Begin_Of_Object
1
Version 8 10
5 64
0
9 0
11 0
1
14 0
1
12 1
3
0 127 0 0 1 2900 10 0 0 0
1 2
5
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
13 0
2
15 0
7 0
16 0
1
End_Of_Object VChipMultibusConfig 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 0
0
End_Of_Object VChipConfigC200 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 1
0
End_Of_Object VChipConfigC200 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC005 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC005 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC527 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC527 2
VChipConfigC1000 2 Begin_Of_Object
1
0
1000 16000 0 0
1 35 1 0 2 0 0 0 0 0 0
1 1000 0
0
55 24 0
2 2 27 12 2 2 0
0
0 0
6 3 2 2
End_Of_Object VChipConfigC1000 2
VChipConfigC1000 2 Begin_Of_Object
1
0
1000 16000 0 0
1 35 1 0 2 0 0 0 0 0 0
1 1000 1
0
55 24 0
2 2 27 12 2 2 0
0
0 0
6 3 2 2
End_Of_Object VChipConfigC1000 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 0
0
End_Of_Object VChipConfigC462 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 1
0
End_Of_Object VChipConfigC462 2
0
13
3 0
5 0
6 0
7 0
8 0
9 0
11 0
13 0
14 0
15 0
16 0
17 0
18 0
VScanBaudrateConfiguration 2 Begin_Of_Object
1
2
256
1000
5
1000
1
0
1
256
1000
5
1000
1
0
1
End_Of_Object VScanBaudrateConfiguration 2
4
0
VPersistentPath 2 Begin_Of_Object
1
<VFileName V9 QL> 1 "config.cpd" 
End_Of_Object VPersistentPath 2
0
0
0
0
VPlugInsPersistentWrapper 2 Begin_Of_Object
1
<PlugIns>
</PlugIns>
End_Of_Object VPlugInsPersistentWrapper 2
0
0
VMacroStreamer 2 Begin_Of_Object
2
VMacroManager 3 Begin_Of_Object
3
0
0
0
0
End_Of_Object VMacroManager 3
End_Of_Object VMacroStreamer 2
VSignalGeneratorStreamer 2 Begin_Of_Object
1
VAnlyzSigGeneratorManager 3 Begin_Of_Object
5
0
0
0
0
0
End_Of_Object VAnlyzSigGeneratorManager 3
End_Of_Object VSignalGeneratorStreamer 2
SignalGeneratorsReplay 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Signal Generators and Signal Replay
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{A47C3A65-0122-41DE-9522-B1EDA732B2F8}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
2
1
2
1
<?xml version="1.0" encoding="utf-8"?>
<SymbolSelectionProperties xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<AreDiagQualifiersShown>true</AreDiagQualifiersShown>
<CurrentViewId>None</CurrentViewId>
<SearchProperties>
<SearchTermHistory />
</SearchProperties>
<ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>Embedded/NetworkSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
</ViewProperties>
</SymbolSelectionProperties>
END_OF_WORKSPACE_MEMBER_DATA
END_OF_WORKSPACE_MEMBER
2
0
0
0
0

END_OF_WORKSPACE_DATA

END_OF_WORKSPACE_CONFIGURATION
LinNMWindow 0
LinScopeWindow 0
VCanGlOpConf 2 Begin_Of_Object
1
1
2
End_Of_Object VCanGlOpConf 2
0
1
0
<End_of_SimulinkController>
StartOfComment
EndOfComment
16.7 SP7
VHILInterfaceMgrAnlyz 2 Begin_Of_Object
5
0
0
2809
0
3030
1
End_Of_Object VHILInterfaceMgrAnlyz 2
0
BasicDiagnosticsEditor 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Basic Diagnostics
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{0379109C-B49D-4F9E-B492-0DD19EE84138}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANalyzer.BasicDiagnosticsEditor.DLL
Vector.CANalyzer.BasicDiagnosticsEditor, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.BasicDiagnosticsEditor.VBasicDiagnosticsEditorWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
0
CalculateExtendedStatistics 1
0
0
25
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.SymbolSelectionListBox.Data.SymbolMRUList
1
1
Int32
Count
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
J1939::VGlobalSettings 2 Begin_Of_Object
2
1
0
End_Of_Object J1939::VGlobalSettings 2
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Start Values
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{07079DC4-1507-4DC5-B862-4A71B3D8F17B}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
424
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.StartValuesController
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValuesModel
3
StartValuesModel
3
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.GUISettings
4
GUISettings
4
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Boolean
SetValuesOnMeasurementStart
True

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValue, Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null]]
6
StartValues
6

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValueGroup, Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null]]
7
StartValuesGroups
7
TypeRef:5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:4
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.GUI.ColumnSettings, Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null]]
8
ColumnSettings
9
TypeRef:5
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Array
_items
11
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValue
9
1
0
-1
Int32
_size
0
Int32
_version
0
--TextFormatter: End of Object--
TypeRef:7
7
Array
_items
12
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValueGroup
10
1
0
3
TypeRef:10
ArrayElement
13

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Object
11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
Int32
_size
1
Int32
_version
92
--TextFormatter: End of Object--
TypeRef:8
9
Array
_items
14
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.ColumnSettings
12
1
0
15
TypeRef:12
ArrayElement
15
TypeRef:12
ArrayElement
16
TypeRef:12
ArrayElement
17
TypeRef:12
ArrayElement
18
TypeRef:12
ArrayElement
19
TypeRef:12
ArrayElement
20
TypeRef:12
ArrayElement
21
TypeRef:12
ArrayElement
22
TypeRef:12
ArrayElement
23
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
Int32
_size
9
Int32
_version
119
--TextFormatter: End of Object--
TypeRef:10
13
Int32
StartValueGroup_ID
0
String
StartValueGroup_Name
1
Start values group1
String
StartValueGroup_Comment
1

Boolean
StartValueGroup_Active
True
Boolean
StartValueGroup_AutoPersist
False
TypeRef:5
SerializationVersion
24
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
15
Int32
mHandle
0
Int32
mDefaultWidth
25
Int32
mWidth
25
Int32
SortOrderInt
0
Int32
mVisibleIndex
1
TypeRef:5
SerializationVersion
25
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
3
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
16
Int32
mHandle
1
Int32
mDefaultWidth
142
Int32
mWidth
142
Int32
SortOrderInt
0
Int32
mVisibleIndex
2
--TextFormatter: End of Object--
TypeRef:12
17
Int32
mHandle
2
Int32
mDefaultWidth
130
Int32
mWidth
130
Int32
SortOrderInt
0
Int32
mVisibleIndex
3
--TextFormatter: End of Object--
TypeRef:12
18
Int32
mHandle
3
Int32
mDefaultWidth
84
Int32
mWidth
84
Int32
SortOrderInt
0
Int32
mVisibleIndex
5
--TextFormatter: End of Object--
TypeRef:12
19
Int32
mHandle
4
Int32
mDefaultWidth
120
Int32
mWidth
120
Int32
SortOrderInt
0
Int32
mVisibleIndex
6
--TextFormatter: End of Object--
TypeRef:12
20
Int32
mHandle
5
Int32
mDefaultWidth
70
Int32
mWidth
70
Int32
SortOrderInt
0
Int32
mVisibleIndex
4
--TextFormatter: End of Object--
TypeRef:12
21
Int32
mHandle
6
Int32
mDefaultWidth
55
Int32
mWidth
55
Int32
SortOrderInt
0
Int32
mVisibleIndex
0
--TextFormatter: End of Object--
TypeRef:12
22
Int32
mHandle
7
Int32
mDefaultWidth
198
Int32
mWidth
198
Int32
SortOrderInt
0
Int32
mVisibleIndex
8
--TextFormatter: End of Object--
TypeRef:12
23
Int32
mHandle
8
Int32
mDefaultWidth
40
Int32
mWidth
40
Int32
SortOrderInt
0
Int32
mVisibleIndex
7
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
VStandaloneLoggingUserConfig 2 Begin_Of_Object
4
0
VLogCfgData 3 Begin_Of_Object
14
1
1
0
1
1
0
0
0
1024
60
1
0
0
3
1
1
1
1
0
2
0
0
3
VLogExportPersister 4 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 4

End_Of_Serialized_Data 3
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_.blf" 
0
0
1
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_{MeasurementIndex}.blf" 
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_001.blf" 
0
1
<VFileName V9 QL> 1 "..\..\..\TEST\python_api_vector_canoe\input\config\Logging_{MeasurementIndex}.blf" 
0
c91360ac-f841-4293-b22b-6d558d51c087
1
VLoggingComment 4 Begin_Of_Object
1
0
End_Of_Object VLoggingComment 4
2
0
0
End_Of_Object VLogCfgData 3
0
VAutoRunPreLoggingCaplBox 3 Begin_Of_Object
1
<VFileName V9 QL> 0 "" 
0
End_Of_Object VAutoRunPreLoggingCaplBox 3
0
VTriggerCfgData 3 Begin_Of_Object
5
1
0
0
1
0
0
0
0
1
0
0
0
0
0
0
1
1
5000
VEvCondBlock 4 Begin_Of_Object
1
VEvCondGroup 5 Begin_Of_Object
2
VEvCondPrimitive 6 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 6
1
0
0
End_Of_Object VEvCondGroup 5
End_Of_Object VEvCondBlock 4
VEvCondBlock 4 Begin_Of_Object
1
VEvCondGroup 5 Begin_Of_Object
2
VEvCondPrimitive 6 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 6
1
0
0
End_Of_Object VEvCondGroup 5
End_Of_Object VEvCondBlock 4
0
0
0
116
1
0
0
1
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 3
End_Of_Object VStandaloneLoggingUserConfig 2
Mapping::VMappingManager 2 Begin_Of_Object
4
1
Mapping::VMappingGroup 3 Begin_Of_Object
2
1
1
0
Static Mapping
1
Mapping::VMappingGroup 4 Begin_Of_Object
2
1
0
1
Group 1
0
0
0
End_Of_Object Mapping::VMappingGroup 4
0
0
End_Of_Object Mapping::VMappingGroup 3
0

End_Of_Object Mapping::VMappingManager 2
VTSystemControl 0
TestConfigurationSetup
VTestConfigurationSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Test Setup for Test Units
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{ED618A25-4011-4AA1-908A-DD3F5297F140}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
35
APPDIR Vector.CANoe.TestConfigurationSetup.DLL
Vector.CANoe.TestConfigurationSetup, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.TestConfigurationSetup.TestConfigurationSetup
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
TestConfigurationSetupPersistence
1
0;-1;-1;
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VTestConfigurationSetupWrapper 2
AFDXVLStatisticSysVars
NAFDX::NStatisticsMonitor::VSVClient 2 Begin_Of_Object
1
Begin_Of_Multi_Line_String
2
﻿<?xml version="1.0" encoding="utf-8"?>
<systemvariables version="4" />
End_Of_Serialized_Data 2
End_Of_Object NAFDX::NStatisticsMonitor::VSVClient 2
DocumentViewer
VDocumentViewerWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Documents
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{07CCB4B4-7935-4502-AB59-82FC6907ECF7}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
37
APPDIR Vector.CANalyzer.DocumentViewer.DLL
Vector.CANalyzer.DocumentViewer, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DocumentViewer.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
SplitterExpanded
True
Int32
DocumentListHeight
77
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
0
End_Of_Object VDocumentViewerWrapper 2
SVDialogSettings
VSVDialogSettings 2 Begin_Of_Object
1
-1
-1
930
600
1
1
0
320
440
365
0
0
0
0
0
End_Of_Object VSVDialogSettings 2
FunctionBusDialogSettings
VFunctionBusDialogSettings 2 Begin_Of_Object
2
-1
-1
1140
550
300
300
-1
0
End_Of_Object VFunctionBusDialogSettings 2
AutomationSequences
VAutomationSequencesWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Automation Sequences
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{85853E0A-F90B-4A5E-919C-954121F753FF}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
34
APPDIR Vector.CANalyzer.AutomationSequences.DLL
Vector.CANalyzer.AutomationSequences, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.AutomationSequences.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Int32
SelectedTabPage
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VAutomationSequencesWrapper 2
LogFileConverter
VLogFileConverter 2 Begin_Of_Object
1
2
VLogExportPersister 3 Begin_Of_Object
7
1416
78171113
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "..\..\..\..\..\Downloads\VIN 643_System issue_12.03.2025 (1).csv" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.
None
0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
0
End_Of_Object VLogExportPersister 3

End_Of_Serialized_Data 2
End_Of_Object VLogFileConverter 2
ThreadingSettings
VPersistentThreadingSettings 2 Begin_Of_Object
1
3
7
End_Of_Object VPersistentThreadingSettings 2
GlSignalSamplingSettings
GlLoggerConfig::VGlSignalSamplingSettings 2 Begin_Of_Object
1
0
End_Of_Object GlLoggerConfig::VGlSignalSamplingSettings 2
NodeLayerConfiguration
32
APPDIR Vector.CANoe.NodeLayer.Configuration.Persistency.DLL
Vector.CANoe.NodeLayer.Configuration.Persistency, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeLayer.Configuration.Persistency.Persistor
1
1
String
NodeLayers
7
<ProjectVariantPropertyModel>
  <FileFormatVersion>3</FileFormatVersion>
  <ProjectVariantProperties>
    <VariantProperties />
    <ModifiableParameters />
  </ProjectVariantProperties>
</ProjectVariantPropertyModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
ILConfigurationComponent
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440

1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{67135C6A-3FB7-4A80-A7F4-31BD80DB17B8}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
47
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.ILConfigurationComponent
1
1
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.GUI.GUISettings
2
GUISettings
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
Boolean
DbcSettingsAvailable
False
TypeRef:3
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
ScenarioManager
NWlan::VCar2xScenarioManagerWnd 2 Begin_Of_Object
3
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Car2x Scenario Manager
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{9F2B4698-29AF-492C-B74F-FD0F83956924}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
31
APPDIR Vector.CANalyzer.Wlan.ScenarioManager.DLL
Vector.CANalyzer.Wlan.ScenarioManager, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Wlan.ScenarioManagerProxy
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3

1

End_Of_Object NWlan::VCar2xScenarioManagerWnd 2
Multimedia
VMultimediaWrapper 2 Begin_Of_Object
1
0
End_Of_Object VMultimediaWrapper 2
FrameworkData
70
APPDIR Vector.CANalyzer.Framework.DLL
Vector.CANalyzer.Framework, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Framework.SerializationStore
1
1

System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.HashSet`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]
2
WindowFavorites
2

System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.HashSet`1[[Vector.CANalyzer.Framework.FavoritesManager+DialogCommandInfo, Vector.CANalyzer.Framework, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null]]
3
DialogFavorites
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
Int32
Version
2

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.GenericEqualityComparer`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]
5
Comparer
5
Int32
Capacity
0
--TextFormatter: End of Object--
TypeRef:3
3
Int32
Version
2

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.ObjectEqualityComparer`1[[Vector.CANalyzer.Framework.FavoritesManager+DialogCommandInfo, Vector.CANalyzer.Framework, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null]]
6
Comparer
6
Int32
Capacity
0
--TextFormatter: End of Object--
TypeRef:5
5
--TextFormatter: End of Object--
TypeRef:6
6
--TextFormatter: End of Object--
VCommonMacroSettings
VCommonMacroSettings 2 Begin_Of_Object
1
3
YYYY-MM-DD_hh-mm-ss
End_Of_Object VCommonMacroSettings 2
VSymbolSelectionDialogSettings
VSymbolSelectionDialogSettings 2 Begin_Of_Object
1

End_Of_Object VSymbolSelectionDialogSettings 2
FunctionBusData
NFunctionBus::NDataModel::VFunctionBusData 2 Begin_Of_Object
9
0
NFunctionBus::NDataModel::VBindingConfiguration 3 Begin_Of_Object
2
4
Abstract
CAPL
C#
Mapping
End_Of_Object NFunctionBus::NDataModel::VBindingConfiguration 3


End_Of_Object NFunctionBus::NDataModel::VFunctionBusData 2
OfflineConfig
VOfflineConfigWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Offline Mode
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{42AE76F5-A50F-43B7-B52A-EC9A26DAAD32}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
159
APPDIR Vector.CANalyzer.OfflineMode.GUI.DLL
Vector.CANalyzer.OfflineMode.GUI, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.OfflineMode.GUI.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
OverallModel
125
<?xml version="1.0" encoding="utf-8"?>
<OfflineConfigOverallModel xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<DataModel xmlns="" i:type="OfflineConfigDataModel">
<LogFileMergeMode>ByMeasurementTime</LogFileMergeMode>
<RootGroup xmlns="" i:type="RootGroup">
<OfflineSources xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
<Id>59c0bf81-1278-4746-9eeb-2acc151cbf65</Id>
<UserOffset>0</UserOffset>
<MergeMode>0</MergeMode>
<ChannelMappingSetId i:nil="true" />
</RootGroup>
<ChannelMappingSets xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
<DefaultChannelMappingSetId i:nil="true" />
<BreakOnCondition>false</BreakOnCondition>
<BreakpointOffset>PT0S</BreakpointOffset>
<BreakpointOffsetMode>NoBreak</BreakpointOffsetMode>
<TimeRangeMode>EntireRange</TimeRangeMode>
<TimeRangeStart i:nil="true" />
<TimeRangeEnd i:nil="true" />
</DataModel>
<GuiModel xmlns="" i:type="OfflineConfigGuiModel">
<ConfigDialogState xmlns="" i:type="ConfigDialogState">
<Left i:nil="true" />
<Top i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
</ConfigDialogState>
<FrontEndViewState xmlns="" i:type="FrontEndViewState">
<ColumnStates xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>IsActive</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>0</VisiblePosition>
<Width>56</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileTitle</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>1</VisiblePosition>
<Width>190</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>MeasurementStart</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>2</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>MeasurementEnd</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>3</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FirstTimeStamp</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>4</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>UserOffset</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>5</VisiblePosition>
<Width>100</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileFormat</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>6</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FormatVersion</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>7</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>DisplayTargetId</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>8</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileSize</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>9</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>UncompressedSize</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>10</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>NumberOfObjects</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>11</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>ChannelMappingSet</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>12</VisiblePosition>
<Width>100</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>IndexField</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>13</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileName</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>14</VisiblePosition>
<Width>227</Width>
</d4p1:anyType>
</ColumnStates>
<ExtensibleMetadataHeight>150</ExtensibleMetadataHeight>
<IsExtensibleMetadataExpanded>false</IsExtensibleMetadataExpanded>
</FrontEndViewState>
</GuiModel>
</OfflineConfigOverallModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VOfflineConfigWrapper 2
CanDbSettings
VCanDbSettings 2 Begin_Of_Object
1
64
































































End_Of_Object VCanDbSettings 2
CANstressNGSettings
CANstressNG::VCANstressNGMgr 2 Begin_Of_Object
1
0
34
64
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
End_Of_Object CANstressNG::VCANstressNGMgr 2
FunctionBusSetup
VFunctionBusSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 -1 -1 -1 -1 40 40 1148 721
Function Bus Setup
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 40 40 1148 721
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
1
1920 848
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{A55D7B3D-107C-4FCE-8258-B199F10EAD0D}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 4
77
APPDIR Vector.CANoe.FunctionBus.GUI.DLL
Vector.CANoe.FunctionBus.GUI, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.FunctionBusSetup.FunctionBusSetupComponent
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
DataModel
31
<?xml version="1.0" encoding="utf-8"?>
<DataModel xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<ViewDataModel xmlns="" i:type="ViewDataModel">
<BindingViewState xmlns="" i:type="BindingViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</BindingViewState>
<InterfacesViewState xmlns="" i:type="InterfacesViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</InterfacesViewState>
<MainViewState xmlns="" i:type="MainViewState">
<SplitterPosition i:nil="true" />
</MainViewState>
<ParticipantSendModelViewState xmlns="" i:type="ParticipantSendModelViewState">
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</ParticipantSendModelViewState>
</ViewDataModel>
</DataModel>
String
FolderViewGuiPersistency
9
<?xml version="1.0" encoding="utf-8"?>
<FolderViewGui xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<NameColumnWidth>150</NameColumnWidth>
<StateColumnWidth>100</StateColumnWidth>
<RoleColumnWidth>70</RoleColumnWidth>
<BindingColumnWidth>70</BindingColumnWidth>
<SubSystemColumnWidth>100</SubSystemColumnWidth>
<RtServerColumnWidth>70</RtServerColumnWidth>
</FolderViewGui>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
5
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VFunctionBusSetupWrapper 2
ApplicationModelSetup
VApplicationModelSetup 2 Begin_Of_Object
1
0
End_Of_Object VApplicationModelSetup 2
DiagnosticParameterWindow
VDiagnosticParameterWindowWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 40 40 540 440
Diagnostic Parameters
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 380 166 1523 665
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 9 0 0 -1 -1 40 40 540 440
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{4A1915D3-A3E1-4AFE-AB0F-4978BD4916B9}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
50
APPDIR Vector.CANalyzer.DiagnosticParameterWindow.DLL
Vector.CANalyzer.DiagnosticParameterWindow, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DiagnosticParameterWindow.DiagnosticParameterWindow
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
ParameterWindowCtrlPersistence
1
-1;False;0,200,True;1,60,True;2,60,True;3,75,True;4,21,True;5,50,True;6,75,True;
APPDIR Vector.CANalyzer.DiagnosticParameterWindow.DLL
Vector.CANalyzer.DiagnosticParameterWindow, Version=16.7.5.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DiagnosticParameterWindow.Definitions.LastRecentSearchPopupItemList
3
RecentSearchItems
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Int32
Version
1
Int32
Count
0
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VDiagnosticParameterWindowWrapper 2
ParticipantModelSetup
VParticipantModelSetup 2 Begin_Of_Object
2
0
0
End_Of_Object VParticipantModelSetup 2
VttTapClient
VttTapClientPersist 2 Begin_Of_Object
1
0
End_Of_Object VttTapClientPersist 2
VarCodeView
VVarCodeViewWrapperManager 2 Begin_Of_Object
1
0
End_Of_Object VVarCodeViewWrapperManager 2
ConnectivitySettings
Connectivity::VConnectivitySettings 2 Begin_Of_Object
14

0

0



0


0
0


0

0
0
3
5
-1
0
3
0
CANoe::Connectivity::MQTT
7
0
4
0
CANoe::Connectivity::HTTP
8
0
5
0
CANoe::Connectivity::ConnectivityHub
1
0
6
1

1
256
End_Of_Object Connectivity::VConnectivitySettings 2
ErtSysVars
VErtSystemVariableManagerAnlyz 2 Begin_Of_Object
1
0
End_Of_Object VErtSystemVariableManagerAnlyz 2
PDUIGComponentManager
0
MGWSettings
VMGWSettings 2 Begin_Of_Object
1
0
End_Of_Object VMGWSettings 2
FunctionBusInteractiveStimulation
NFunctionBusInteractiveStimulation::VStimulationManager 2 Begin_Of_Object
1
0
End_Of_Object NFunctionBusInteractiveStimulation::VStimulationManager 2
GraphicalElementComponent
NGraphicalElements::VGraphicalElementHostManager 2 Begin_Of_Object
1
0
End_Of_Object NGraphicalElements::VGraphicalElementHostManager 2
DiagnosticViewComponent
NDiagnosticViews::VWindowHostManager 2 Begin_Of_Object
1
0
End_Of_Object NDiagnosticViews::VWindowHostManager 2
IntegrationBus
IntegrationBusPersist 2 Begin_Of_Object
3
0



<VFileName V9 QL> 0 "" 
0
0
0
End_Of_Object IntegrationBusPersist 2
BussiMapping
BussiMappingPersist 2 Begin_Of_Object
1
0
End_Of_Object BussiMappingPersist 2
End_Of_Object VGlobalConfiguration 1

import os
import socket
import sqlite3
import subprocess
import threading
import time
from typing import Dict

from dotenv import load_dotenv
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload, MediaUploadProgress
from loguru import logger
from tenacity import retry, wait_exponential, stop_after_attempt, retry_if_exception_type

from src.queue_manager.core import QueueManager
from src.queue_manager.interfaces.drive import authenticate_drive
from src.queue_manager.interfaces.sheets import authenticate_sheets, fetch_sheet_data

load_dotenv()  # Load environment variables from .env file

DRIVE_PROJECT_FOLDER = os.environ.get('DRIVE_PROJECT_FOLDER', '1BFbahFfHlndjov7TYVM5yvU8r5reMEje')  # Set this in your environment

def get_or_create_drive_subfolder(service, parent_id, subfolder_name):
    # Search for subfolder
    # Escape backslashes in subfolder_name for the query
    escaped_subfolder_name = subfolder_name.replace('\\', '\\\\').replace("'", "\\'")
    results = service.files().list(
        q=f"'{parent_id}' in parents and name='{escaped_subfolder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        spaces='drive',
        fields='files(id, name)',
        pageSize=1
    ).execute()
    files = results.get('files', [])
    if files:
        return files[0]['id']
    # Create if not found
    file_metadata = {
        'name': subfolder_name,
        'mimeType': 'application/vnd.google-apps.folder',
        'parents': [parent_id]
    }
    folder = service.files().create(body=file_metadata, fields='id').execute()
    return folder['id']

# Define retry decorator for network operations
@retry(
    retry=retry_if_exception_type((socket.timeout, TimeoutError, ConnectionError, HttpError)),
    wait=wait_exponential(multiplier=1, min=4, max=120),  # max wait increased for large files
    stop=stop_after_attempt(7),  # up to 7 attempts
    reraise=True
)
def upload_with_retry(service, file_metadata, media):
    """Upload a file with retry logic for handling timeouts and connection errors."""
    request = service.files().create(body=file_metadata, media_body=media, fields='id,webViewLink')
    response = None
    
    # Track upload progress
    last_progress = [0]
    
    def progress_callback(progress: MediaUploadProgress):
        if hasattr(progress, 'resumable_progress') and hasattr(progress, 'total_size'):
            current_progress = int(progress.resumable_progress * 100 / progress.total_size)
            if current_progress > last_progress[0] + 10 or current_progress == 100:  # Log every 10% or at completion
                logger.info(f"Upload progress: {current_progress}%")
                last_progress[0] = current_progress
                
    # Execute with progress tracking
    while response is None:
        try:
            status, response = request.next_chunk(num_retries=3)
            if status:
                progress_callback(status)
        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retry on server errors
                logger.warning(f"Server error {e.resp.status}, retrying...")
                time.sleep(5)
                continue
            else:
                # Other HTTP errors are raised
                raise
    
    return response

def upload_csv_and_update_flag(item: Dict, queue: QueueManager):
    """
    Upload a CSV file to Google Drive with chunked uploading and retry logic.
    Updates queue status to 'uploading', then 'uploaded' or 'error_uploading'.
    The 'item' dictionary must contain 'local_csv_path', 'drive_filename', and 'subfolder'.
    """
    local_csv_path = item.get('local_csv_path') # Changed from converted_link
    drive_filename = item.get('drive_filename')
    subfolder = item.get('subfolder', '') # Default to empty string if not present

    if not drive_filename: # Basic check
        logger.error(f"drive_filename missing in item for upload: {item}")
        return None

    if not local_csv_path or not os.path.exists(local_csv_path):
        logger.error(f"Local CSV file not found or path missing for upload: {local_csv_path} (item: {drive_filename}). Marking as error_uploading.")
        # Pass local_csv_path if available, even if it's invalid, for sheet consistency
        queue.update_status(item, 'error_uploading', local_csv_path_val=local_csv_path)
        return None

    logger.info(f"Setting status to 'uploading' for {drive_filename} ({subfolder})")
    # Pass local_csv_path_val to ensure it's in item_details_for_sheet
    item = queue.update_status(item, 'uploading', local_csv_path_val=local_csv_path)

    try:
        service = authenticate_drive()
        
        csv_files_folder_id = get_or_create_drive_subfolder(service, DRIVE_PROJECT_FOLDER, 'CSV_FILES')
        target_drive_folder_id = get_or_create_drive_subfolder(service, csv_files_folder_id, subfolder) if subfolder else csv_files_folder_id
        
        file_metadata = {
            'name': os.path.basename(local_csv_path), # Use the actual CSV filename
            'parents': [target_drive_folder_id]
        }
        
        file_size_mb = os.path.getsize(local_csv_path) / (1024 * 1024)
        logger.info(f"Starting upload of {local_csv_path} ({file_size_mb:.2f} MB) for {drive_filename} ({subfolder})")
        
        chunk_size = 8 * 1024 * 1024
        media = MediaFileUpload(
            local_csv_path,
            mimetype='text/csv',
            resumable=True,
            chunksize=chunk_size
        )
        
        uploaded_file_details = upload_with_retry(service, file_metadata, media)
        
        drive_file_id = uploaded_file_details.get('id')
        google_drive_link = uploaded_file_details.get('webViewLink')

        if drive_file_id and google_drive_link:
            logger.info(f"Successfully uploaded {local_csv_path}. Drive link: {google_drive_link}. Updating status to 'uploaded'.")
            # Pass the google_drive_link to be stored as remote_converted_link in DB for 'uploaded' status
            queue.update_status(item, 'uploaded', remote_converted_link=google_drive_link, local_csv_path_val=local_csv_path)
        else:
            logger.error(f"Upload seemed successful for {local_csv_path} but no Drive file ID/Link was returned. Marking as error_uploading.")
            queue.update_status(item, 'error_uploading', local_csv_path_val=local_csv_path) # No drive link to pass
            
        logger.info(f"Drive file ID: {drive_file_id if drive_file_id else 'N/A'} for {local_csv_path}")
        return drive_file_id

    except HttpError as http_err:
        logger.error(f"Google Drive API HttpError during upload for {drive_filename} ({subfolder}): {http_err}")
        logger.error(f"Details: Status {http_err.resp.status}, Reason: {http_err._get_reason()}")
        queue.update_status(item, 'error_uploading', local_csv_path_val=local_csv_path)
        raise
    except Exception as e:
        logger.error(f"Upload failed for {drive_filename} ({subfolder}): {str(e)}")
        queue.update_status(item, 'error_uploading', local_csv_path_val=local_csv_path)
        raise

def async_upload(item: Dict, queue: QueueManager):
    """Wrapper for asynchronous upload."""
    try:
        upload_csv_and_update_flag(item, queue)
    except Exception as e:
        # Error is already logged by upload_csv_and_update_flag,
        # and status is set to error_uploading.
        # This log is for the async wrapper itself.
        logger.error(f"Async upload task failed for item {item.get('drive_filename')}: {e}")


def sync_sheet_with_database():
    """
    Read Google Sheet data, compare with local database, and update mismatched rows.
    """
    try:
        logger.info("Starting Google Sheets sync with local database...")

        # Initialize services
        queue = QueueManager()
        sheets_service = authenticate_sheets()

        # Fetch data from Google Sheets
        sheet_data = fetch_sheet_data(sheets_service)
        if not sheet_data:
            logger.info("No data found in Google Sheets or sheet is empty.")
            return

        # Get all items from local database
        db_items = queue.get_all()

        # Create a lookup dictionary for database items using blf_link as key
        db_lookup = {}
        for item in db_items:
            blf_link = item.get('blf_link')
            if blf_link:
                db_lookup[blf_link] = item

        logger.info(f"Found {len(sheet_data)} rows in Google Sheets and {len(db_items)} items in database.")

        # Compare sheet data with database
        mismatches_found = 0
        for row_index, sheet_row in enumerate(sheet_data, start=2):  # Start from row 2 (assuming row 1 is headers)
            if len(sheet_row) < 2:  # Need at least drive_filename and blf_link
                continue

            # Extract data from sheet row
            # Expected order: [drive_filename, blf_link, local_csv_path, converted_link, queue_number, status, last_updated]
            sheet_drive_filename = sheet_row[0] if len(sheet_row) > 0 else ""
            sheet_blf_link = sheet_row[1] if len(sheet_row) > 1 else ""
            # sheet_local_csv_path = sheet_row[2] if len(sheet_row) > 2 else ""  # Not used currently
            sheet_converted_link = sheet_row[3] if len(sheet_row) > 3 else ""
            sheet_queue_number = sheet_row[4] if len(sheet_row) > 4 else ""
            sheet_status = sheet_row[5] if len(sheet_row) > 5 else ""

            # Find corresponding database item
            db_item = db_lookup.get(sheet_blf_link)
            if not db_item:
                logger.warning(f"Row {row_index}: Item with blf_link '{sheet_blf_link}' found in sheet but not in database.")
                continue

            # Compare key fields and detect mismatches
            mismatch_detected = False
            mismatch_details = []

            # Compare drive_filename
            if sheet_drive_filename != db_item.get('drive_filename', ''):
                mismatch_detected = True
                mismatch_details.append(f"drive_filename: sheet='{sheet_drive_filename}' vs db='{db_item.get('drive_filename', '')}'")

            # Compare status
            if sheet_status != db_item.get('queue_status', ''):
                mismatch_detected = True
                mismatch_details.append(f"status: sheet='{sheet_status}' vs db='{db_item.get('queue_status', '')}'")

            # Compare converted_link
            if sheet_converted_link != db_item.get('converted_link', ''):
                mismatch_detected = True
                mismatch_details.append(f"converted_link: sheet='{sheet_converted_link}' vs db='{db_item.get('converted_link', '')}'")

            # Compare queue_number
            try:
                sheet_queue_num = int(sheet_queue_number) if sheet_queue_number else 0
                db_queue_num = db_item.get('queue_number', 0) or 0
                if sheet_queue_num != db_queue_num:
                    mismatch_detected = True
                    mismatch_details.append(f"queue_number: sheet='{sheet_queue_num}' vs db='{db_queue_num}'")
            except ValueError:
                logger.warning(f"Row {row_index}: Invalid queue_number in sheet: '{sheet_queue_number}'")

            if mismatch_detected:
                mismatches_found += 1
                logger.info(f"Row {row_index}: Mismatch detected for '{sheet_drive_filename}': {'; '.join(mismatch_details)}")

                # Update the database item with sheet data (sheet takes precedence)
                try:
                    # Update the database record using sqlite3 directly
                    with sqlite3.connect(queue.db_path) as conn:
                        update_query = """
                        UPDATE queue
                        SET drive_filename = ?, queue_status = ?, converted_link = ?, queue_number = ?
                        WHERE blf_link = ?
                        """
                        conn.execute(update_query, (
                            sheet_drive_filename,
                            sheet_status,
                            sheet_converted_link,
                            sheet_queue_num,
                            sheet_blf_link
                        ))
                        conn.commit()

                    logger.info(f"Updated database record for '{sheet_drive_filename}' based on sheet data.")

                except Exception as e:
                    logger.error(f"Failed to update database for '{sheet_drive_filename}': {e}")

        if mismatches_found == 0:
            logger.info("No mismatches found between Google Sheets and database.")
        else:
            logger.info(f"Sync completed. Updated {mismatches_found} mismatched records in database.")

    except Exception as e:
        logger.error(f"Error during Google Sheets sync: {e}", exc_info=True)


def check_and_trigger_converter():
    """
    Check if run_converter.ps1 process is running. If not, trigger it.
    """
    try:
        logger.info("Checking for running converter processes...")

        # Check for running PowerShell processes with run_converter.ps1
        check_cmd = [
            "powershell.exe",
            "-Command",
            "Get-Process | Where-Object { $_.ProcessName -eq 'powershell' -and $_.CommandLine -like '*run_converter.ps1*' } | Select-Object Id, ProcessName"
        ]

        result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=30)

        # Check if any converter processes are running
        converter_running = False
        if result.returncode == 0 and result.stdout.strip():
            # Parse the output to see if any processes were found
            output_lines = result.stdout.strip().split('\n')
            # Filter out header lines and empty lines
            process_lines = [line for line in output_lines if line.strip() and not line.startswith('Id') and not line.startswith('--')]

            if process_lines:
                converter_running = True
                logger.info(f"Found {len(process_lines)} running converter process(es).")
            else:
                logger.info("No converter processes found in PowerShell output.")
        else:
            logger.info("No converter processes currently running.")

        if not converter_running:
            logger.info("No run_converter.ps1 process found. Triggering converter...")

            # Trigger the converter using the specified command
            trigger_cmd = [
                "cmd.exe", "/c", "START",
                "run_converter.ps1",
                "/D", "C:\\Users\\<USER>\\Documents\\CODES\\batch_blf_csv_cron",
                "Powershell.exe",
                "-ExecutionPolicy", "Bypass",
                "-File", "C:\\Users\\<USER>\\Documents\\CODES\\batch_blf_csv_cron\\run_converter.ps1"
            ]

            # Execute the command
            trigger_result = subprocess.run(trigger_cmd, capture_output=True, text=True, timeout=60)

            if trigger_result.returncode == 0:
                logger.info("Successfully triggered run_converter.ps1")
            else:
                logger.error(f"Failed to trigger run_converter.ps1. Return code: {trigger_result.returncode}")
                if trigger_result.stderr:
                    logger.error(f"Error output: {trigger_result.stderr}")
                if trigger_result.stdout:
                    logger.info(f"Command output: {trigger_result.stdout}")
        else:
            logger.info("Converter process is already running. No action needed.")

    except subprocess.TimeoutExpired:
        logger.error("Timeout while checking/triggering converter process")
    except Exception as e:
        logger.error(f"Error while checking/triggering converter: {e}", exc_info=True)

def poll_and_upload_controller():
    """
    Polls the queue to upload converted files and handle floating/stuck states.
    This acts as a controller to ensure all items are processed correctly.
    Also performs periodic Google Sheets sync and converter process monitoring.
    """
    queue = QueueManager()
    cycle_count = 0

    while True:
        cycle_count += 1
        logger.info(f"Polling cycle {cycle_count} - Checking items to upload or fix...")

        try:
            # Every 12 cycles (approximately 1 hour), sync with Google Sheets
            if cycle_count % 12 == 1:
                logger.info("Performing periodic Google Sheets sync...")
                sync_sheet_with_database()

            # Every 1 cycles (approximately 40 minutes), check converter process
            if cycle_count % 8 == 1:
                logger.info("Checking converter process status...")
                check_and_trigger_converter()
            all_items = queue.get_all()
            if not all_items:
                logger.info("Queue is empty.")
                time.sleep(300)
                continue

            # A set to keep track of items that are being processed in this cycle
            # to avoid multiple threads for the same item.
            processed_items = set()

            for item in all_items:
                item_id = item.get('id')
                if item_id in processed_items:
                    continue

                status = item.get('queue_status')
                drive_filename = item.get('drive_filename')
                local_csv_path = item.get('local_csv_path')

                if status == 'uploading' and not item.get('converted_link'):
                    logger.warning(f"Found floating 'uploading' state for '{drive_filename}'.")
                    if local_csv_path and os.path.exists(local_csv_path):
                        logger.info(f"Re-initiating upload for '{drive_filename}'.")
                        threading.Thread(target=async_upload, args=(item, queue), daemon=True).start()
                        processed_items.add(item_id)
                    else:
                        logger.error(f"Item '{drive_filename}' is 'uploading' but local file is missing. Marking as 'error_uploading'.")
                        queue.update_status(item, 'error_uploading', local_csv_path_val=local_csv_path)
                        processed_items.add(item_id)

                # Case 3: Floating 'converting' state.
                # The converter script might have crashed.
                elif status == 'converting':
                    logger.warning(f"Found floating 'converting' state for '{drive_filename}'.")
                    if local_csv_path and os.path.exists(local_csv_path):
                        logger.info(f"Local file exists for '{drive_filename}'. Conversion was likely successful. Initiating upload.")
                        threading.Thread(target=async_upload, args=(item, queue), daemon=True).start()
                        processed_items.add(item_id)
                    else:
                        logger.warning(f"Local file does not exist for '{drive_filename}'. Resetting to 'pending' for converter to retry.")
                        queue.update_status(item, 'pending')
                        processed_items.add(item_id)

        except Exception as e:
            logger.error(f"Error in polling upload controller loop: {e}", exc_info=True)
        
        logger.info("Polling cycle complete. Sleeping for 5 minutes.")
        time.sleep(300)


if __name__ == '__main__':
    poll_and_upload_controller()
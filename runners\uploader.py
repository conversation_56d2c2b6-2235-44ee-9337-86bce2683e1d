from dotenv import load_dotenv
load_dotenv() # Load environment variables from .env file
from src.queue_manager.interfaces.drive import authenticate_drive
from googleapiclient.http import MediaFileUpload, MediaUploadProgress
import os
import sqlite3
import time
import socket
from loguru import logger
from googleapiclient.errors import HttpError
from google.auth.transport.requests import Request
from tenacity import retry, wait_exponential, stop_after_attempt, retry_if_exception_type
from src.queue_manager.core import QueueManager
import threading

DRIVE_PROJECT_FOLDER = os.environ.get('DRIVE_PROJECT_FOLDER', '1BFbahFfHlndjov7TYVM5yvU8r5reMEje')  # Set this in your environment

def get_or_create_drive_subfolder(service, parent_id, subfolder_name):
    # Search for subfolder
    results = service.files().list(
        q=f"'{parent_id}' in parents and name='{subfolder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        spaces='drive',
        fields='files(id, name)',
        pageSize=1
    ).execute()
    files = results.get('files', [])
    if files:
        return files[0]['id']
    # Create if not found
    file_metadata = {
        'name': subfolder_name,
        'mimeType': 'application/vnd.google-apps.folder',
        'parents': [parent_id]
    }
    folder = service.files().create(body=file_metadata, fields='id').execute()
    return folder['id']

# Define retry decorator for network operations
@retry(
    retry=retry_if_exception_type((socket.timeout, TimeoutError, ConnectionError, HttpError)),
    wait=wait_exponential(multiplier=1, min=4, max=120),  # max wait increased for large files
    stop=stop_after_attempt(7),  # up to 7 attempts
    reraise=True
)
def upload_with_retry(service, file_metadata, media):
    """Upload a file with retry logic for handling timeouts and connection errors."""
    request = service.files().create(body=file_metadata, media_body=media, fields='id')
    response = None
    
    # Track upload progress
    last_progress = [0]
    
    def progress_callback(progress: MediaUploadProgress):
        if progress.resumable_progress:
            current_progress = int(progress.resumable_progress * 100 / progress.total_size)
            if current_progress > last_progress[0] + 10 or current_progress == 100:  # Log every 10% or at completion
                logger.info(f"Upload progress: {current_progress}%")
                last_progress[0] = current_progress
                
    # Execute with progress tracking
    while response is None:
        try:
            status, response = request.next_chunk(num_retries=3)
            if status:
                progress_callback(status)
        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retry on server errors
                logger.warning(f"Server error {e.resp.status}, retrying...")
                time.sleep(5)
                continue
            else:
                # Other HTTP errors are raised
                raise
    
    return response

def upload_csv_and_update_flag(item: Dict, queue: QueueManager):
    """
    Upload a CSV file to Google Drive with chunked uploading and retry logic.
    Updates queue status to 'uploading', then 'uploaded' or 'error_uploading'.
    The 'item' dictionary must contain 'converted_link', 'drive_filename', and 'subfolder'.
    """
    local_csv_path = item.get('converted_link')
    drive_filename = item.get('drive_filename')
    subfolder = item.get('subfolder', '') # Default to empty string if not present

    if not drive_filename: # Basic check
        logger.error(f"drive_filename missing in item for upload: {item}")
        return None

    if not local_csv_path or not os.path.exists(local_csv_path):
        logger.error(f"Local CSV file not found or path missing for upload: {local_csv_path} (item: {drive_filename}). Marking as error_uploading.")
        queue.update_status(item, 'error_uploading') # Pass the whole item
        return None

    logger.info(f"Setting status to 'uploading' for {drive_filename} ({subfolder})")
    queue.update_status(item, 'uploading') # Pass the whole item

    try:
        service = authenticate_drive()
        
        csv_files_folder_id = get_or_create_drive_subfolder(service, DRIVE_PROJECT_FOLDER, 'CSV_FILES')
        target_drive_folder_id = get_or_create_drive_subfolder(service, csv_files_folder_id, subfolder) if subfolder else csv_files_folder_id
        
        file_metadata = {
            'name': os.path.basename(local_csv_path), # Use the actual CSV filename
            'parents': [target_drive_folder_id]
        }
        
        file_size_mb = os.path.getsize(local_csv_path) / (1024 * 1024)
        logger.info(f"Starting upload of {local_csv_path} ({file_size_mb:.2f} MB) for {drive_filename} ({subfolder})")
        
        chunk_size = 8 * 1024 * 1024
        media = MediaFileUpload(
            local_csv_path,
            mimetype='text/csv',
            resumable=True,
            chunksize=chunk_size
        )
        
        uploaded_file_details = upload_with_retry(service, file_metadata, media)
        
        logger.info(f"Successfully uploaded {local_csv_path}. Updating status to 'uploaded' for {drive_filename} ({subfolder}).")
        drive_file_id = uploaded_file_details.get('id')
        
        if drive_file_id:
            google_drive_link = f"https://drive.google.com/file/d/{drive_file_id}/view"
            logger.info(f"Successfully uploaded {local_csv_path}. Drive link: {google_drive_link}. Updating status to 'uploaded'.")
            # Pass the google_drive_link to be stored as converted_link in DB for 'uploaded' status
            queue.update_status(item, 'uploaded', uploaded_drive_link=google_drive_link)
        else:
            logger.error(f"Upload seemed successful for {local_csv_path} but no Drive file ID was returned. Marking as error_uploading.")
            queue.update_status(item, 'error_uploading') # No drive link to pass
            
        logger.info(f"Drive file ID: {drive_file_id if drive_file_id else 'N/A'} for {local_csv_path}")
        return drive_file_id

    except HttpError as http_err:
        logger.error(f"Google Drive API HttpError during upload for {drive_filename} ({subfolder}): {http_err}")
        logger.error(f"Details: Status {http_err.resp.status}, Reason: {http_err._get_reason()}")
        queue.update_status(item, 'error_uploading') # Pass the whole item
        raise
    except Exception as e:
        logger.error(f"Upload failed for {drive_filename} ({subfolder}): {str(e)}")
        queue.update_status(item, 'error_uploading') # Pass the whole item
        raise

def async_upload(item: Dict, queue: QueueManager):
    """Wrapper for asynchronous upload."""
    try:
        upload_csv_and_update_flag(item, queue)
    except Exception as e:
        # Error is already logged by upload_csv_and_update_flag,
        # and status is set to error_uploading.
        # This log is for the async wrapper itself.
        logger.error(f"Async upload task failed for item {item.get('drive_filename')}: {e}")

def poll_and_upload_missed():
    queue = QueueManager()
    while True:
        try:
            items = queue.get_converted_not_uploaded()
            if not items:
                logger.info("No converted but not uploaded files found.")
            else:
                logger.info(f"Found {len(items)} items to potentially upload.")

            for item_to_upload in items: # Renamed to avoid conflict with 'item' in outer scope if any
                try:
                    # local_csv_path is already part of item_to_upload as 'converted_link'
                    if not item_to_upload.get('converted_link') or not os.path.exists(item_to_upload['converted_link']):
                        logger.warning(f"Local CSV path not found or invalid in item {item_to_upload.get('drive_filename')} ({item_to_upload.get('subfolder')}): {item_to_upload.get('converted_link')}. Skipping upload.")
                        # Optionally, mark as error_uploading here if the link is bad
                        # queue.update_status(item_to_upload, 'error_uploading')
                        continue
                    
                    logger.info(f"Starting async upload thread for {item_to_upload.get('drive_filename')}")
                    # Pass the whole item dictionary to async_upload
                    threading.Thread(target=async_upload, args=(item_to_upload, queue), daemon=True).start()
                
                except Exception as e: # Catch errors specific to starting the thread or pre-thread logic
                    logger.error(f"Failed to start async upload thread for {item_to_upload.get('drive_filename')} ({item_to_upload.get('subfolder')}): {e}")
        except Exception as e:
            logger.error(f"Error in polling upload loop: {e}")
        logger.info("Sleeping 5 minutes before next upload poll.")
        time.sleep(300)

if __name__ == '__main__':
    poll_and_upload_missed()
;CANoe Version |4|15|0|51765 config 
Version: 15.2.41 Build 41
32 PRO
10
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1.0.1
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Controller.CANwinCommunicator
2
1.0.0
VGlobalConfiguration 1 Begin_Of_Object
18
VGlobalParameters 2 Begin_Of_Object
36
2
3,100,200,500
1000000 1.000000 0 1000 1 1 0 0 1 1 1 0 0 0 1 0 0 0
1
0
0 1
ResetSignalsOnMeasurementStart=1
VDatabaseContainerStreamer 3 Begin_Of_Object
9
1
<VFileName V9 QL> 1 "S1_CAN1_Move4.0_Rev4.0.28_Untruncated.dbc" 
CAN1_V01_Move4_0_Rev4_0_28

1
0
1
000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001

1
0
0
0
0
0
End_Of_Object VDatabaseContainerStreamer 3
0
0
1
<VFileName V9 QL> 1 "config.cfg" 
0
0
0
1
VPersistentEnvarSelectionData 3 Begin_Of_Object
1
1 1 0 0
~
~
End_Of_Object VPersistentEnvarSelectionData 3
VPersistentExtensionData 3 Begin_Of_Object
3
VPersistentRelation 4 Begin_Of_Object
1
HookDLLActivations
1
1
End_Of_Object VPersistentRelation 4
End_Of_Object VPersistentExtensionData 3
VPersistentTreeStateInfo 3 Begin_Of_Object
1
Version
5
DialogBegin
1
75 75 605 580
SymbolExplorerDialogBegin
1
HistoryBegin
1 0
HistoryEnd
FiltersBegin
Begin
3 0 -1
0
SymbSelHeaderMgrBegin
1 6
0 1 200 0 0
1 1 100 0 0
2 0 100 0 0
3 0 75 1 1
5 1 80 0 0
6 1 200 0 0
15 1 80 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 -1
-1
SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
10 1 75 0 0
11 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 -1
-1
SymbSelHeaderMgrBegin
1 3
0 1 200 0 0
7 0 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End

FiltersEnd
0 0
SymbolExplorerDialogEnd

DialogEnd
End_Of_Object VPersistentTreeStateInfo 3
VPrintSettings 3 Begin_Of_Object
1
0 0 0 0 0
0 0 0 0 0 0 0 0 0 1
<VFileName V9 QL> 1 "" 
@@@@
Ausdruck Seite: {PAGE}    {DATE}  {TIME}
Lizenznehmer: {LICENSENAME}
Seriennummer: {LICENSENO}
@@@@
0

End_Of_Object VPrintSettings 3
<VFileName V9 QL> 1 "..\Public\Documents\Vector\CANwin\8.0.71\CANwin Demos\templates\portlink1.pre" 
1
VPortlinkConfigurationStreamer 3 Begin_Of_Object
1
1
0
0
0
END_OF_DRIVER
END_OF_PORT_CONFIGURATION_STREAM
End_Of_Object VPortlinkConfigurationStreamer 3
0
1
VWTP20ObsParameters-1:0:0:
FlexRayTP2ObsParameters: 2 0x2 36 VFrTPParams 2 0 1 0 1 1 0 0 8 255 0 :
FlexRayTP2ObsParametersEnd
VDoIPObserverParams 3 Begin_Of_Object
2
1
0
End_Of_Object VDoIPObserverParams 3
VISOTPParameters-1:active:onlyknown:interleave:stmin:seqnr:unexpected:sender:extended:baseaddr=1024:rxmask=255:storedata:maxlen=65536:pad=54
EOO
DiagnosticsSettingsV1.1
<VFileName V9 BQL> 1 base=cfg "BMS_CDD_Version_3.3_15th_JUN_22.120.cdd" 
Base_Variant#en-US#CAN#CAN1##BMS##0#
EOO
DiagnosticsSettingsV2.0
BMS
.C#######0x00#1
.T#1;0
.S#..\..\..\..\..\janarthanan.s\Downloads\Copy of BMS_GenerateKeyEx.dll
EOO
0
0
<VMultibusFilterDialogSettings> V2 275
<VWidthInfoContainer> 6
<VWidthInfo> V2 20 1 -1 36 
<VWidthInfo> V2 1 1 -1 47 
<VWidthInfo> V2 0 1 -1 40 
<VWidthInfo> V2 2 1 -1 47 
<VWidthInfo> V2 4 1 -1 33 
<VWidthInfo> V2 5 3 0 24 1 24 2 24 
<VBusWidthInfoSet> 4
1 0 <VWidthInfoContainer> 1
<VWidthInfo> V2 6 2 0 36 1 36 
1 1 <VWidthInfoContainer> 4
<VWidthInfo> V2 7 1 -1 45 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 9 1 -1 30 
<VWidthInfo> V2 6 2 0 24 1 32 
1 2 <VWidthInfoContainer> 4
<VWidthInfo> V2 19 1 -1 45 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 22 1 -1 30 
<VWidthInfo> V2 6 2 0 24 1 32 
8 0 <VWidthInfoContainer> 5
<VWidthInfo> V2 7 1 -1 47 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 11 3 0 30 1 30 3 30 
<VWidthInfo> V2 17 3 0 20 1 20 2 20 
<VWidthInfo> V2 18 6 0 26 1 33 2 26 3 26 4 20 5 40 
EndOf <VMultibusFilterDialogSettings>
VGlobalActionsStreamer 3 Begin_Of_Object
3
3
0
End_Of_Object VGlobalActionsStreamer 3
VEventSortingConfigStreamer 3 Begin_Of_Object
1
0
0
0
1
1
1
0
End_Of_Object VEventSortingConfigStreamer 3
FlexRayOptionParameters: 2 0 1 0 1 1 :0 :
FlexRayOptionParametersEnd
VCaplOptionsStreamer 3 Begin_Of_Object
2
17
1448
0
2001
1
2002
0
2005
0
2008
1
2013
1
2020
1
2032
1
2038
1
2039
0
2040
1
2041
1
2054
0
2055
1
2065
0
2135
1
2201
0
1
512
End_Of_Object VCaplOptionsStreamer 3
VSVConfigurationStreamer 3 Begin_Of_Object
1
73
﻿<?xml version="1.0" encoding="utf-8"?>
<systemvariables version="4" />
2
0
End_Of_Object VSVConfigurationStreamer 3
VOfflineBusStatisticSettings 3 Begin_Of_Object
1
1
1
1 1
1 500000
1 2
1 500000
0 3
1 0
0 4
1 0
0 5
1 0
0 6
1 0
0 7
1 0
0 8
1 0
0 9
1 0
0 10
1 0
0 11
1 0
0 12
1 0
0 13
1 0
0 14
1 0
0 15
1 0
0 16
1 0
0 17
1 0
0 18
1 0
0 19
1 0
0 20
1 0
0 21
1 0
0 22
1 0
0 23
1 0
0 24
1 0
0 25
1 0
0 26
1 0
0 27
1 0
0 28
1 0
0 29
1 0
0 30
1 0
0 31
1 0
0 32
1 0
0 33
1 0
0 34
1 0
0 35
1 0
0 36
1 0
0 37
1 0
0 38
1 0
0 39
1 0
0 40
1 0
0 41
1 0
0 42
1 0
0 43
1 0
0 44
1 0
0 45
1 0
0 46
1 0
0 47
1 0
0 48
1 0
0 49
1 0
0 50
1 0
0 51
1 0
0 52
1 0
0 53
1 0
0 54
1 0
0 55
1 0
0 56
1 0
0 57
1 0
0 58
1 0
0 59
1 0
0 60
1 0
0 61
1 0
0 62
1 0
0 63
1 0
0 64
1 0
0 65
1 0
0 66
1 0
0 67
1 0
0 68
1 0
0 69
1 0
0 70
1 0
0 71
1 0
0 72
1 0
0 73
1 0
0 74
1 0
0 75
1 0
0 76
1 0
0 77
1 0
0 78
1 0
0 79
1 0
0 80
1 0
0 81
1 0
0 82
1 0
0 83
1 0
0 84
1 0
0 85
1 0
0 86
1 0
0 87
1 0
0 88
1 0
0 89
1 0
0 90
1 0
0 91
1 0
0 92
1 0
0 93
1 0
0 94
1 0
0 95
1 0
0 96
1 0
0 97
1 0
0 98
1 0
0 99
1 0
0 100
1 0
0 101
1 0
0 102
1 0
0 103
1 0
0 104
1 0
0 105
1 0
0 106
1 0
0 107
1 0
0 108
1 0
0 109
1 0
0 110
1 0
0 111
1 0
0 112
1 0
0 113
1 0
0 114
1 0
0 115
1 0
0 116
1 0
0 117
1 0
0 118
1 0
0 119
1 0
0 120
1 0
0 121
1 0
0 122
1 0
0 123
1 0
0 124
1 0
0 125
1 0
0 126
1 0
0 127
1 0
0 128
1 0
0 129
1 0
0 130
1 0
0 131
1 0
0 132
1 0
0 133
1 0
0 134
1 0
0 135
1 0
0 136
1 0
0 137
1 0
0 138
1 0
0 139
1 0
0 140
1 0
0 141
1 0
0 142
1 0
0 143
1 0
0 144
1 0
0 145
1 0
0 146
1 0
0 147
1 0
0 148
1 0
0 149
1 0
0 150
1 0
0 151
1 0
0 152
1 0
0 153
1 0
0 154
1 0
0 155
1 0
0 156
1 0
0 157
1 0
0 158
1 0
0 159
1 0
0 160
1 0
0 161
1 0
0 162
1 0
0 163
1 0
0 164
1 0
0 165
1 0
0 166
1 0
0 167
1 0
0 168
1 0
0 169
1 0
0 170
1 0
0 171
1 0
0 172
1 0
0 173
1 0
0 174
1 0
0 175
1 0
0 176
1 0
0 177
1 0
0 178
1 0
0 179
1 0
0 180
1 0
0 181
1 0
0 182
1 0
0 183
1 0
0 184
1 0
0 185
1 0
0 186
1 0
0 187
1 0
0 188
1 0
0 189
1 0
0 190
1 0
0 191
1 0
0 192
1 0
0 193
1 0
0 194
1 0
0 195
1 0
0 196
1 0
0 197
1 0
0 198
1 0
0 199
1 0
0 200
1 0
0 201
1 0
0 202
1 0
0 203
1 0
0 204
1 0
0 205
1 0
0 206
1 0
0 207
1 0
0 208
1 0
0 209
1 0
0 210
1 0
0 211
1 0
0 212
1 0
0 213
1 0
0 214
1 0
0 215
1 0
0 216
1 0
0 217
1 0
0 218
1 0
0 219
1 0
0 220
1 0
0 221
1 0
0 222
1 0
0 223
1 0
0 224
1 0
0 225
1 0
0 226
1 0
0 227
1 0
0 228
1 0
0 229
1 0
0 230
1 0
0 231
1 0
0 232
1 0
0 233
1 0
0 234
1 0
0 235
1 0
0 236
1 0
0 237
1 0
0 238
1 0
0 239
1 0
0 240
1 0
0 241
1 0
0 242
1 0
0 243
1 0
0 244
1 0
0 245
1 0
0 246
1 0
0 247
1 0
0 248
1 0
0 249
1 0
0 250
1 0
0 251
1 0
0 252
1 0
0 253
1 0
0 254
1 0
0 255
1 0
End_Of_Object VOfflineBusStatisticSettings 3
VNETOptionsStreamer 3 Begin_Of_Object
4
0
<VFileName V9 QL> 0 "" 
1

0
End_Of_Object VNETOptionsStreamer 3
0
1
VUserFileMgrAnlyz 3 Begin_Of_Object
2
0
0
End_Of_Object VUserFileMgrAnlyz 3
VBasicDiagnosticStreamer 3 Begin_Of_Object
1
0
End_Of_Object VBasicDiagnosticStreamer 3
VCLibraryOptions 3 Begin_Of_Object
1
0
End_Of_Object VCLibraryOptions 3
NValueObjectDisplay::VNameDisplaySettings 3 Begin_Of_Object
3
12
0
4
1
4
2
4
3
4
4
1
5
1
6
4
7
4
8
4
9
6
10
4
11
4
12
0
1
1
1
2
1
3
1
4
1
5
1
6
1
7
1
8
1
9
1
10
1
11
1
12
0
0
1
0
2
0
3
0
4
0
5
0
6
0
7
0
8
0
9
0
10
0
11
0
0
1
9
128
0
End_Of_Object NValueObjectDisplay::VNameDisplaySettings 3
ConfigurationSavedByCANwBeginner 0
VGlobalExportAndLoggingSettings 3 Begin_Of_Object
9
2
1
0
0
6
1
0.10000000000000001
2
0
0.10000000000000001
2
19
0
1
3
1
0
::
,
.
<VFileName V9 QL> 1 "" 
410
0
0
1
6
0
6
0
0
1
2
0
3
6
730
0
0.10000000000000001
0
0
0
VLoggingComment 4 Begin_Of_Object
1
1
VLoggingCommentAttribute 5 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 5
End_Of_Object VLoggingComment 4
End_Of_Object VGlobalExportAndLoggingSettings 3
0
VRTFilterOptions 3 Begin_Of_Object
3
0
0
0
0
0
End_Of_Object VRTFilterOptions 3
VRTTxBufferOptions 3 Begin_Of_Object
2
1
1
500
End_Of_Object VRTTxBufferOptions 3
VRTIRQReductionOptions 3 Begin_Of_Object
2
500
End_Of_Object VRTIRQReductionOptions 3
VPersistentDebuggerOptions 3 Begin_Of_Object
2
64
10000
End_Of_Object VPersistentDebuggerOptions 3
7
0
0
0
0
0
1
1
0
1
VAFDXGlobalSettings 3 Begin_Of_Object
4
1000
15
1
0
0
0
0
End_Of_Object VAFDXGlobalSettings 3
VRTCANErrorFrameOptions 3 Begin_Of_Object
1
1
0
End_Of_Object VRTCANErrorFrameOptions 3
1
ILConfiguration::VProxyManager 3 Begin_Of_Object
1
0
0
End_Of_Object ILConfiguration::VProxyManager 3
4
VGlobalVariableSettings 3 Begin_Of_Object
1
65001
End_Of_Object VGlobalVariableSettings 3
VGeneralLoggingBlockSettings 3 Begin_Of_Object
2
2000
29
0
3
End_Of_Object VGeneralLoggingBlockSettings 3
0
0
43
0
1
SecurityManager::VSecurityManager 3 Begin_Of_Object
3
0
0
NULL
End_Of_Object SecurityManager::VSecurityManager 3
VRTAutosarPDULayerMode 3 Begin_Of_Object
1
2
End_Of_Object VRTAutosarPDULayerMode 3
VErtOptions 3 Begin_Of_Object
2
0
0
End_Of_Object VErtOptions 3
0
1
471
VDistributedDebuggingSettings 3 Begin_Of_Object
1
0
2828
End_Of_Object VDistributedDebuggingSettings 3
VAdditionalLicenseOptions 3 Begin_Of_Object
1
0
End_Of_Object VAdditionalLicenseOptions 3
End_Of_Object VGlobalParameters 2
VDesktopManager 2 Begin_Of_Object
1
1
4
VDesktop 3 Begin_Of_Object
1
Trace
{9A300368-5C43-4C52-8A1F-83F68E1E0BCD}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Configuration
{051F9568-8F91-4D13-B4A8-D82D98D021BA}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{31AD4644-E941-452D-832D-80EF52A05036}" Guid="4007d333-cefc-4f4a-852e-30b62a93a1d7" State="DockableInsideHost" DockedSize="201, 197" FloatingSize="423, 189"><AutoHideStateInfo RootDock="Bottom" /><DockedInsideHostStateInfo RootDock="Bottom" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Analysis
{CBE571D9-1968-4A46-89F5-A949CD457AA6}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Desktop1
{7F93EF3F-D8E5-42F0-9BB1-50011280605B}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
4294967295
4294967295
4294967295
End_Of_Object VDesktopManager 2
2
VDiagnoseBox 2 Begin_Of_Object
1
VDiagnoseBoxRoot 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
1
1 -1 0 1 0 0 0 0 273 105 1093 423
BMS - Diagnostic Console
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{1821E664-0670-4243-8CA0-25AB6975A3C1}
0
End_Of_Object VBoxRoot 4
BMS
DIAGNOSE_CONTROL_END
End_Of_Object VDiagnoseBoxRoot 3
End_Of_Object VDiagnoseBox 2
VErrorMemoryBox 2 Begin_Of_Object
1
VDiagnoseBoxRoot 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
1
1 -1 0 1 0 0 0 0 273 105 1093 423
BMS - Fault Memory
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{D095C46D-3168-4A7E-AEF1-788E5D621F46}
0
End_Of_Object VBoxRoot 4
BMS
DIAGNOSE_CONTROL_END
End_Of_Object VDiagnoseBoxRoot 3
End_Of_Object VErrorMemoryBox 2
VGBAnlyzBox 2 Begin_Of_Object
3
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
0 2 0 1 -1 -1 -8 -31 775 0 1275 588

1

MDI_DOCK_INFO_END
5
1
6
2 3 -1 -1 -1 -1 22 22 953 535
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
7
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 775 0 1275 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 22 22 953 535
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 775 0 1275 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 0 0 0 0 0 0 0 0 0
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 775 0 1275 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
4
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{E8FCB431-7366-42DA-A18A-A2422C1CD152}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
4
1
95
VDAOGBFunctionBlock 4 Begin_Of_Object
1
43
0
TABPredecessor:
0
TABSuccessor:
44
VPlugConf 5 Begin_Of_Object
1
End_Of_Object VPlugConf 5
VDAOGBFunctionBlock 5 Begin_Of_Object
1
44
0
TABPredecessor:
43
TABSuccessor:
90
VChannelFilterConfiguration 6 Begin_Of_Object
3
VMigratedGenericConfiguration<class_VChannelFilterData> 7 Begin_Of_Object
1
VChannelFilterData 8 Begin_Of_Object
2
1
VBusChannelData 9 Begin_Of_Object
1
2
2
1
2
1
1
End_Of_Object VBusChannelData 9
End_Of_Channel_Data
1
End_Of_Object VChannelFilterData 8
End_Of_Object VMigratedGenericConfiguration<class_VChannelFilterData> 7
0
0
End_Of_Object VChannelFilterConfiguration 6
VDOCrossing 6 Begin_Of_Object
2
45
0
VDAOGBHSStd 7 Begin_Of_Object
1
46
0
0 0
TABPredecessor:
95
TABSuccessor:
48
VDODynamicLine 8 Begin_Of_Object
1
47
0
0
VDAOSwitch 9 Begin_Of_Object
1
48
0
TABPredecessor:
46
TABSuccessor:
49
VDAOGBHSStd 10 Begin_Of_Object
1
49
0
0 0
TABPredecessor:
48
TABSuccessor:
51
VDODynamicLine 11 Begin_Of_Object
1
50
0
0
VDOFRamification 12 Begin_Of_Object
1
51
0
TABPredecessor:
49
TABSuccessor:
53
7
VDORefinement 13 Begin_Of_Object
1
52
0
13
VDAOGBHSStd 14 Begin_Of_Object
1
53
0
0 0
TABPredecessor:
51
TABSuccessor:
55
VDODynamicLine 15 Begin_Of_Object
1
54
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
55
0
TABPredecessor:
53
TABSuccessor:
58
VNetMSControlConfiguration 17 Begin_Of_Object
1
VNETControlConfiguration 18 Begin_Of_Object
1
VConfigurationRoot 19 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 19
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 2 0 1 -1 -1 -1 -1 0 0 512 362
CAN Statistics
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1920 527
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 362
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 362
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 362
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{DF1D1FB8-3F6B-4B58-9502-C920BE0A79B0}
0
End_Of_Object VBoxRoot 21
1 2 0 1 -1 -1 -1 -1 0 0 482 266
End_Of_Object VUniqueBox 20
1
1 2 0 1 -1 -1 -1 -1 0 0 482 266
0
End_Of_Object VNETControlBox 19
135
APPDIR Vector.CANalyzer.CAN.StatisticsMonitor.DLL
Vector.CANalyzer.CAN.StatisticsMonitor, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.StatisticsMonitor.StatisticsMonitor
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
App
2
UInt16
Channel
1
Array
States
3

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.UInt64
3
1
0
19
UInt64
ArrayElement
65536
UInt64
ArrayElement
65537
UInt64
ArrayElement
196611
UInt64
ArrayElement
196624
UInt64
ArrayElement
65540
UInt64
ArrayElement
196616
UInt64
ArrayElement
65542
UInt64
ArrayElement
65543
UInt64
ArrayElement
196611
UInt64
ArrayElement
65545
UInt64
ArrayElement
196619
UInt64
ArrayElement
196613
UInt64
ArrayElement
18
UInt64
ArrayElement
17
UInt64
ArrayElement
65548
UInt64
ArrayElement
196623
UInt64
ArrayElement
65550
UInt64
ArrayElement
196618
UInt64
ArrayElement
196621
UInt64
ArrayElement
131071
Array
ColumnWidths
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Int32
4
1
0
4
Int32
ArrayElement
160
Int32
ArrayElement
80
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
UInt64
TimerInterval
10
UInt64
TimerIntervalMs
1050
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
4
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETControlConfiguration 18
End_Of_Object VNetMSControlConfiguration 17
VDOLine 17 Begin_Of_Object
1
56
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
57
0
7
VDAOGBHSStd 14 Begin_Of_Object
1
58
0
0 0
TABPredecessor:
55
TABSuccessor:
60
VDODynamicLine 15 Begin_Of_Object
1
59
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
60
0
TABPredecessor:
58
TABSuccessor:
63
VTraceConfiguration 17 Begin_Of_Object
1
VTraceControlCfg 18 Begin_Of_Object
9
VTraceSearchCfg 19 Begin_Of_Object
1
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
0
End_Of_Object VTraceSearchCfg 19
VTraceFilterCfg 19 Begin_Of_Object
1
0
1
VTraceAnalysisFilterGroup 20 Begin_Of_Object
2
1
Filter Group 0
2
VTraceAnalysisSingleFilter 21 Begin_Of_Object
4
1
1
0
End_Of_Object VTraceAnalysisSingleFilter 21
VTraceAnalysisSingleFilter 21 Begin_Of_Object
4
0
0
0
End_Of_Object VTraceAnalysisSingleFilter 21
1
End_Of_Object VTraceAnalysisFilterGroup 20
End_Of_Object VTraceFilterCfg 19
1
0
0
0
43
0
0
1
1
18
ver=5: FT FT FT FT FT FT
End_Of_Serialized_Data 18
2
0
3
1
18
ver=3;BMS:ver=5: FT FT FT FT FT
End_Of_Serialized_Data 18
4
0
5
0
6
1
18
GFver=5;ver=5: FF TF TF FF FF FF FF FF FF TF;F T DiVa;T F _Statistics;F T CANoe;F T VTS
End_Of_Serialized_Data 18
7
0
8
0
9
0
10
0
11
1
18
ver=5: FT FT FT FT
End_Of_Serialized_Data 18
12
0
13
0
14
0
15
0
16
0
17
0
18
0
19
0
20
0
21
0
22
0
23
0
24
0
25
0
26
0
27
0
28
0
29
0
30
0
31
0
32
0
33
0
34
0
35
0
36
0
37
0
38
0
39
0
40
0
41
0
42
0
0
1
VTraceColumnConfiguration 19 Begin_Of_Object
4
3
Initial
142
VTNColumnData 20 Begin_Of_Object
4
0
104
0
Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
1
37
1
Chn
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
2
45
2
ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
3
120
3
Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
4
170
-1
ID / Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
5
43
5
Dir
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
6
35
6
DLC
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
7
270
8
Data
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
8
37
-1
Attr
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
9
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
10
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
11
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
12
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
13
100
-1
Frame Duration
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
14
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
15
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
16
100
4
Event Type
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
17
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
18
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
19
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
20
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
21
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
22
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
23
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
24
100
-1
Bus Idle
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
25
100
-1
Bus Busy
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
26
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
27
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
28
110
-1
d:hh:mm:ss
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
29
85
-1
Diff Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
30
50
-1
Bustype
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
31
120
-1
Sender Node
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
32
50
-1
Bus
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
33
80
-1
Database
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
34
50
-1
Counter
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
35
100
-1
Start of Frame
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
36
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
37
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
38
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
39
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
40
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
41
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
42
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
43
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
44
50
-1
Arb. Field ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
45
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
46
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
47
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
48
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
49
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
50
150
-1
Data ASCII
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
51
100
-1
Comment
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
52
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
53
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
54
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
55
120
-1
Diff Time (Ref. Event)
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
56
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
57
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
58
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
59
120
-1
Date and Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
60
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
61
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
62
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
63
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
64
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
65
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
66
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
67
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
68
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
69
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
70
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
71
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
72
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
73
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
74
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
75
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
76
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
77
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
78
35
7
Data length
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
79
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
80
80
-1
XCP/CCP Command
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
81
80
-1
XCP/CCP Parameter
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
82
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
83
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
84
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
85
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
86
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
87
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
88
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
89
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
90
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
91
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
92
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
93
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
94
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
95
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
96
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
97
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
98
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
99
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
100
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
101
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
102
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
103
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
104
120
-1
Diff Time (last Occ.)
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
105
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
106
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
107
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
108
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
109
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
110
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
111
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
112
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
113
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
114
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
115
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
116
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
117
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
118
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
119
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
120
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
121
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
122
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
123
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
124
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
125
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
126
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
127
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
128
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
129
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
130
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
131
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
132
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
133
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
134
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
135
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
136
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
137
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
138
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
139
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
140
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
141
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
End_Of_Object VTraceColumnConfiguration 19
0
0
VTraceControlFixedModeExpansionItems 19 Begin_Of_Object
3
0
End_Of_Object VTraceControlFixedModeExpansionItems 19
18
C:\Users\<USER>\Downloads\PYTHON FILES\BATCH_CONVERSION_MULTI\config
End_Of_Serialized_Data 18
18
%s
End_Of_Serialized_Data 18
18

End_Of_Serialized_Data 18
0
2
1
1
18
VLogExportPersister 19 Begin_Of_Object
7
1416
11062253
%s
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19

End_Of_Serialized_Data 18
1
0
0
290
0
150
<VFileName V9 QL> 1 "" 
3
End_Of_Object VTraceControlCfg 18
VNETTraceControlBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 -1 2 3 -1 -1 -11 -45 384 105 1370 761
Trace 2
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 384 105 1370 761
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
1
1920 527
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{C098173F-7349-46CA-B381-BDF5CA34C1CC}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETTraceControlBox 18
End_Of_Object VTraceConfiguration 17
VDOLine 17 Begin_Of_Object
1
61
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
62
0
7
VDAOGBHSStd 14 Begin_Of_Object
1
63
0
0 0
TABPredecessor:
60
TABSuccessor:
65
VDODynamicLine 15 Begin_Of_Object
1
64
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
65
0
TABPredecessor:
63
TABSuccessor:
68
VTraceConfiguration 17 Begin_Of_Object
1
VTraceControlCfg 18 Begin_Of_Object
9
VTraceSearchCfg 19 Begin_Of_Object
1
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
0
End_Of_Object VTraceSearchCfg 19
VTraceFilterCfg 19 Begin_Of_Object
1
0
1
VTraceAnalysisFilterGroup 20 Begin_Of_Object
2
1
Filter Group 0
2
VTraceAnalysisSingleFilter 21 Begin_Of_Object
4
1
1
0
End_Of_Object VTraceAnalysisSingleFilter 21
VTraceAnalysisSingleFilter 21 Begin_Of_Object
4
0
0
0
End_Of_Object VTraceAnalysisSingleFilter 21
1
End_Of_Object VTraceAnalysisFilterGroup 20
End_Of_Object VTraceFilterCfg 19
1
0
0
0
43
0
0
1
1
18
ver=5: FT FT FT FT FT FT
End_Of_Serialized_Data 18
2
0
3
1
18
ver=3;BMS:ver=5: FT FT FT FT FT
End_Of_Serialized_Data 18
4
0
5
0
6
1
18
GFver=5;ver=5: FT TF TF FT FT FF FF FF FF TF;F T DiVa;T F _Statistics;F T CANoe;F T VTS
End_Of_Serialized_Data 18
7
0
8
0
9
0
10
0
11
1
18
ver=5: FT FT FT FT
End_Of_Serialized_Data 18
12
0
13
0
14
0
15
0
16
0
17
0
18
0
19
0
20
0
21
0
22
0
23
0
24
0
25
0
26
0
27
0
28
0
29
0
30
0
31
0
32
0
33
0
34
0
35
0
36
0
37
0
38
0
39
0
40
0
41
0
42
0
0
1
VTraceColumnConfiguration 19 Begin_Of_Object
4
1
Initial
142
VTNColumnData 20 Begin_Of_Object
4
0
104
0
Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
1
57
1
Chn
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
2
45
2
ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
3
120
3
Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
4
170
-1
ID / Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
5
43
5
Dir
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
6
45
6
DLC
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
7
270
7
Data
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
8
37
-1
Attr
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
9
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
10
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
11
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
12
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
13
150
-1
Frame Duration
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
14
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
15
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
16
70
4
Event Type
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
17
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
18
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
19
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
20
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
21
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
22
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
23
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
24
85
-1
Bus Idle
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
25
85
-1
Bus Busy
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
26
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
27
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
28
110
-1
HH:MM:SS
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
29
85
-1
Diff time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
30
50
-1
Bustype
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
31
120
-1
Send node
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
32
50
-1
Bus
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
33
80
-1
Database
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
34
50
-1
Counter
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
35
90
-1
Start of Frame
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
36
50
-1
PGN
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
37
50
-1
DP
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
38
50
-1
Prio
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
39
50
-1
Specific 10
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
40
50
-1
Specific 11
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
41
50
-1
Specific 12
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
42
50
-1
Specific 13
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
43
50
-1
Specific 14
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
44
50
-1
Specific 15
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
45
50
-1
Specific 16
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
46
50
-1
Specific 17
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
47
50
-1
Specific 18
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
48
50
-1
Specific 19
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
49
50
-1
Specific 20
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
50
150
-1
Data ASCII
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
51
150
-1
Comment
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
52
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
53
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
54
50
-1
Namespace
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
55
120
-1
Diff time (ref. event)
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
56
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
57
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
58
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
59
120
-1
Date / Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
60
50
-1
Receive Node
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
61
50
-1
VL No.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
62
200
-1
Sub VL ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
63
50
-1
Transmitter
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
64
200
-1
Receiver
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
65
50
-1
Tx Port ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
66
50
-1
Rx Port ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
67
65
-1
Port Char.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
68
50
-1
IP Frag.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
69
150
-1
Master Port Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
70
65
-1
BAG
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
71
65
-1
Seq. No.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
72
50
-1
Line
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
73
50
-1
Interface ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
74
50
-1
Equipment
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
75
50
-1
Partition
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
76
50
-1
BRS
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
77
50
-1
ESI
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
78
45
-1
Data length
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
79
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
80
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
81
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
82
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
83
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
84
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
85
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
86
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
87
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
88
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
89
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
90
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
91
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
92
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
93
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
94
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
95
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
96
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
97
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
98
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
99
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
100
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
101
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
102
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
103
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
104
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
105
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
106
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
107
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
108
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
109
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
110
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
111
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
112
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
113
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
114
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
115
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
116
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
117
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
118
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
119
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
120
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
121
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
122
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
123
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
124
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
125
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
126
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
127
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
128
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
129
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
130
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
131
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
132
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
133
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
134
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
135
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
136
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
137
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
138
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
139
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
140
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
141
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
End_Of_Object VTraceColumnConfiguration 19
0
0
VTraceControlFixedModeExpansionItems 19 Begin_Of_Object
3
4
VConfigMessage 20 Begin_Of_Object
5
VConfigBusObject 21 Begin_Of_Object
1
VConfigBusEvent 22 Begin_Of_Object
1
VConfigEvent 23 Begin_Of_Object
1
End_Of_Object VConfigEvent 23
1
1
End_Of_Object VConfigBusEvent 22
End_Of_Object VConfigBusObject 21
9
1
VDatabaseBusMessageId 21 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 21
NULL
0
0
0
End_Of_Object VConfigMessage 20
VConfigMessage 20 Begin_Of_Object
5
VConfigBusObject 21 Begin_Of_Object
1
VConfigBusEvent 22 Begin_Of_Object
1
VConfigEvent 23 Begin_Of_Object
1
End_Of_Object VConfigEvent 23
1
1
End_Of_Object VConfigBusEvent 22
End_Of_Object VConfigBusObject 21
9
1
VDatabaseBusMessageId 21 Begin_Of_Object
3
820
1
1
0
0
0
MCU_Fault_Data2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 21
NULL
0
0
0
End_Of_Object VConfigMessage 20
VConfigMessage 20 Begin_Of_Object
5
VConfigBusObject 21 Begin_Of_Object
1
VConfigBusEvent 22 Begin_Of_Object
1
VConfigEvent 23 Begin_Of_Object
1
End_Of_Object VConfigEvent 23
1
1
End_Of_Object VConfigBusEvent 22
End_Of_Object VConfigBusObject 21
9
1
VDatabaseBusMessageId 21 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 21
NULL
0
0
0
End_Of_Object VConfigMessage 20
VConfigMessage 20 Begin_Of_Object
5
VConfigBusObject 21 Begin_Of_Object
1
VConfigBusEvent 22 Begin_Of_Object
1
VConfigEvent 23 Begin_Of_Object
1
End_Of_Object VConfigEvent 23
1
1
End_Of_Object VConfigBusEvent 22
End_Of_Object VConfigBusObject 21
9
1
VDatabaseBusMessageId 21 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 21
NULL
0
0
0
End_Of_Object VConfigMessage 20
0
0
0
0
End_Of_Object VTraceControlFixedModeExpansionItems 19
18
C:\Users\<USER>\Downloads\PYTHON FILES\BATCH_CONVERSION_MULTI\15 (x64)\Templates\CANoe
End_Of_Serialized_Data 18
18
Trace-Fenster
End_Of_Serialized_Data 18
18

End_Of_Serialized_Data 18
0
776880451
1
1
18
VLogExportPersister 19 Begin_Of_Object
7
1416
44616685
Trace-Fenster
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19

End_Of_Serialized_Data 18
0
0
0
290
0
150
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe" 
1
End_Of_Object VTraceControlCfg 18
VNETTraceControlBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 7 3 3 0 1031 -8 -31 431 301 1013 595
Trace
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 1275 541
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
5
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
3 3 0 1031 -8 -31 431 301 1013 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
7
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
2 3 0 1031 -1 -1 431 301 1013 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
6
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 431 301 1013 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
7
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{31FB7B33-3D93-4EE7-A007-8DB311A944A4}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETTraceControlBox 18
End_Of_Object VTraceConfiguration 17
VDOLine 17 Begin_Of_Object
1
66
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
67
0
4
VDAOGBHSStd 14 Begin_Of_Object
1
68
0
0 0
TABPredecessor:
65
TABSuccessor:
70
VDODynamicLine 15 Begin_Of_Object
1
69
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
70
0
TABPredecessor:
68
TABSuccessor:
73
VNETDataListControlHost 17 Begin_Of_Object
1
VConfigurationRoot 18 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 18
VNETDataBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 3 0 1 -1 -1 -1 -1 513 0 1030 265
Data
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 423 193
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1013 595
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 0 0 423 193
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1920 732
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 513 0 1030 265
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 513 0 1030 265
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{AA76F618-2087-4A48-B611-BF29871F1267}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETDataBox 18
1
9
0
20 150 16 100 40 40 50 100 60 100 1
35 35
30
70 70 70 100
100
1 1 0 1 0 0 1 1 1 0 1
0 0
0
0 0 0 0
0
1 0
5000 0 10000 0 10000
1 0
VLogCfgData 18 Begin_Of_Object
12
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
0
29
1
1
0
2
0
0
18
VLogExportPersister 19 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19

End_Of_Serialized_Data 18
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Data.mdf" 
0
1
0
10
80
410
1
1
0


<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Data.mdf" 
1
0
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
0
2f73645e-10d4-45f0-82a7-ada61fa5f302
1
VLoggingComment 19 Begin_Of_Object
1
1
VLoggingCommentAttribute 20 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 20
End_Of_Object VLoggingComment 19
End_Of_Object VLogCfgData 18
1
[End_of_Control]
End_Of_Object VNETDataListControlHost 17
VDOLine 17 Begin_Of_Object
1
71
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
72
0
5
VDAOGBHSStd 14 Begin_Of_Object
1
73
0
0 0
TABPredecessor:
70
TABSuccessor:
75
VDODynamicLine 15 Begin_Of_Object
1
74
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
75
0
TABPredecessor:
73
TABSuccessor:
78
VGraphBoxConf 17 Begin_Of_Object
1
VNETGraphBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 6 0 1 -1 -1 -8 -31 200 118 801 474
Graphics
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
4
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
6
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 363 1275 836
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
4
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 0 363 1275 836
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
4
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 363 1275 836
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{DDA24D23-CEC5-4F19-BC3F-BFC4A28BA823}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETGraphBox 18
81
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
Expanded
True
Int32
SplitterWidth
604
Int32
SplitterHeight
80
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.Position
3
LegendPosition
3
Int32
value__
0
--TextFormatter: End of Object--
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.GraphicCommandID
4
ScrollSignalsButton1Command
4
Int32
value__
29
--TextFormatter: End of Object--
TypeRef:4
ScrollSignalsButton2Command
5
Int32
value__
30
--TextFormatter: End of Object--
TypeRef:4
ScrollButton1Command
6
Int32
value__
35
--TextFormatter: End of Object--
TypeRef:4
ScrollButton2Command
7
Int32
value__
36
--TextFormatter: End of Object--
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
VSignalObjectStreamer 18 Begin_Of_Object
1
121
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
AUX_BATTERY_CHARGING_STATUS
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1726
1
1
0
0
0
Digital_Input_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
AUX_BATTERY_CHARGING_STATUS
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1726
Digital_Input_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
AUX_BATTERY_CHARGING_STATUS
"" 159 800080 -6.9388939e-018 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_charge_inhibit_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_charge_inhibit_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_charge_inhibit_00
"" 159 8080 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_charge_inhibit_01
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_charge_inhibit_01
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_charge_inhibit_01
"" 159 8000 0. 1. -100. 100. 1 0 0 2 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_charge_inhibit_02
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_charge_inhibit_02
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_charge_inhibit_02
"" 159 80 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_charge_inhibit_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
284
1
1
0
0
0
BMS00_Parameter_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_charge_inhibit_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
284
BMS00_Parameter_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_charge_inhibit_10ms
"" 159 800000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Derate_Charge_Current_Flag_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Derate_Charge_Current_Flag_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Derate_Charge_Current_Flag_00
"" 159 808000 -2.3533914 5.3782854 -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Derate_Charge_Current_Flag_01
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Derate_Charge_Current_Flag_01
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Derate_Charge_Current_Flag_01
"" 159 969696 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Derate_Charge_Current_Flag_02
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Derate_Charge_Current_Flag_02
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Derate_Charge_Current_Flag_02
"" 159 0 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Derate_Charge_Current_Flag_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
284
1
1
0
0
0
BMS00_Parameter_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Derate_Charge_Current_Flag_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
284
BMS00_Parameter_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Derate_Charge_Current_Flag_10ms
"" 159 66ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_discharge_inhibit_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_discharge_inhibit_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_discharge_inhibit_00
"" 159 ff99cc 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_discharge_inhibit_01
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_discharge_inhibit_01
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_discharge_inhibit_01
"" 159 663399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_discharge_inhibit_02
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_discharge_inhibit_02
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_discharge_inhibit_02
"" 159 ffcc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_discharge_inhibit_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
284
1
1
0
0
0
BMS00_Parameter_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_discharge_inhibit_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
284
BMS00_Parameter_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_discharge_inhibit_10ms
"" 159 669933 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Precharge_failure_status_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Precharge_failure_status_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Precharge_failure_status_00
"" 159 3399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Precharge_failure_status_01
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Precharge_failure_status_01
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Precharge_failure_status_01
"" 159 cccc33 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Precharge_failure_status_02
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
290
1
1
0
0
0
Overall_Battery_Fault
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Precharge_failure_status_02
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
290
Overall_Battery_Fault
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Precharge_failure_status_02
"" 159 5a5a5a 0.13604544 0.9497155 -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Precharge_failure_status_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
284
1
1
0
0
0
BMS00_Parameter_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Precharge_failure_status_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
284
BMS00_Parameter_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Precharge_failure_status_10ms
"" 159 ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Low_temperature_during_charging_Error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Low_temperature_during_charging_Error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Low_temperature_during_charging_Error
"" 159 cc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Low_temperature_during_charging_warning_info
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Low_temperature_during_charging_warning_info
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Low_temperature_during_charging_warning_info
"" 159 ff00ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_current_charge_Error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_current_charge_Error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_current_charge_Error
"" 159 ff0000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_current_charge_Warning
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_current_charge_Warning
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_current_charge_Warning
"" 159 800080 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_temperature_charge_Error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_temperature_charge_Error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_temperature_charge_Error
"" 159 8080 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_temperature_charge_warning_info
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_temperature_charge_warning_info
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_temperature_charge_warning_info
"" 159 8000 -28.046392 77.614441 -100. 100. 20 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_voltage_charge_Error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_voltage_charge_Error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_voltage_charge_Error
"" 159 80 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_voltage_charge_Permanent_Fault
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_voltage_charge_Permanent_Fault
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_voltage_charge_Permanent_Fault
"" 159 800000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Over_voltage_charge_warning_info
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Over_voltage_charge_warning_info
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Over_voltage_charge_warning_info
"" 159 808000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Precharge_failure
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Precharge_failure
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Precharge_failure
"" 159 969696 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Battery_Temperature_Blanket_Cut_off_Grade4
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Battery_Temperature_Blanket_Cut_off_Grade4
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Battery_Temperature_Blanket_Cut_off_Grade4
"" 159 8080 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
CellDip_Error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
CellDip_Error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
CellDip_Error
"" 159 8000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
CellDip_Fault
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
CellDip_Fault
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
CellDip_Fault
"" 159 80 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
DC_Bus_undervoltage_fault_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
DC_Bus_undervoltage_fault_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
DC_Bus_undervoltage_fault_Grade_3
"" 159 800000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Drive_Overload_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Drive_Overload_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Drive_Overload_Grade_3
"" 159 808000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Drive_overtemperature_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Drive_overtemperature_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Drive_overtemperature_Grade_3
"" 159 969696 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Encoder_disconnection_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Encoder_disconnection_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Encoder_disconnection_Grade_3
"" 159 0 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Motor_over_speed_fault_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Motor_over_speed_fault_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Motor_over_speed_fault_Grade_3
"" 159 66ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Motor_overtemperature_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Motor_overtemperature_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Motor_overtemperature_Grade_3
"" 159 ff99cc 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Motor_temperature_sensor_disconnection_fault_Grade_2
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Motor_temperature_sensor_disconnection_fault_Grade_2
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Motor_temperature_sensor_disconnection_fault_Grade_2
"" 159 663399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Overvoltagebaseline_of_hardware_is_wrong_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Overvoltagebaseline_of_hardware_is_wrong_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Overvoltagebaseline_of_hardware_is_wrong_Grade_3
"" 159 ffcc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
SOCRegenWarn
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
SOCRegenWarn
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
SOCRegenWarn
"" 159 669933 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Software_overcurrent_protection_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Software_overcurrent_protection_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Software_overcurrent_protection_Grade_3
"" 159 3399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Software_overvoltage_fault_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Software_overvoltage_fault_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Software_overvoltage_fault_Grade_3
"" 159 cccc33 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Stalling_Fault_Grade_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
819
1
1
0
0
0
MCU_Fault_Data
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Stalling_Fault_Grade_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
819
MCU_Fault_Data
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Stalling_Fault_Grade_3
"" 159 5a5a5a 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
TempRegen_Warning
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
TempRegen_Warning
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
TempRegen_Warning
"" 159 ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Vehicle_Mode_Level_1
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
323
1
1
0
0
0
Vehicle_Mode
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Vehicle_Mode_Level_1
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
323
Vehicle_Mode
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Vehicle_Mode_Level_1
"" 159 cc99 0. 15. -100. 100. 2 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Vehicle_Mode_Level_2
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
323
1
1
0
0
0
Vehicle_Mode
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Vehicle_Mode_Level_2
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
323
Vehicle_Mode
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Vehicle_Mode_Level_2
"" 159 ff00ff 0. 15. -100. 100. 2 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Vehicle_Mode_Level_3
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
323
1
1
0
0
0
Vehicle_Mode
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Vehicle_Mode_Level_3
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
323
Vehicle_Mode
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Vehicle_Mode_Level_3
"" 159 ff0000 0. 15. -100. 100. 2 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Vehicle_Mode_Level_4
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
323
1
1
0
0
0
Vehicle_Mode
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Vehicle_Mode_Level_4
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
323
Vehicle_Mode
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Vehicle_Mode_Level_4
"" 159 800080 0. 15. -100. 100. 2 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Precharge_failure_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1600
1
1
0
0
0
BMS00_Parameter_7
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Precharge_failure_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1600
BMS00_Parameter_7
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Precharge_failure_10ms
"" 159 0 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Precharge_too_fast_Info
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Precharge_too_fast_Info
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Precharge_too_fast_Info
"" 159 66ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Precharge_too_fast_Info_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1600
1
1
0
0
0
BMS00_Parameter_7
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Precharge_too_fast_Info_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1600
BMS00_Parameter_7
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Precharge_too_fast_Info_10ms
"" 159 ff99cc 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Precharge_too_slow_Info
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1040
1
1
0
0
0
BMS00_Fault_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Precharge_too_slow_Info
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1040
BMS00_Fault_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Precharge_too_slow_Info
"" 159 663399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
BMS00_Precharge_too_slow_Info_10ms
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1600
1
1
0
0
0
BMS00_Parameter_7
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
BMS00_Precharge_too_slow_Info_10ms
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1600
BMS00_Parameter_7
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
BMS00_Precharge_too_slow_Info_10ms
"" 159 ffcc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_AmpHr_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1607
1
1
0
0
0
AmpHr_Data_00
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_AmpHr_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1607
AmpHr_Data_00
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_AmpHr_00
"Amphr" 1 669933 0. 6553. -100. 100. 1000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_control_message_timeout_error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_control_message_timeout_error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_control_message_timeout_error
"" 159 3399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_current_limit_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
304
1
1
0
0
0
BMS00_Parameter_Limit_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_current_limit_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
304
BMS00_Parameter_Limit_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_current_limit_00
"Amp" 1 cccc33 0. 300. -100. 100. 50 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_MOSFET_Enable
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1754
1
1
0
0
0
BMS00_Cloud_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_MOSFET_Enable
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1754
BMS00_Cloud_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_MOSFET_Enable
"" 159 5a5a5a 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_Over_Current
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1754
1
1
0
0
0
BMS00_Cloud_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_Over_Current
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1754
BMS00_Cloud_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_Over_Current
"" 159 ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_Switch_Demand_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1600
1
1
0
0
0
BMS00_Parameter_7
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_Switch_Demand_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1600
BMS00_Parameter_7
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_Switch_Demand_00
"" 159 cc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_TTL
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
851
1
1
0
0
0
Display_Info
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_TTL
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
851
Display_Info
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_TTL
"" 159 ff00ff 0. 3. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charge_Voltage_limit_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
304
1
1
0
0
0
BMS00_Parameter_Limit_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charge_Voltage_limit_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
304
BMS00_Parameter_Limit_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charge_Voltage_limit_00
"V" 1 ff0000 0. 65. -100. 100. 10 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Connected
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1581
1
1
0
0
0
Charger_Connection_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Connected
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1581
Charger_Connection_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Connected
"" 159 800080 0. 3. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Mode
"" 159 8080 0. 7. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Mode_Request
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
288
1
1
0
0
0
Overall_Battery_Status_Info
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Mode_Request
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
288
Overall_Battery_Status_Info
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Mode_Request
"" 159 8000 0. 7. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_output_Short_circuit_error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_output_Short_circuit_error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_output_Short_circuit_error
"" 159 80 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_over_temperature_error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_over_temperature_error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_over_temperature_error
"" 159 800000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Plug_Sense
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1754
1
1
0
0
0
BMS00_Cloud_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Plug_Sense
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1754
BMS00_Cloud_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Plug_Sense
"" 159 808000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
CHARGER_PLUG_SENSE_STATUS
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1726
1
1
0
0
0
Digital_Input_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
CHARGER_PLUG_SENSE_STATUS
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1726
Digital_Input_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
CHARGER_PLUG_SENSE_STATUS
"" 159 969696 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Plugged_In
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1581
1
1
0
0
0
Charger_Connection_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Plugged_In
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1581
Charger_Connection_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Plugged_In
"" 159 0 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Retrigger
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigMessage 23 Begin_Of_Object
5
VConfigBusObject 24 Begin_Of_Object
1
VConfigBusEvent 25 Begin_Of_Object
1
VConfigEvent 26 Begin_Of_Object
1
End_Of_Object VConfigEvent 26
1
0
End_Of_Object VConfigBusEvent 25
End_Of_Object VConfigBusObject 24
0
1
VDatabaseBusMessageId 24 Begin_Of_Object
3
4294967295
1
0
0
0
0

0

0
0
End_Of_Object VDatabaseBusMessageId 24
NULL
0
0
0
End_Of_Object VConfigMessage 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Retrigger
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_19
VCU
1649
VCU_ASW_Debug_Message2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Retrigger
"" 159 66ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Sleep_Request
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigMessage 23 Begin_Of_Object
5
VConfigBusObject 24 Begin_Of_Object
1
VConfigBusEvent 25 Begin_Of_Object
1
VConfigEvent 26 Begin_Of_Object
1
End_Of_Object VConfigEvent 26
1
0
End_Of_Object VConfigBusEvent 25
End_Of_Object VConfigBusObject 24
0
1
VDatabaseBusMessageId 24 Begin_Of_Object
3
4294967295
1
0
0
0
0

0

0
0
End_Of_Object VDatabaseBusMessageId 24
NULL
0
0
0
End_Of_Object VConfigMessage 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Sleep_Request
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_19
VCU
1649
VCU_ASW_Debug_Message2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Sleep_Request
"" 159 ff99cc 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_Status_Signal
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_Status_Signal
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_Status_Signal
"" 159 663399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_under_temperature_error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_under_temperature_error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_under_temperature_error
"" 159 ffcc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charger_USB_12V_Supply_Control_Command
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1727
1
1
0
0
0
Digital_Output_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charger_USB_12V_Supply_Control_Command
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1727
Digital_Output_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charger_USB_12V_Supply_Control_Command
"" 159 669933 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
ChargerReAuthError
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
ChargerReAuthError
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
ChargerReAuthError
"" 159 3399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
ChargerReAuthWarning
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
ChargerReAuthWarning
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
ChargerReAuthWarning
"" 159 cccc33 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_Current_Available_range
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_Current_Available_range
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_Current_Available_range
"Amp" 1 5a5a5a 0. 500. -100. 100. 100 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_Mode_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
304
1
1
0
0
0
BMS00_Parameter_Limit_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_Mode_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
304
BMS00_Parameter_Limit_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_Mode_00
"" 159 ff 0. 7. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_Rate
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
384
1
1
0
0
0
BMS00_Parameter_5
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_Rate
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
384
BMS00_Parameter_5
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_Rate
"%" 159 cc99 0. 100. -100. 100. 20 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_status_of_AUX_battery
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
323
1
1
0
0
0
Vehicle_Mode
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_status_of_AUX_battery
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
323
Vehicle_Mode
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_status_of_AUX_battery
"" 159 ff00ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_system_error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1288
1
1
0
0
0
Fast_Charger_Parameter_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_system_error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1288
Fast_Charger_Parameter_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_system_error
"" 159 ff0000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_time_Max
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
384
1
1
0
0
0
BMS00_Parameter_5
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_time_Max
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
384
BMS00_Parameter_5
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_time_Max
"Min" 159 800080 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Charging_Voltage_Available_range
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Charging_Voltage_Available_range
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Charging_Voltage_Available_range
"V" 1 8080 0. 120. -100. 100. 20 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_AmpHr_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1607
1
1
0
0
0
AmpHr_Data_00
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_AmpHr_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1607
AmpHr_Data_00
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_AmpHr_00
"Amphr" 1 8000 -6553. 5.0000001e-002 -100. 100. 1000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_current_limit_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
304
1
1
0
0
0
BMS00_Parameter_Limit_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_current_limit_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
304
BMS00_Parameter_Limit_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_current_limit_00
"Amp" 1 80 -400. 0. -100. 100. 50 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
347
1
1
0
0
0
Vehicle_Discharge_Mode_Request
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
347
Vehicle_Discharge_Mode_Request
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_Mode
"" 159 800000 0. 15. -100. 100. 2 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_MOSFET_Enable
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1754
1
1
0
0
0
BMS00_Cloud_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_MOSFET_Enable
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1754
BMS00_Cloud_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_MOSFET_Enable
"" 159 808000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_Over_current
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1754
1
1
0
0
0
BMS00_Cloud_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_Over_current
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1754
BMS00_Cloud_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_Over_current
"" 159 969696 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_Power_Limit_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
320
1
1
0
0
0
BMS00_Parameter_Limit_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_Power_Limit_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
320
BMS00_Parameter_Limit_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_Power_Limit_00
"Watts" 223 0 -50000. 0. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Discharge_Switch_Demand_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1600
1
1
0
0
0
BMS00_Parameter_7
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Discharge_Switch_Demand_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1600
BMS00_Parameter_7
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Discharge_Switch_Demand_00
"" 159 66ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Factored_Charge_Amphr
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1752
1
1
0
0
0
BMS00_Cloud_Data_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Factored_Charge_Amphr
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1752
BMS00_Cloud_Data_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Factored_Charge_Amphr
"Amphr" 1 ff99cc 0. 100. -100. 100. 20 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Factored_Discharge_Amphr
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1760
1
1
0
0
0
BMS00_Cloud_Data_5
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Factored_Discharge_Amphr
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1760
BMS00_Cloud_Data_5
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Factored_Discharge_Amphr
"Amphr" 1 663399 -6553. 0.5 -100. 100. 1000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Fast_Charge_Terminate_Request
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
20
1
1
0
0
0
Fast_Charger_Connection_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Fast_Charge_Terminate_Request
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
20
Fast_Charger_Connection_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Fast_Charge_Terminate_Request
"" 159 ffcc99 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Fast_Charger_Alive
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1329
1
1
0
0
0
Fast_Charger_Keep_Alive_Message
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Fast_Charger_Alive
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1329
Fast_Charger_Keep_Alive_Message
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Fast_Charger_Alive
"" 159 669933 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Fast_Charger_Contactor_Status
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
20
1
1
0
0
0
Fast_Charger_Connection_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Fast_Charger_Contactor_Status
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
20
Fast_Charger_Connection_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Fast_Charger_Contactor_Status
"" 159 3399 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Fast_Charger_Plug_Status
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
20
1
1
0
0
0
Fast_Charger_Connection_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Fast_Charger_Plug_Status
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
20
Fast_Charger_Connection_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Fast_Charger_Plug_Status
"" 159 cccc33 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Max_Cell_Deviation_Observed_during_Charge
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1762
1
1
0
0
0
BMS00_Cloud_Data_6
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Max_Cell_Deviation_Observed_during_Charge
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1762
BMS00_Cloud_Data_6
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Max_Cell_Deviation_Observed_during_Charge
"V" 1 5a5a5a 0. 65. -100. 100. 10 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Max_Cell_Deviation_Observed_during_Discharge
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1762
1
1
0
0
0
BMS00_Cloud_Data_6
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Max_Cell_Deviation_Observed_during_Discharge
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1762
BMS00_Cloud_Data_6
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Max_Cell_Deviation_Observed_during_Discharge
"V" 1 ff 0. 65. -100. 100. 10 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Number_of_Charge_Stops_during_charge
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1756
1
1
0
0
0
BMS00_Cloud_Data_3
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Number_of_Charge_Stops_during_charge
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1756
BMS00_Cloud_Data_3
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Number_of_Charge_Stops_during_charge
"Count" 159 cc99 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Over_time_to_fast_charge_Error
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Over_time_to_fast_charge_Error
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Over_time_to_fast_charge_Error
"" 159 ff00ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Overall_Charge_current_limit
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
289
1
1
0
0
0
Overall_Battery_Current_Limit
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Overall_Charge_current_limit
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
289
Overall_Battery_Current_Limit
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Overall_Charge_current_limit
"Amp" 1 ff0000 0. 600. -100. 100. 100 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Overall_Charge_Voltage_limit
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
288
1
1
0
0
0
Overall_Battery_Status_Info
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Overall_Charge_Voltage_limit
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
288
Overall_Battery_Status_Info
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Overall_Charge_Voltage_limit
"V" 1 800080 3.60285 12.134711 -100. 100. 1 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Overall_Discharge_current_limit
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
289
1
1
0
0
0
Overall_Battery_Current_Limit
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Overall_Discharge_current_limit
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
289
Overall_Battery_Current_Limit
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Overall_Discharge_current_limit
"Amp" 1 8080 -400. 0. -100. 100. 50 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Overall_Discharge_Power_Limit
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
289
1
1
0
0
0
Overall_Battery_Current_Limit
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Overall_Discharge_Power_Limit
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
289
Overall_Battery_Current_Limit
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Overall_Discharge_Power_Limit
"Watts" 223 8000 -50000. 0. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Overcharge_Protection
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Overcharge_Protection
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Overcharge_Protection
"" 159 80 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Pre_Charge_Enable
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1754
1
1
0
0
0
BMS00_Cloud_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Pre_Charge_Enable
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1754
BMS00_Cloud_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Pre_Charge_Enable
"" 159 800000 0.24084845 0.82991415 -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Precharge_Switch_Demand_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1600
1
1
0
0
0
BMS00_Parameter_7
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Precharge_Switch_Demand_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1600
BMS00_Parameter_7
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Precharge_Switch_Demand_00
"" 159 808000 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Remaining_charging_time
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1289
1
1
0
0
0
Fast_Charger_Parameter_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Remaining_charging_time
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1289
Fast_Charger_Parameter_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Remaining_charging_time
"min" 159 969696 0. 65534. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Slow_Charger_Fault_Information
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
531
1
1
0
0
0
Charger_Status
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Slow_Charger_Fault_Information
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
531
Charger_Status
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Slow_Charger_Fault_Information
"" 159 0 0. 3. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
TempRiseChrgDtctn_60
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1041
1
1
0
0
0
BMS00_Fault_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
TempRiseChrgDtctn_60
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1041
BMS00_Fault_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
TempRiseChrgDtctn_60
"" 159 66ff 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Time_to_chargeFC_80
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
400
1
1
0
0
0
BMS00_Parameter_6
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Time_to_chargeFC_80
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
400
BMS00_Parameter_6
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Time_to_chargeFC_80
"Min" 159 ff99cc 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Time_to_chargeFC_100
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
400
1
1
0
0
0
BMS00_Parameter_6
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Time_to_chargeFC_100
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
400
BMS00_Parameter_6
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Time_to_chargeFC_100
"Min" 159 663399 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Time_to_chargeSC_80
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
400
1
1
0
0
0
BMS00_Parameter_6
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Time_to_chargeSC_80
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
400
BMS00_Parameter_6
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Time_to_chargeSC_80
"Min" 159 ffcc99 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Time_to_Full_chargeSC_00
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
400
1
1
0
0
0
BMS00_Parameter_6
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Time_to_Full_chargeSC_00
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
400
BMS00_Parameter_6
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Time_to_Full_chargeSC_00
"Min" 159 669933 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Total_Ah_Lost_in_charging_Aux_battery_in_Idle
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1760
1
1
0
0
0
BMS00_Cloud_Data_5
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Total_Ah_Lost_in_charging_Aux_battery_in_Idle
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1760
BMS00_Cloud_Data_5
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Total_Ah_Lost_in_charging_Aux_battery_in_Idle
"Amphr" 1 3399 -43. 0. -100. 100. 5 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Total_Amphr_Charged_in_the_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1758
1
1
0
0
0
BMS00_Cloud_Data_4
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Total_Amphr_Charged_in_the_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1758
BMS00_Cloud_Data_4
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Total_Amphr_Charged_in_the_Mode
"Amphr" 1 cccc33 0. 100. -100. 100. 20 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Total_Amphr_Discharged_in_the_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1756
1
1
0
0
0
BMS00_Cloud_Data_3
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Total_Amphr_Discharged_in_the_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1756
BMS00_Cloud_Data_3
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Total_Amphr_Discharged_in_the_Mode
"Amphr" 1 5a5a5a -100. 0. -100. 100. 20 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Total_Charge_Time
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1760
1
1
0
0
0
BMS00_Cloud_Data_5
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Total_Charge_Time
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1760
BMS00_Cloud_Data_5
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Total_Charge_Time
"Minutes" 159 ff 0. 65535. -100. 100. 10000 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Total_Kwhr_Charged_in_the_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1758
1
1
0
0
0
BMS00_Cloud_Data_4
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Total_Kwhr_Charged_in_the_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1758
BMS00_Cloud_Data_4
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Total_Kwhr_Charged_in_the_Mode
"Kwhr" 1 cc99 0. 65. -100. 100. 10 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Total_Kwhr_Dicharged_in_the_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1756
1
1
0
0
0
BMS00_Cloud_Data_3
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Total_Kwhr_Dicharged_in_the_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
BMS00
1756
BMS00_Cloud_Data_3
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Total_Kwhr_Dicharged_in_the_Mode
"Kwhr" 1 ff00ff -65. 0. -100. 100. 10 0 0 0 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Vehicle_Discharge_Mode
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
275
1
1
0
0
0
MCU_Data_2
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Vehicle_Discharge_Mode
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
MCU
275
MCU_Data_2
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Vehicle_Discharge_Mode
"" 159 ff0000 0. 15. -100. 100. 2 0 0 1 576000000 1 1 0 0 
VSignalObject 19 Begin_Of_Object
1
VSignalObjectBase 20 Begin_Of_Object
1
VHostSignal 21 Begin_Of_Object
2
0
Waiting_state_before_charging_start
0
End_Of_Object VHostSignal 21
2
VSignalState 21 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 21
End_Of_Object VSignalObjectBase 20
19
ValueObjectConfiguration::VConfiguredDBSignal 20 Begin_Of_Object
4
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22 Begin_Of_Object
1
VConfigCANFrame 23 Begin_Of_Object
5
VConfigMessage 24 Begin_Of_Object
5
VConfigBusObject 25 Begin_Of_Object
1
VConfigBusEvent 26 Begin_Of_Object
1
VConfigEvent 27 Begin_Of_Object
1
End_Of_Object VConfigEvent 27
1
1
End_Of_Object VConfigBusEvent 26
End_Of_Object VConfigBusObject 25
31
1
VDatabaseBusMessageId 25 Begin_Of_Object
3
1288
1
1
0
0
0
Fast_Charger_Parameter_1
0
CAN1_V01_Move4_0_Rev4_0_28
0
1
End_Of_Object VDatabaseBusMessageId 25
NULL
0
0
0
End_Of_Object VConfigMessage 24
End_Of_Object VConfigCANFrame 23
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 22
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 21
Waiting_state_before_charging_start
1
1
1
CAN1
CAN1_V01_Move4_0_Rev4_0_28
VCU
1288
Fast_Charger_Parameter_1
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 20

End_Of_Serialized_Data 19
End_Of_Object VSignalObject 19
[MeasurementObject]
Waiting_state_before_charging_start
"" 159 800080 0. 1. -100. 100. 1 0 0 1 576000000 1 1 0 0 
[GraphWindow:x_x_x_x_x_x_WindowBk_Grid_AxisBk_XAxisFr_YAxisFr_x_x_x_x_x_x]
2118486.9108699997887 4236973.8217399995774 2118486.9108699997887 200000 576000000 1 ffffff b2b2b2 ffffff 0 0 0 0 1 1 1 0
0 30 5000
0
0 100
0
16777215
0
2
0
1
41943040
22
VLogExportPersister 19 Begin_Of_Object
7
1416
25200253
Graphics Window
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19
12
20 37 320 20 18 194 55 55 55 55 60 13 
604
1
0
1
0
0
0
-11
0
0
0
0
0
0
0
400
0
Tahoma
0
1
0
0
0
-11
0
0
0
34
0
0
0
400
0
Tahoma
0
1
1
0
0
11711154
32768
0
0
0
0
1
0
0
0
0
228
0 10
1
121
1
0 0

AUX_BATTERY_CHARGING_STATUS
8388736 0
1
1 1

Battery_charge_inhibit_00
32896 0
1
2 2

Battery_charge_inhibit_01
32768 0
1
3 3

Battery_charge_inhibit_02
128 0
1
4 4

Battery_charge_inhibit_10ms
8388608 0
1
5 5

Battery_Derate_Charge_Current_Flag_00
8421376 0
1
6 6

Battery_Derate_Charge_Current_Flag_01
9868950 0
1
7 7

Battery_Derate_Charge_Current_Flag_02
0 0
1
8 8

Battery_Derate_Charge_Current_Flag_10ms
26367 0
1
9 9

Battery_discharge_inhibit_00
16751052 0
1
10 10

Battery_discharge_inhibit_01
6697881 0
1
11 11

Battery_discharge_inhibit_02
16764057 0
1
12 12

Battery_discharge_inhibit_10ms
6723891 0
1
13 13

Battery_Precharge_failure_status_00
13209 0
1
14 14

Battery_Precharge_failure_status_01
13421619 0
1
15 15

Battery_Precharge_failure_status_02
5921370 0
1
16 16

Battery_Precharge_failure_status_10ms
255 0
1
17 17

BMS00_Low_temperature_during_charging_Error
52377 0
1
18 18

BMS00_Low_temperature_during_charging_warning_info
16711935 0
1
19 19

BMS00_Over_current_charge_Error
16711680 0
1
20 20

BMS00_Over_current_charge_Warning
8388736 0
1
21 21

BMS00_Over_temperature_charge_Error
32896 0
1
22 22

BMS00_Over_temperature_charge_warning_info
32768 0
1
23 23

BMS00_Over_voltage_charge_Error
128 0
1
24 24

BMS00_Over_voltage_charge_Permanent_Fault
8388608 0
1
25 25

BMS00_Over_voltage_charge_warning_info
8421376 0
1
26 26

BMS00_Precharge_failure
9868950 0
1
27 27

Battery_Temperature_Blanket_Cut_off_Grade4
32896 0
1
28 28

CellDip_Error
32768 0
1
29 29

CellDip_Fault
128 0
1
30 30

DC_Bus_undervoltage_fault_Grade_3
8388608 0
1
31 31

Drive_Overload_Grade_3
8421376 0
1
32 32

Drive_overtemperature_Grade_3
9868950 0
1
33 33

Encoder_disconnection_Grade_3
0 0
1
34 34

Motor_over_speed_fault_Grade_3
26367 0
1
35 35

Motor_overtemperature_Grade_3
16751052 0
1
36 36

Motor_temperature_sensor_disconnection_fault_Grade_2
6697881 0
1
37 37

Overvoltagebaseline_of_hardware_is_wrong_Grade_3
16764057 0
1
38 38

SOCRegenWarn
6723891 0
1
39 39

Software_overcurrent_protection_Grade_3
13209 0
1
40 40

Software_overvoltage_fault_Grade_3
13421619 0
1
41 41

Stalling_Fault_Grade_3
5921370 0
1
42 42

TempRegen_Warning
255 0
1
43 43

Vehicle_Mode_Level_1
52377 0
1
44 44

Vehicle_Mode_Level_2
16711935 0
1
45 45

Vehicle_Mode_Level_3
16711680 0
1
46 46

Vehicle_Mode_Level_4
8388736 0
1
47 47

BMS00_Precharge_failure_10ms
0 0
1
48 48

BMS00_Precharge_too_fast_Info
26367 0
1
49 49

BMS00_Precharge_too_fast_Info_10ms
16751052 0
1
50 50

BMS00_Precharge_too_slow_Info
6697881 0
1
51 51

BMS00_Precharge_too_slow_Info_10ms
16764057 0
1
52 52

Charge_AmpHr_00
6723891 0
1
53 53

Charge_control_message_timeout_error
13209 0
1
54 54

Charge_current_limit_00
13421619 0
1
55 55

Charge_MOSFET_Enable
5921370 0
1
56 56

Charge_Over_Current
255 0
1
57 57

Charge_Switch_Demand_00
52377 0
1
58 58

Charge_TTL
16711935 0
1
59 59

Charge_Voltage_limit_00
16711680 0
1
60 60

Charger_Connected
8388736 0
1
61 61

Charger_Mode
32896 0
1
62 62

Charger_Mode_Request
32768 0
1
63 63

Charger_output_Short_circuit_error
128 0
1
64 64

Charger_over_temperature_error
8388608 0
1
65 65

Charger_Plug_Sense
8421376 0
1
66 66

CHARGER_PLUG_SENSE_STATUS
9868950 0
1
67 67

Charger_Plugged_In
0 0
1
68 68

Charger_Retrigger
26367 0
1
69 69

Charger_Sleep_Request
16751052 0
1
70 70

Charger_Status_Signal
6697881 0
1
71 71

Charger_under_temperature_error
16764057 0
1
72 72

Charger_USB_12V_Supply_Control_Command
6723891 0
1
73 73

ChargerReAuthError
13209 0
1
74 74

ChargerReAuthWarning
13421619 0
1
75 75

Charging_Current_Available_range
5921370 0
1
76 76

Charging_Mode_00
255 0
1
77 77

Charging_Rate
52377 0
1
78 78

Charging_status_of_AUX_battery
16711935 0
1
79 79

Charging_system_error
16711680 0
1
80 80

Charging_time_Max
8388736 0
1
81 81

Charging_Voltage_Available_range
32896 0
1
82 82

Discharge_AmpHr_00
32768 0
1
83 83

Discharge_current_limit_00
128 0
1
84 84

Discharge_Mode
8388608 0
1
85 85

Discharge_MOSFET_Enable
8421376 0
1
86 86

Discharge_Over_current
9868950 0
1
87 87

Discharge_Power_Limit_00
0 0
1
88 88

Discharge_Switch_Demand_00
26367 0
1
89 89

Factored_Charge_Amphr
16751052 0
1
90 90

Factored_Discharge_Amphr
6697881 0
1
91 91

Fast_Charge_Terminate_Request
16764057 0
1
92 92

Fast_Charger_Alive
6723891 0
1
93 93

Fast_Charger_Contactor_Status
13209 0
1
94 94

Fast_Charger_Plug_Status
13421619 0
1
95 95

Max_Cell_Deviation_Observed_during_Charge
5921370 0
1
96 96

Max_Cell_Deviation_Observed_during_Discharge
255 0
1
97 97

Number_of_Charge_Stops_during_charge
52377 0
1
98 98

Over_time_to_fast_charge_Error
16711935 0
1
99 99

Overall_Charge_current_limit
16711680 0
1
100 100

Overall_Charge_Voltage_limit
8388736 0
1
101 101

Overall_Discharge_current_limit
32896 0
1
102 102

Overall_Discharge_Power_Limit
32768 0
1
103 103

Overcharge_Protection
128 0
1
104 104

Pre_Charge_Enable
8388608 0
1
105 105

Precharge_Switch_Demand_00
8421376 0
1
106 106

Remaining_charging_time
9868950 0
1
107 107

Slow_Charger_Fault_Information
0 0
1
108 108

TempRiseChrgDtctn_60
26367 0
1
109 109

Time_to_chargeFC_80
16751052 0
1
110 110

Time_to_chargeFC_100
6697881 0
1
111 111

Time_to_chargeSC_80
16764057 0
1
112 112

Time_to_Full_chargeSC_00
6723891 0
1
113 113

Total_Ah_Lost_in_charging_Aux_battery_in_Idle
13209 0
1
114 114

Total_Amphr_Charged_in_the_Mode
13421619 0
1
115 115

Total_Amphr_Discharged_in_the_Mode
5921370 0
1
116 116

Total_Charge_Time
255 0
1
117 117

Total_Kwhr_Charged_in_the_Mode
52377 0
1
118 118

Total_Kwhr_Dicharged_in_the_Mode
16711935 0
1
119 119

Vehicle_Discharge_Mode
16711680 0
1
120 120

Waiting_state_before_charging_start
8388736 0
0
VLogCfgData 19 Begin_Of_Object
12
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
0
29
1
1
0
2
0
0
19
VLogExportPersister 20 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 20

End_Of_Serialized_Data 19
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Graphics.mdf" 
0
1
0
10
80
410
1
1
0


<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Graphics.mdf" 
1
0
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
0
a9af47f1-0162-488b-b58f-c0c7093765a8
1
VLoggingComment 20 Begin_Of_Object
1
1
VLoggingCommentAttribute 21 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 21
End_Of_Object VLoggingComment 20
End_Of_Object VLogCfgData 19
0 128
0 0 0 0
1 40 20 15
0 -1
1
1
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
1 12
1 1 1 1 0 0 0 0 1 1 0 0 
0 1 2 5 -1 -1 -1 -1 3 4 -1 -1 
20 37 340 194 55 55 55 55 60 60 82 45 
604 80
0
0
30000000000
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
0
1
0
0
0
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
1
1
0
0
End_Of_Object VSignalObjectStreamer 18
End_Of_Object VGraphBoxConf 17
VDOLine 17 Begin_Of_Object
1
76
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
77
0
5
VDAOGBHSStd 14 Begin_Of_Object
1
78
0
0 0
TABPredecessor:
75
TABSuccessor:
80
VDODynamicLine 15 Begin_Of_Object
1
79
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
80
0
TABPredecessor:
78
TABSuccessor:
83
VGraphBoxConf 17 Begin_Of_Object
1
VNETGraphBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 0 0 1 -1 -1 -1 -1 273 105 1003 550
Graphics 2
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 273 105 1003 550
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{8A56773D-28FC-4156-B0B4-2A9BEEFE2EB7}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETGraphBox 18
81
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
Expanded
True
Int32
SplitterWidth
184
Int32
SplitterHeight
80
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.Position
3
LegendPosition
3
Int32
value__
0
--TextFormatter: End of Object--
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.GraphicCommandID
4
ScrollSignalsButton1Command
4
Int32
value__
29
--TextFormatter: End of Object--
TypeRef:4
ScrollSignalsButton2Command
5
Int32
value__
30
--TextFormatter: End of Object--
TypeRef:4
ScrollButton1Command
6
Int32
value__
35
--TextFormatter: End of Object--
TypeRef:4
ScrollButton2Command
7
Int32
value__
36
--TextFormatter: End of Object--
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
VSignalObjectStreamer 18 Begin_Of_Object
1
0
[GraphWindow:x_x_x_x_x_x_WindowBk_Grid_AxisBk_XAxisFr_YAxisFr_x_x_x_x_x_x]
0 100000 100000 200000 576000000 1 ffffff b2b2b2 ffffff 0 0 0 0 1 1 1 0
0 30 5000
0
0 100
0
16777215
0
2
0
1
41943040
-1
VLogExportPersister 19 Begin_Of_Object
7
1416
25200253
Graphics Window
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19
12
20 38 62 20 18 22 22 24 29 33 44 13 
184
0
0
1
0
0
0
-11
0
0
0
0
0
0
0
400
0
Tahoma
0
1
0
0
0
-11
0
0
0
34
0
0
0
400
0
Tahoma
0
1
1
0
0
11711154
32768
0
0
0
0
0
0
0
0
0
317
0 10
1
0
0
VLogCfgData 19 Begin_Of_Object
12
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
1
29
1
1
0
2
0
0
19
VLogExportPersister 20 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 20

End_Of_Serialized_Data 19
<VFileName V9 QL> 1 "Graphics_2.mdf" 
0
1
0
30
80
410
1
1
0


<VFileName V9 QL> 1 "{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "Graphics_2.mdf" 
1
1
<VFileName V9 QL> 1 "{LoggingBlock}.mdf" 
0
14388b96-ac0b-402d-83d2-d7d73f87b111
1
VLoggingComment 20 Begin_Of_Object
1
1
VLoggingCommentAttribute 21 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 21
End_Of_Object VLoggingComment 20
End_Of_Object VLogCfgData 19
0 128
0 0 0 0
1 40 20 15
0 -1
1
1
0
0
0
1 12
1 1 1 0 0 0 0 0 0 0 0 0 
0 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 
20 38 82 22 22 24 29 33 44 44 82 30 
184 80
0
0
30000000000
1
0
1
End_Of_Object VSignalObjectStreamer 18
End_Of_Object VGraphBoxConf 17
VDOLine 17 Begin_Of_Object
1
81
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
82
0
6
VDAOGBHSStd 14 Begin_Of_Object
1
83
0
0 0
TABPredecessor:
80
TABSuccessor:
85
VDODynamicLine 15 Begin_Of_Object
1
84
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
85
0
TABPredecessor:
83
TABSuccessor:
87
VTriggerConfiguration 17 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 18 Begin_Of_Object
1
VTriggerCfgData 19 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
200000
1
1
1
1
50000
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 19
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 18
VTriggerBox 18 Begin_Of_Object
1
VBoxRoot 19 Begin_Of_Object
1
1
1 -1 0 1 0 0 0 0 208 222 817 579
Logging
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{B4420F90-8E30-47C5-ADE2-9C9639240574}
0
End_Of_Object VBoxRoot 19
End_Of_Object VTriggerBox 18
0
End_Of_Object VTriggerConfiguration 17
VDOLine 17 Begin_Of_Object
1
86
0
10 0
VDAOGBFunctionBlock 18 Begin_Of_Object
1
87
0
TABPredecessor:
85
TABSuccessor:
0
VLoggingConfiguration 19 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 20 Begin_Of_Object
1
VLogCfgData 21 Begin_Of_Object
12
0
1
1
1
0
0
0
0
1024
60
1
0
0
3
0
29
1
1
0
2
0
0
21
VLogExportPersister 22 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "..\BLF\instance-2\S1 AIR_OOTY_P53AUDCAXCEA00027_BAK_2023-07-13_9_10_22_3.0.3.gen2.0627_1.blf" 
<VFileName V9 QL> 1 "..\CSV\S1 AIR_OOTY_P53AUDCAXCEA00027_BAK_2023-07-13_9_10_22_3.0.3.gen2.0627_1.csv" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 22

End_Of_Serialized_Data 21
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging.blf" 
0
1
0
10
80
410
1
1
1


<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\{LoggingBlock}.blf" 
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging.blf" 
1
1
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\{LoggingBlock}.blf" 
0
85901f01-a10c-41e5-9bc1-c07accc3cad0
1
VLoggingComment 22 Begin_Of_Object
1
1
VLoggingCommentAttribute 23 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 23
End_Of_Object VLoggingComment 22
End_Of_Object VLogCfgData 21
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 20
1
End_Of_Object VLoggingConfiguration 19
VDOLine 19 Begin_Of_Object
1
88
0
60 0
NULL
End_Of_Object VDOLine 19

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 18
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
End_Of_Object VDOFRamification 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
End_Of_Object VDAOSwitch 9
End_Of_Object VDODynamicLine 8
End_Of_Object VDAOGBHSStd 7
NULL
VDODynamicLine 7 Begin_Of_Object
1
89
1
20
VDAOGBHSStd 8 Begin_Of_Object
1
90
1
1 0
TABPredecessor:
44
TABSuccessor:
95
VDODynamicLine 9 Begin_Of_Object
1
91
1
0
VDODynamicLine 10 Begin_Of_Object
1
92
1
10
VDODynamicLine 11 Begin_Of_Object
1
93
0
20
VDOLine 12 Begin_Of_Object
1
94
0
10 0
VDAOGBFunctionBlock 13 Begin_Of_Object
1
95
0
TABPredecessor:
90
TABSuccessor:
46
VLoggingConfiguration 14 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 15 Begin_Of_Object
1
VLogCfgData 16 Begin_Of_Object
12
0
1
1
1
0
0
0
0
1024
60
0
0
0
3
1
29
1
1
0
2
1
0
16
VLogExportPersister 17 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 17

End_Of_Serialized_Data 16
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging_Prefilter.blf" 
0
1
0
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging_Prefilter.blf" 
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe" 
1
1
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging_Prefilter.blf" 
0
ed3b1956-122c-4e07-8c5c-9e3a5cf274d0
1
VLoggingComment 17 Begin_Of_Object
1
1
VLoggingCommentAttribute 18 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 18
End_Of_Object VLoggingComment 17
End_Of_Object VLogCfgData 16
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 15
5
End_Of_Object VLoggingConfiguration 14
NULL

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 13
End_Of_Object VDOLine 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDODynamicLine 9
End_Of_Object VDAOGBHSStd 8
End_Of_Object VDODynamicLine 7
4
End_Of_Object VDOCrossing 6

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
VDAOGBFunctionBlock 4 Begin_Of_Object
1
1
0
TABPredecessor:
0
TABSuccessor:
48
VOfflineSrcConfiguration 5 Begin_Of_Object
3
VMigratedGenericConfiguration<struct_VOfflineCfgData> 6 Begin_Of_Object
1
VOfflineCfgData 7 Begin_Of_Object
2
VReplayCfgBase 8 Begin_Of_Object
1
1
<VFileName V9 QL> 1 "..\..\..\030723_8P_P_VIN 0095_S1 AIR_R_ANBU_65.blf" 
1
End_Of_Object VReplayCfgBase 8
VCfgBreakCondition 8 Begin_Of_Object
1
VDataBreakCondition 9 Begin_Of_Object
1
0
VEvCondBlock 10 Begin_Of_Object
1
VEvCondGroup 11 Begin_Of_Object
2
VEvCondPrimitive 12 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 12
1
0
0
End_Of_Object VEvCondGroup 11
End_Of_Object VEvCondBlock 10
End_Of_Object VDataBreakCondition 9
End_Of_Object VCfgBreakCondition 8
0
0
0
0
0
0
End_Of_Object VOfflineCfgData 7
End_Of_Object VMigratedGenericConfiguration<struct_VOfflineCfgData> 6
VChannelMapping 6 Begin_Of_Object
1
0
End_Of_Object VChannelMapping 6
End_Of_Object VOfflineSrcConfiguration 5
NULL

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
0
End_Of_Object VDOLocalInfoStruct 3
67305472
0
0
2
VDOLocalInfoStruct 3 Begin_Of_Object
End_Of_Object VDOLocalInfoStruct 3
End_Of_Serialized_Data 2
0.000000
0 0
End_Of_Object VGBAnlyzBox 2
VGBRealTimeBox 2 Begin_Of_Object
1
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
0 1 0 1 -1 -1 -8 -31 0 0 774 588

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 43 43 963 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
6
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 0 0 774 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 44 44 975 557
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 774 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 0 0 0 0 0 0 0 0 0
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 512 0 1024 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 512 0 1024 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 774 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{859D5F30-8AA0-44F5-A116-746AD888DE4A}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
4
1
89
VDAOBus 4 Begin_Of_Object
1
1
1
VDAOGBFunctionBlock 5 Begin_Of_Object
1
89
3
TABPredecessor:
1
TABSuccessor:
2
VCANInteractiveGeneratorConfiguration 6 Begin_Of_Object
1
VMigratedGenericConfiguration<class_VCANInteractiveGeneratorData> 7 Begin_Of_Object
1
VCANInteractiveGeneratorData 8 Begin_Of_Object
1
1
End_Of_Object VCANInteractiveGeneratorData 8
End_Of_Object VMigratedGenericConfiguration<class_VCANInteractiveGeneratorData> 7
3
CAN IG
BOF_MBSSDATA
1 0 0 0 
SS_BEGIN_COMMON_INFO
1
3
Behavior
1
Buses
1
Misc
1
SS_END_COMMON_INFO

EOF_MBSSDATA
1
0 1
VNETStandaloneComponent 7 Begin_Of_Object
1
VNETControlBox 8 Begin_Of_Object
2
VUniqueBox 9 Begin_Of_Object
1
VBoxRoot 10 Begin_Of_Object
1
3
1 4 0 1 -1 -1 -8 -31 273 105 1065 698
CAN IG
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 273 105 1065 698
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
4
0
0
0
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{42093941-1010-4A38-B72F-9D287F3905C9}
0
End_Of_Object VBoxRoot 10
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 9
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 8
1711
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Controller.CANwinCommunicator
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Controller.Controller
3
Controller
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.SendItemList.ConfigurationModel
5
Model
5
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.SendItemList.SendListItem
6
FocusedSendListItem
6
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.GUI.GridViewSettings
7
SendItemGridViewSettings
7
TypeRef:7
FrameSignalGridViewSettings
8
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.GUI.MainControlSettings
8
MainControlSettingsSettings
9
Boolean
AutomaticGMLANColumnActivation
True
Boolean
AutomaticCANFDColumnActivation
True
Boolean
AutomaticJ1939ColumnActivation
True
TypeRef:4
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
4
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:5
5
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Util.DataCollection`1[[Vector.CANalyzer.CAN.InteractiveGenerator.Model.SendItemList.SendListItem, Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
9
Items
11
TypeRef:4
SerializationVersion
12
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.FramePersistor
10
Frame
13
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.TriggerConditionPersistor
11
TriggerCondition
14
TypeRef:4
SerializationVersion
15
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:7
7

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.CAN.InteractiveGenerator.Util.SerializeableKeyValuePair`1[[Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.GUI.GridColumnSettings, Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]], Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
12
ColumnSettings
16
TypeRef:4
SerializationVersion
17
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:7
8
TypeRef:12
ColumnSettings
18
--TextFormatter: End of Object--
TypeRef:8
9
Boolean
IsLeftRightSplitterExpanded
False
Boolean
IsUpperLowerSplitterExpanded
True
Int32
LeftRightSplitterDistance
300
Int32
UpperLowerSplitterDistance
250
Boolean
IsAnyPropertyChanged
True
TypeRef:4
SerializationVersion
19
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:9
11
Int32
Count
1
TypeRef:6
Item_0
6
--TextFormatter: End of Object--
TypeRef:10
13
String
FrameName
1
7C1
String
UserComment
1

UInt32
CANID
1
Boolean
IdentifierExtension
False
UInt32
ChannelNumber
1
UInt32
DataLengthCode
8
Int32
PayloadLength
8
Byte
PayloadByte0
0
Byte
PayloadByte1
0
Byte
PayloadByte2
0
Byte
PayloadByte3
0
Byte
PayloadByte4
0
Byte
PayloadByte5
0
Byte
PayloadByte6
0
Byte
PayloadByte7
0
UInt32
GMLANParameterID
0
UInt32
GMLANPriority
0
UInt32
GMLANSourceID
1
String
GMLANSourceNode
1

UInt32
J1939DataPage
0
UInt32
J1939ExtendedDataPage
0
UInt32
J1939Priority
0
UInt32
J1939Source
1
UInt32
J1939Destination
0
UInt32
J1939ParameterGroupNumber
0
String
J1939SourceNode
1

String
J1939DestinationNode
1

Boolean
IsJ1939TransportProtocolMessage
False
UInt32
J1939TransportProtocolLength
8
UInt32
ProtocolType
1
UInt32
ProtocolType_New
1
Boolean
RemoteTransmissionRequest
False
UInt32
FrameType
1
UInt32
FrameType_New
1
TypeRef:4
SerializationVersion
20
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
4
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:11
14
Boolean
IsActive
False
Int32
TriggerType
1
UInt32
TriggerCANId
0
String
InvalidCANFrameName
1

UInt32
TriggerEventChannelNumber
1
String
TriggerSystemVariablePath
1

Int32
TriggerSystemVariableChangeType
1

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Object
13
TriggerDatabaseFrameEntry
0
Int32
CompatibleTriggerType
1
Boolean
HasNativeTriggerKey
False
Boolean
HasTriggerKey
False
UInt32
TriggerPeriod
10
TypeRef:4
SerializationVersion
21
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
4
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
16
Array
_items
22
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Util.SerializeableKeyValuePair`1[[Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.GUI.GridColumnSettings, Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
14
1
0
31
TypeRef:14
ArrayElement
23
TypeRef:14
ArrayElement
24
TypeRef:14
ArrayElement
25
TypeRef:14
ArrayElement
26
TypeRef:14
ArrayElement
27
TypeRef:14
ArrayElement
28
TypeRef:14
ArrayElement
29
TypeRef:14
ArrayElement
30
TypeRef:14
ArrayElement
31
TypeRef:14
ArrayElement
32
TypeRef:14
ArrayElement
33
TypeRef:14
ArrayElement
34
TypeRef:14
ArrayElement
35
TypeRef:14
ArrayElement
36
TypeRef:14
ArrayElement
37
TypeRef:14
ArrayElement
38
TypeRef:14
ArrayElement
39
TypeRef:14
ArrayElement
40
TypeRef:14
ArrayElement
41
TypeRef:14
ArrayElement
42
TypeRef:14
ArrayElement
43
TypeRef:14
ArrayElement
44
TypeRef:14
ArrayElement
45
TypeRef:14
ArrayElement
46
TypeRef:14
ArrayElement
47
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
Int32
_size
25
Int32
_version
54
--TextFormatter: End of Object--
TypeRef:12
18
Array
_items
48
TypeRef:14
1
0
15
TypeRef:14
ArrayElement
49
TypeRef:14
ArrayElement
50
TypeRef:14
ArrayElement
51
TypeRef:14
ArrayElement
52
TypeRef:14
ArrayElement
53
TypeRef:14
ArrayElement
54
TypeRef:14
ArrayElement
55
TypeRef:14
ArrayElement
56
TypeRef:14
ArrayElement
57
TypeRef:14
ArrayElement
58
TypeRef:14
ArrayElement
59
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
TypeRef:13
ArrayElement
0
Int32
_size
11
Int32
_version
26
--TextFormatter: End of Object--
TypeRef:14
23
String
Key
1
colItemType
APPDIR Vector.CANalyzer.CAN.InteractiveGenerator.DLL
Vector.CANalyzer.CAN.InteractiveGenerator, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.InteractiveGenerator.Model.Persistence.GUI.GridColumnSettings
15
Value
60
TypeRef:4
SerializationVersion
61
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:14
24
String
Key
1
colName
TypeRef:15
Value
62
--TextFormatter: End of Object--
TypeRef:14
25
String
Key
1
colDatabaseComment
TypeRef:15
Value
63
--TextFormatter: End of Object--
TypeRef:14
26
String
Key
1
colUserComment
TypeRef:15
Value
64
--TextFormatter: End of Object--
TypeRef:14
27
String
Key
1
colID
TypeRef:15
Value
65
--TextFormatter: End of Object--
TypeRef:14
28
String
Key
1
colChannel
TypeRef:15
Value
66
--TextFormatter: End of Object--
TypeRef:14
29
String
Key
1
colIDE
TypeRef:15
Value
67
--TextFormatter: End of Object--
TypeRef:14
30
String
Key
1
colDataLengthCode
TypeRef:15
Value
68
--TextFormatter: End of Object--
TypeRef:14
31
String
Key
1
colBRS
TypeRef:15
Value
69
--TextFormatter: End of Object--
TypeRef:14
32
String
Key
1
colRTR
TypeRef:15
Value
70
--TextFormatter: End of Object--
TypeRef:14
33
String
Key
1
colTriggerType
TypeRef:15
Value
71
--TextFormatter: End of Object--
TypeRef:14
34
String
Key
1
colSend
TypeRef:15
Value
72
--TextFormatter: End of Object--
TypeRef:14
35
String
Key
1
colPositionInList
TypeRef:15
Value
73
--TextFormatter: End of Object--
TypeRef:14
36
String
Key
1
colRowNumber
TypeRef:15
Value
74
--TextFormatter: End of Object--
TypeRef:14
37
String
Key
1
colParameterID
TypeRef:15
Value
75
--TextFormatter: End of Object--
TypeRef:14
38
String
Key
1
colPriority
TypeRef:15
Value
76
--TextFormatter: End of Object--
TypeRef:14
39
String
Key
1
colSourceID
TypeRef:15
Value
77
--TextFormatter: End of Object--
TypeRef:14
40
String
Key
1
colProtocol
TypeRef:15
Value
78
--TextFormatter: End of Object--
TypeRef:14
41
String
Key
1
colJ1939ParameterGroupNumber
TypeRef:15
Value
79
--TextFormatter: End of Object--
TypeRef:14
42
String
Key
1
colJ1939DataPage
TypeRef:15
Value
80
--TextFormatter: End of Object--
TypeRef:14
43
String
Key
1
colJ1939ExtendedDataPage
TypeRef:15
Value
81
--TextFormatter: End of Object--
TypeRef:14
44
String
Key
1
colJ1939Priority
TypeRef:15
Value
82
--TextFormatter: End of Object--
TypeRef:14
45
String
Key
1
colJ1939Source
TypeRef:15
Value
83
--TextFormatter: End of Object--
TypeRef:14
46
String
Key
1
colJ1939Destination
TypeRef:15
Value
84
--TextFormatter: End of Object--
TypeRef:14
47
String
Key
1
colSendNow
TypeRef:15
Value
85
--TextFormatter: End of Object--
TypeRef:14
49
String
Key
1
colStartBit
TypeRef:15
Value
86
--TextFormatter: End of Object--
TypeRef:14
50
String
Key
1
colSignalLength
TypeRef:15
Value
87
--TextFormatter: End of Object--
TypeRef:14
51
String
Key
1
colSignalName
TypeRef:15
Value
88
--TextFormatter: End of Object--
TypeRef:14
52
String
Key
1
colDatabaseComment
TypeRef:15
Value
89
--TextFormatter: End of Object--
TypeRef:14
53
String
Key
1
colRawValue
TypeRef:15
Value
90
--TextFormatter: End of Object--
TypeRef:14
54
String
Key
1
colRawValueStepSize
TypeRef:15
Value
91
--TextFormatter: End of Object--
TypeRef:14
55
String
Key
1
colPhysicalValue
TypeRef:15
Value
92
--TextFormatter: End of Object--
TypeRef:14
56
String
Key
1
colPhysicalStepSize
TypeRef:15
Value
93
--TextFormatter: End of Object--
TypeRef:14
57
String
Key
1
colUnit
TypeRef:15
Value
94
--TextFormatter: End of Object--
TypeRef:14
58
String
Key
1
colGeneratorSettings
TypeRef:15
Value
95
--TextFormatter: End of Object--
TypeRef:14
59
String
Key
1
colGeneratorType
TypeRef:15
Value
96
--TextFormatter: End of Object--
TypeRef:15
60
Int32
ColumnHandle
4
Int32
VisibleIndex
6
Boolean
Visible
True
Int32
Width
140
Int32
SortOrder
0
Int32
SortIndex
-1
TypeRef:4
SerializationVersion
97
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:15
62
Int32
ColumnHandle
1
Int32
VisibleIndex
3
Boolean
Visible
True
Int32
Width
72
Int32
SortOrder
1
Int32
SortIndex
0
--TextFormatter: End of Object--
TypeRef:15
63
Int32
ColumnHandle
3
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
64
Int32
ColumnHandle
2
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
65
Int32
ColumnHandle
5
Int32
VisibleIndex
4
Boolean
Visible
True
Int32
Width
70
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
66
Int32
ColumnHandle
0
Int32
VisibleIndex
5
Boolean
Visible
True
Int32
Width
80
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
67
Int32
ColumnHandle
6
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
30
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
68
Int32
ColumnHandle
25
Int32
VisibleIndex
7
Boolean
Visible
True
Int32
Width
50
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
69
Int32
ColumnHandle
11
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
30
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
70
Int32
ColumnHandle
12
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
40
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
71
Int32
ColumnHandle
8
Int32
VisibleIndex
2
Boolean
Visible
True
Int32
Width
96
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
72
Int32
ColumnHandle
9
Int32
VisibleIndex
1
Boolean
Visible
True
Int32
Width
60
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
73
Int32
ColumnHandle
24
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
74
Int32
ColumnHandle
23
Int32
VisibleIndex
0
Boolean
Visible
True
Int32
Width
35
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
75
Int32
ColumnHandle
15
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
76
Int32
ColumnHandle
13
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
77
Int32
ColumnHandle
14
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
78
Int32
ColumnHandle
10
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
79
Int32
ColumnHandle
16
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
80
Int32
ColumnHandle
17
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
30
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
81
Int32
ColumnHandle
18
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
30
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
82
Int32
ColumnHandle
19
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
40
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
83
Int32
ColumnHandle
20
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
84
Int32
ColumnHandle
21
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
85
Int32
ColumnHandle
-1
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
75
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
86
Int32
ColumnHandle
12
Int32
VisibleIndex
8
Boolean
Visible
True
Int32
Width
62
Int32
SortOrder
1
Int32
SortIndex
0
--TextFormatter: End of Object--
TypeRef:15
87
Int32
ColumnHandle
13
Int32
VisibleIndex
9
Boolean
Visible
True
Int32
Width
61
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
88
Int32
ColumnHandle
14
Int32
VisibleIndex
0
Boolean
Visible
True
Int32
Width
84
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
89
Int32
ColumnHandle
15
Int32
VisibleIndex
-1
Boolean
Visible
False
Int32
Width
80
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
90
Int32
ColumnHandle
16
Int32
VisibleIndex
3
Boolean
Visible
True
Int32
Width
73
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
91
Int32
ColumnHandle
17
Int32
VisibleIndex
4
Boolean
Visible
True
Int32
Width
68
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
92
Int32
ColumnHandle
18
Int32
VisibleIndex
5
Boolean
Visible
True
Int32
Width
73
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
93
Int32
ColumnHandle
19
Int32
VisibleIndex
6
Boolean
Visible
True
Int32
Width
68
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
94
Int32
ColumnHandle
20
Int32
VisibleIndex
7
Boolean
Visible
True
Int32
Width
50
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
95
Int32
ColumnHandle
-1
Int32
VisibleIndex
1
Boolean
Visible
True
Int32
Width
95
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
TypeRef:15
96
Int32
ColumnHandle
1
Int32
VisibleIndex
2
Boolean
Visible
True
Int32
Width
90
Int32
SortOrder
0
Int32
SortIndex
-1
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 7
End_Of_Object VCANInteractiveGeneratorConfiguration 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
0
TABPredecessor:
0
TABSuccessor:
89
VDAOGBFunctionBlock 5 Begin_Of_Object
1
2
0
TABPredecessor:
89
TABSuccessor:
0
VCardConf 6 Begin_Of_Object
1
End_Of_Object VCardConf 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
End_Of_Object VDAOBus 4
NULL
0
End_Of_Object VDOLocalInfoStruct 3
0.000000
0 0
1 0 2 59421 1 435 1 2882400001 328 763 176 727 2882400002  0 0 0 59421 1 435 1 2882400001 323 323 178 178 2882400002  0 0 0 0 0 0   3 
SS_BEGIN_COMMON_INFO
1
0
SS_END_COMMON_INFO

EOF_MBSSDATA
2
CAN1
1
1
1
1707345880304 1 0 1 0 1 0 0 0 2000 0 1 0 
SS_BEGIN_COMMON_INFO
1
3
Channels
1
Databases
1
Misc
1
SS_END_COMMON_INFO

EOF_BUSDATA
1
_Start_VPRBSManager 1 
0 0x32 0x1 
_End_VPRBSManager
NodeSignalPanelBustypeCount 1
CAN
NodeSignalPanelCount 1
VNETStandaloneComponent 1 Begin_Of_Object
1
VNETControlBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 -1 2 3 -1 -1 -8 -31 273 105 1093 423
Network Panel - CAN1
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -8 -31 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{57ACD7E3-AA71-490B-BCD4-7D63FC3D44D0}
0
End_Of_Object VBoxRoot 4
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 3
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 2
526
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VMainApplication
1
1
Int32
AppChannel
1
String
DatabaseName
1

String
TxECUName
1

Boolean
BusContext
True
Int32
BusType
1
Int32
NodeSelectionCount
6
String
NodeSelectionName0
1
Tester_Tool
Boolean
NodeSelectionActive0
False
String
NodeSelectionName1
1
ESCL
Boolean
NodeSelectionActive1
False
String
NodeSelectionName2
1
HU
Boolean
NodeSelectionActive2
False
String
NodeSelectionName3
1
MCU
Boolean
NodeSelectionActive3
False
String
NodeSelectionName4
1
VCU
Boolean
NodeSelectionActive4
False
String
NodeSelectionName5
1
BMS00
Boolean
NodeSelectionActive5
False
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VMainView
3
MainView
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VSignalView
5
SignalView
5
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VMessageView
6
MessageView
6
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VRxNodeView
7
RxNodeView
7
Int32
SelectedView
0
TypeRef:4
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:5
5
Int32
Grouping
0
Boolean
SignalColumn_Visible
True
Boolean
MuxColumn_Visible
False
Boolean
SubframeColumn_Visible
False
Boolean
PhysColumn_Visible
True
Boolean
RawColumn_Visible
True
Boolean
MessageColumn_Visible
False
Boolean
UnitColumn_Visible
False
Boolean
RxNodeColumn_Visible
False
Boolean
TxNodeColumn_Visible
True
Boolean
SymbolicValueColumn_Visible
True
Boolean
TxAckColumn_Visible
False
Boolean
SigLenColumn_Visible
False
Int32
SignalColumn_VisibleIndex
0
Int32
MuxColumn_VisibleIndex
-1
Int32
SubframeColumn_VisibleIndex
-1
Int32
PhysColumn_VisibleIndex
1
Int32
RawColumn_VisibleIndex
2
Int32
MessageColumn_VisibleIndex
-1
Int32
UnitColumn_VisibleIndex
-1
Int32
RxNodeColumn_VisibleIndex
-1
Int32
TxNodeColumn_VisibleIndex
4
Int32
SymbolicValueColumn_VisibleIndex
3
Int32
TxAckColumn_VisibleIndex
-1
Int32
SigLenColumn_VisibleIndex
-1
Int32
SignalColumn_Width
115
Int32
MuxColumn_Width
75
Int32
SubframeColumn_Width
75
Int32
PhysColumn_Width
100
Int32
RawColumn_Width
100
Int32
MessageColumn_Width
163
Int32
UnitColumn_Width
75
Int32
RxNodeColumn_Width
168
Int32
TxNodeColumn_Width
543
Int32
SymbolicValueColumn_Width
100
Int32
TxAckColumn_Width
25
Int32
SigLenColumn_Width
75
TypeRef:4
SerializationVersion
9
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Int32
Grouping
0
Boolean
MessageColumn_Visible
True
Boolean
RxNodeColumn_Visible
False
Boolean
TxNodeColumn_Visible
True
Boolean
ButtonColumn_Visible
True
Boolean
SignalColumn_Visible
True
Boolean
MuxColumn_Visible
False
Boolean
SubframeColumn_Visible
False
Boolean
PhysColumn_Visible
True
Boolean
RawColumn_Visible
True
Boolean
UnitColumn_Visible
False
Boolean
SymbolicValueColumn_Visible
True
Boolean
TxAckColumn_Visible
False
Boolean
SigLenColumn_Visible
False
Int32
MessageColumn_VisibleIndex
0
Int32
RxNodeColumn_VisibleIndex
-1
Int32
TxNodeColumn_VisibleIndex
1
Int32
ButtonColumn_VisibleIndex
2
Int32
SignalColumn_VisibleIndex
0
Int32
MuxColumn_VisibleIndex
-1
Int32
SubframeColumn_VisibleIndex
-1
Int32
PhysColumn_VisibleIndex
1
Int32
RawColumn_VisibleIndex
2
Int32
UnitColumn_VisibleIndex
-1
Int32
SymbolicValueColumn_VisibleIndex
3
Int32
TxAckColumn_VisibleIndex
-1
Int32
SigLenColumn_VisibleIndex
-1
Int32
MessageColumn_Width
264
Int32
RxNodeColumn_Width
432
Int32
TxNodeColumn_Width
1036
Int32
ButtonColumn_Width
64
Int32
SignalColumn_Width
75
Int32
MuxColumn_Width
75
Int32
SubframeColumn_Width
75
Int32
PhysColumn_Width
75
Int32
RawColumn_Width
75
Int32
UnitColumn_Width
75
Int32
SymbolicValueColumn_Width
75
Int32
TxAckColumn_Width
25
Int32
SigLenColumn_Width
75
TypeRef:4
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:7
7
Int32
Grouping
0
Boolean
RxNodeColumn_Visible
True
Boolean
SignalColumn_Visible
True
Boolean
MuxColumn_Visible
False
Boolean
SubframeColumn_Visible
False
Boolean
PhysColumn_Visible
True
Boolean
RawColumn_Visible
True
Boolean
UnitColumn_Visible
False
Boolean
MessageColumn_Visible
False
Boolean
TxNodeColumn_Visible
True
Boolean
SymbolicValueColumn_Visible
True
Boolean
TxAckColumn_Visible
False
Boolean
SigLenColumn_Visible
False
Int32
RxNodeColumn_VisibleIndex
0
Int32
SignalColumn_VisibleIndex
0
Int32
MuxColumn_VisibleIndex
-1
Int32
SubframeColumn_VisibleIndex
-1
Int32
PhysColumn_VisibleIndex
1
Int32
RawColumn_VisibleIndex
2
Int32
UnitColumn_VisibleIndex
-1
Int32
MessageColumn_VisibleIndex
-1
Int32
TxNodeColumn_VisibleIndex
4
Int32
SymbolicValueColumn_VisibleIndex
3
Int32
TxAckColumn_VisibleIndex
-1
Int32
SigLenColumn_VisibleIndex
-1
Int32
RxNodeColumn_Width
75
Int32
SignalColumn_Width
75
Int32
MuxColumn_Width
75
Int32
SubframeColumn_Width
75
Int32
PhysColumn_Width
75
Int32
RawColumn_Width
75
Int32
UnitColumn_Width
75
Int32
MessageColumn_Width
75
Int32
TxNodeColumn_Width
75
Int32
SymbolicValueColumn_Width
75
Int32
TxAckColumn_Width
25
Int32
SigLenColumn_Width
75
TypeRef:4
SerializationVersion
11
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 1
EOF_BUS
CAN2
1
1
2
1707345901184 0 0 0 0 0 0 0 0 2000 0 1 0 
SS_BEGIN_COMMON_INFO
1
2
Channels
1
Misc
1
SS_END_COMMON_INFO

EOF_BUSDATA
0
_Start_VPRBSManager 1 
0 0x32 0x1 
_End_VPRBSManager
NodeSignalPanelBustypeCount 0
EOF_BUS

EOF_MBSSDATA
End_Of_Object VGBRealTimeBox 2
VWriteBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 2 0 1 -1 -1 -1 -1 0 542 1275 836
Write
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 542 1275 836
6 0 1027 169 0 0 300 180 300 180 0 61440 1 36756 1904 0 0 0 0 260 0 0 0 1 5 32767 1 59422 1 5 1017 174 4 6 518 1 0 1 0 1 169 0 59422 1 
END_OF_DOCK_INFO
1
2
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 402 423 595
6 1 0 0 0 0 0 0 423 189 0 0 1 36756 1904 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 1 0 0 0 1 197 0 59422 1 
END_OF_DOCK_INFO
0
2
0
0
1
1275 836
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 0 402 423 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1920 732
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 542 1275 836
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 0 0 0 0 0 0 0 0 0
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 363 1024 576
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 363 1024 576
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 542 1275 836
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{31AD4644-E941-452D-832D-80EF52A05036}
0
End_Of_Object VBoxRoot 4
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 3
2
VWriteControlAdapter 3 Begin_Of_Object
2
VControlAdapter 4 Begin_Of_Object
1
End_Of_Object VControlAdapter 4
1
3
WListVer 2
<VFileName V9 QL> 1 "..\Public\Documents\Vector\CANwin\8.0.71\CANwin Demos\templates" 
 0 1 1 1 1 0
 False 147 90 0 300 280 280 280
End_Of_Serialized_Data 3
End_Of_Object VWriteControlAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VWriteBox 2
VWinStore 2 Begin_Of_Object
1
22 2 3 -1 -1 -1 -1 -10000 -10000 -8980 -9233
End_Of_Child_List
End_Of_Object VWinStore 2
VWinStore 2 Begin_Of_Object
1
22 0 1 -1 -1 -1 -1 0 21 1021 739
End_Of_Child_List
End_Of_Object VWinStore 2
VChipMultibusConfig 2 Begin_Of_Object
1
Version 8 10
5 64
0
9 0
11 0
1
14 0
1
12 1
3
0 127 0 0 1 2900 10 0 0 0
1 1
4
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
13 0
2
15 0
7 0
16 0
1
End_Of_Object VChipMultibusConfig 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 0
0
End_Of_Object VChipConfigC200 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 1
0
End_Of_Object VChipConfigC200 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC005 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC005 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC527 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC527 2
VChipConfigC1000 2 Begin_Of_Object
1
1
1000 16000 0 0
1 35 1 0 2 0 0 0 0 0 0
1 1000 0
1
55 24 0
2 2 27 12 2 2 0
0
0 0
End_Of_Object VChipConfigC1000 2
VChipConfigC1000 2 Begin_Of_Object
1
2
1000 16000 0 0
1 35 1 0 2 0 0 0 0 0 0
1 1000 1
1
55 24 0
2 2 27 12 2 2 0
0
0 0
End_Of_Object VChipConfigC1000 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 0
0
End_Of_Object VChipConfigC462 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 1
0
End_Of_Object VChipConfigC462 2
0
13
3 0
5 0
6 0
7 0
8 0
9 0
11 0
13 0
14 0
15 0
16 0
17 0
18 0
VScanBaudrateConfiguration 2 Begin_Of_Object
1
2
256
1000
5
1000
1
0
1
256
1000
5
1000
1
0
1
End_Of_Object VScanBaudrateConfiguration 2
4
0
VPersistentPath 2 Begin_Of_Object
1
<VFileName V9 QL> 1 "config.cpd" 
End_Of_Object VPersistentPath 2
0
3
0
1
VTestSetupBox 2 Begin_Of_Object
1
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 0 0 1 -1 -1 -1 -1 384 105 1536 422

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 384 105 1536 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{5036A8F9-C6A8-46E9-99E3-0A860746B29A}
0
End_Of_Object VBoxRoot 4
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 3
1
2 0
0

END_OF_TEST_SETUP_DATA
END_OF_TEST_SETUP_BOX
End_Of_Object VTestSetupBox 2
VPlugInsPersistentWrapper 2 Begin_Of_Object
1
<PlugIns>
</PlugIns>
End_Of_Object VPlugInsPersistentWrapper 2
0
0
VMacroStreamer 2 Begin_Of_Object
2
VMacroManager 3 Begin_Of_Object
3
0
0
0
0
End_Of_Object VMacroManager 3
End_Of_Object VMacroStreamer 2
VSignalGeneratorStreamer 2 Begin_Of_Object
1
VAnlyzSigGeneratorManager 3 Begin_Of_Object
5
0
0
0
0
0
End_Of_Object VAnlyzSigGeneratorManager 3
End_Of_Object VSignalGeneratorStreamer 2
SignalGeneratorsReplay 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 2 3 -1 -1 -11 -45 211 228 1173 656
Signal Generators and Signal Replay
1

MDI_DOCK_INFO_END
5
1
6
2 3 -1 -1 -1 -1 211 228 1173 656
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
1
1920 865
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 211 228 827 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 211 228 827 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 211 228 827 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{747E60DE-E2ED-48BC-92FC-CD748ECE8AE1}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
0
0
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
2
1
2
1
<?xml version="1.0" encoding="utf-8"?>
<SymbolSelectionProperties xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<AreDiagQualifiersShown>false</AreDiagQualifiersShown>
<CurrentViewId>NetworkSymbols</CurrentViewId>
<SearchProperties>
<SearchTermHistory />
</SearchProperties>
<ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>5</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>6</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>9</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>7</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>8</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>10</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>11</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>12</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>13</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>14</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>15</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>/</Key>
<NodeTreeProperties />
</ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>Embedded/NetworkSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>Embedded/VariableSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
</ViewProperties>
</SymbolSelectionProperties>
END_OF_WORKSPACE_MEMBER_DATA
END_OF_WORKSPACE_MEMBER
1
0
0

END_OF_WORKSPACE_DATA

END_OF_WORKSPACE_CONFIGURATION
LinNMWindow 0
LinScopeWindow 0
VCanGlOpConf 2 Begin_Of_Object
1
1
2
End_Of_Object VCanGlOpConf 2
0
1
0
<End_of_SimulinkController>
StartOfComment
EndOfComment
15.2 SP2
VHILInterfaceMgrAnlyz 2 Begin_Of_Object
5
0
0
2809
0
3030
1
End_Of_Object VHILInterfaceMgrAnlyz 2
1
Begin_Of_Object VNetDiagComponent
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 2 2 3 -1 -1 -11 -45 378 105 734 964
BMS - Diagnostic Session Control
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -1 -1 378 105 734 964
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1920 527
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 105 1516 422
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{828196E0-FC62-442C-A407-9292AD94CA4A}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 3
39
APPDIR Vector.CANalyzer.EcuSessionControl.DLL
Vector.CANalyzer.EcuSessionControl, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.EcuSessionControl.EscWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
Title
1
Diagnostic Session Control
String
EcuName
1
BMS
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
4
UInt16
mMinor
2
UInt16
mPatch
12
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
VEcuSessionCtrlComponent
End_Of_Object VNetDiagComponent
BasicDiagnosticsEditor 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 -1 -1 204 115 1063 889
Basic Diagnostics
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 204 115 1063 889
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 204 115 820 461
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 204 115 820 461
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 204 115 820 461
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{75FD02C3-FF97-470B-B75F-00DE974F63A9}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
0
0
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANalyzer.BasicDiagnosticsEditor.DLL
Vector.CANalyzer.BasicDiagnosticsEditor, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.BasicDiagnosticsEditor.VBasicDiagnosticsEditorWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
0
CalculateExtendedStatistics 1
0
0
25
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.SymbolSelectionListBox.Data.SymbolMRUList
1
1
Int32
Count
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
J1939::VGlobalSettings 2 Begin_Of_Object
2
1
0
End_Of_Object J1939::VGlobalSettings 2
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 118 801 474
Start Values
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{CBEC2C89-372E-48C7-B185-9AF166260F31}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 3
424
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.StartValuesController
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValuesModel
3
StartValuesModel
3
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.GUISettings
4
GUISettings
4
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Boolean
SetValuesOnMeasurementStart
True

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValue, Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
6
StartValues
6

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValueGroup, Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
7
StartValuesGroups
7
TypeRef:5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:4
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.GUI.ColumnSettings, Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
8
ColumnSettings
9
TypeRef:5
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Array
_items
11
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValue
9
1
0
-1
Int32
_size
0
Int32
_version
0
--TextFormatter: End of Object--
TypeRef:7
7
Array
_items
12
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValueGroup
10
1
0
3
TypeRef:10
ArrayElement
13

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Object
11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
Int32
_size
1
Int32
_version
110
--TextFormatter: End of Object--
TypeRef:8
9
Array
_items
14
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.ColumnSettings
12
1
0
15
TypeRef:12
ArrayElement
15
TypeRef:12
ArrayElement
16
TypeRef:12
ArrayElement
17
TypeRef:12
ArrayElement
18
TypeRef:12
ArrayElement
19
TypeRef:12
ArrayElement
20
TypeRef:12
ArrayElement
21
TypeRef:12
ArrayElement
22
TypeRef:12
ArrayElement
23
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
Int32
_size
9
Int32
_version
158
--TextFormatter: End of Object--
TypeRef:10
13
Int32
StartValueGroup_ID
0
String
StartValueGroup_Name
1
Start values group1
String
StartValueGroup_Comment
1

Boolean
StartValueGroup_Active
True
Boolean
StartValueGroup_AutoPersist
False
TypeRef:5
SerializationVersion
24
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
15
Int32
mHandle
0
Int32
mDefaultWidth
25
Int32
mWidth
25
Int32
SortOrderInt
0
Int32
mVisibleIndex
1
TypeRef:5
SerializationVersion
25
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
3
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
16
Int32
mHandle
1
Int32
mDefaultWidth
142
Int32
mWidth
142
Int32
SortOrderInt
0
Int32
mVisibleIndex
2
--TextFormatter: End of Object--
TypeRef:12
17
Int32
mHandle
2
Int32
mDefaultWidth
130
Int32
mWidth
130
Int32
SortOrderInt
0
Int32
mVisibleIndex
3
--TextFormatter: End of Object--
TypeRef:12
18
Int32
mHandle
3
Int32
mDefaultWidth
84
Int32
mWidth
84
Int32
SortOrderInt
0
Int32
mVisibleIndex
5
--TextFormatter: End of Object--
TypeRef:12
19
Int32
mHandle
4
Int32
mDefaultWidth
120
Int32
mWidth
120
Int32
SortOrderInt
0
Int32
mVisibleIndex
6
--TextFormatter: End of Object--
TypeRef:12
20
Int32
mHandle
5
Int32
mDefaultWidth
70
Int32
mWidth
70
Int32
SortOrderInt
0
Int32
mVisibleIndex
4
--TextFormatter: End of Object--
TypeRef:12
21
Int32
mHandle
6
Int32
mDefaultWidth
55
Int32
mWidth
55
Int32
SortOrderInt
0
Int32
mVisibleIndex
0
--TextFormatter: End of Object--
TypeRef:12
22
Int32
mHandle
7
Int32
mDefaultWidth
198
Int32
mWidth
198
Int32
SortOrderInt
0
Int32
mVisibleIndex
8
--TextFormatter: End of Object--
TypeRef:12
23
Int32
mHandle
8
Int32
mDefaultWidth
40
Int32
mWidth
40
Int32
SortOrderInt
0
Int32
mVisibleIndex
7
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
VStandaloneLoggingUserConfig 2 Begin_Of_Object
4
0
VLogCfgData 3 Begin_Of_Object
12
1
1
0
1
1
0
0
0
1024
60
1
0
0
3
0
29
1
1
0
2
0
0
3
VLogExportPersister 4 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 4

End_Of_Serialized_Data 3
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging_.blf" 
0
0
1
30
80
410
1
1
1


<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging_{MeasurementIndex}.blf" 
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe" 
0
1
<VFileName V9 QL> 1 "..\15 (x64)\Templates\CANoe\Logging_{MeasurementIndex}.blf" 
0
b2bea8fb-bbae-4496-b50e-1d99734c6abc
1
VLoggingComment 4 Begin_Of_Object
1
0
End_Of_Object VLoggingComment 4
End_Of_Object VLogCfgData 3
0
VAutoRunPreLoggingCaplBox 3 Begin_Of_Object
1
<VFileName V9 QL> 0 "" 
0
End_Of_Object VAutoRunPreLoggingCaplBox 3
0
VTriggerCfgData 3 Begin_Of_Object
5
1
0
0
1
0
0
0
0
1
0
0
0
200000
0
0
1
1
5000
VEvCondBlock 4 Begin_Of_Object
1
VEvCondGroup 5 Begin_Of_Object
2
VEvCondPrimitive 6 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 6
1
0
0
End_Of_Object VEvCondGroup 5
End_Of_Object VEvCondBlock 4
VEvCondBlock 4 Begin_Of_Object
1
VEvCondGroup 5 Begin_Of_Object
2
VEvCondPrimitive 6 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 6
1
0
0
End_Of_Object VEvCondGroup 5
End_Of_Object VEvCondBlock 4
0
0
0
116
1
0
0
1
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 3
End_Of_Object VStandaloneLoggingUserConfig 2
Mapping::VMappingManager 2 Begin_Of_Object
4
1
Mapping::VMappingGroup 3 Begin_Of_Object
2
1
1
0
Static Mapping
1
Mapping::VMappingGroup 4 Begin_Of_Object
2
1
0
1
Group 1
0
0
0
End_Of_Object Mapping::VMappingGroup 4
0
0
End_Of_Object Mapping::VMappingGroup 3
0

End_Of_Object Mapping::VMappingManager 2
VTSystemControl 0
TestConfigurationSetup
VTestConfigurationSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 201 120 808 481
Test Setup for Test Units
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 201 120 808 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{AD3ED849-7244-4301-B45D-2F8D16DA3AC7}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
35
APPDIR Vector.CANoe.TestConfigurationSetup.DLL
Vector.CANoe.TestConfigurationSetup, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.TestConfigurationSetup.TestConfigurationSetup
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
TestConfigurationSetupPersistence
1
0;-1;-1;
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VTestConfigurationSetupWrapper 2
AFDXVLStatisticSysVars
NAFDX::NStatisticsMonitor::VSVClient 2 Begin_Of_Object
1
Begin_Of_Multi_Line_String
2
﻿<?xml version="1.0" encoding="utf-8"?>
<systemvariables version="4" />
End_Of_Serialized_Data 2
End_Of_Object NAFDX::NStatisticsMonitor::VSVClient 2
DocumentViewer
VDocumentViewerWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 118 801 473
Documents
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{CDDAFC54-497C-4AEC-B07E-5CBBD16670E2}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
37
APPDIR Vector.CANalyzer.DocumentViewer.DLL
Vector.CANalyzer.DocumentViewer, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DocumentViewer.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
SplitterExpanded
True
Int32
DocumentListHeight
150
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
0
End_Of_Object VDocumentViewerWrapper 2
SVDialogSettings
VSVDialogSettings 2 Begin_Of_Object
1
-1
-1
930
600
1
1
0
320
440
365
0
0
0
0
0
End_Of_Object VSVDialogSettings 2
FunctionBusDialogSettings
VFunctionBusDialogSettings 2 Begin_Of_Object
2
-1
-1
1140
550
300
300
-1
0
End_Of_Object VFunctionBusDialogSettings 2
AutomationSequences
VAutomationSequencesWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 118 801 473
Automation Sequences
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{85D9A417-54DF-47E0-BDA6-B4CDBEBEB276}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
34
APPDIR Vector.CANalyzer.AutomationSequences.DLL
Vector.CANalyzer.AutomationSequences, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.AutomationSequences.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Int32
SelectedTabPage
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VAutomationSequencesWrapper 2
LogFileConverter
VLogFileConverter 2 Begin_Of_Object
1
2
VLogExportPersister 3 Begin_Of_Object
7
1416
78171113
<VFileName V9 QL> 1 "..\..\BATCH_CONVERSION\BLF\S1 AIR_RMZ_P53AUDCA4CEA00078_BAK_2023-07-12_14_20_00_3.0.3.gen2.0708_1.blf" 
<VFileName V9 QL> 1 "..\..\..\..\..\janarthanan.s\Downloads\P53ABDCB9BHA00063_2406_3PM.csv" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.
None
0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
15549484050000
C:\Users\<USER>\Downloads\P53ABDCB9BHA00063_2406_3PM.blf1566151886/25/2023 9:11:52 PM6/25/2023 9:12:10 PM
133320758570000000
0
15549484050000
0
730
0
0.10000000000000001
0
0
0
End_Of_Object VLogExportPersister 3

End_Of_Serialized_Data 2
End_Of_Object VLogFileConverter 2
ThreadingSettings
VPersistentThreadingSettings 2 Begin_Of_Object
1
3
7
End_Of_Object VPersistentThreadingSettings 2
GlSignalSamplingSettings
GlLoggerConfig::VGlSignalSamplingSettings 2 Begin_Of_Object
1
0
End_Of_Object GlLoggerConfig::VGlSignalSamplingSettings 2
NodeLayerConfiguration
32
APPDIR Vector.CANoe.NodeLayer.Configuration.Persistency.DLL
Vector.CANoe.NodeLayer.Configuration.Persistency, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeLayer.Configuration.Persistency.Persistor
1
1
String
NodeLayers
7
<ProjectVariantPropertyModel>
  <FileFormatVersion>3</FileFormatVersion>
  <ProjectVariantProperties>
    <VariantProperties />
    <ModifiableParameters />
  </ProjectVariantProperties>
</ProjectVariantPropertyModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
ILConfigurationComponent
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 118 801 473

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{D82A35E3-A550-404A-A6B8-740E8E185479}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
217
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.ILConfigurationComponent
1
1
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.GUI.GUISettings
2
GUISettings
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
Boolean
DbcSettingsAvailable
True
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.GUI.GUISettingsDbc
4
GUISettingsDbc
4
TypeRef:3
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:4
4
Int32
BottomPanelSize
289
Int32
PropertyGridSize
240
Boolean
BottomPanelExpanded
True
Boolean
PropertyGridExpanded
True
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.GUI.CommonGUISettings
5
CommonSettings
6
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.GUI.ColumnSettings
6
ColumnSettingsForFrames
7
TypeRef:6
ColumnSettingsForSignals
8
TypeRef:3
SerializationVersion
9
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:5
6
Int32
DialogWidth
754
Int32
DialogHeight
540
Int32
DialogPosX
306
Int32
DialogPosY
90
String
ActiveFilters
1

TypeRef:3
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
7
Int32
NumberOfEntries
14
String
ColumnInfo0
1
Vector;GenMsgILSupport;3;75;0
String
ColumnInfo1
1
Vector;GenMsgSendType;4;75;0
String
ColumnInfo2
1
Vector;GenMsgCycleTime;5;85;0
String
ColumnInfo3
1
Vector;GenMsgCycleTimeFast;6;109;0
String
ColumnInfo4
1
Vector;GenMsgFastOnStart;7;195;0
String
ColumnInfo5
1
Vector;GenMsgStartDelayTime;8;88;0
String
ColumnInfo6
1
Vector;GenMsgDelayTime;9;86;0
String
ColumnInfo7
1
Vector;GenMsgNrOfRepetition;10;75;0
String
ColumnInfo8
1
Vector;DiagRequest;-1;75;0
String
ColumnInfo9
1
Vector;DiagResponse;-1;81;0
String
ColumnInfo10
1
Vector;DiagState;-1;75;0
String
ColumnInfo11
1
Vector;NmMessage;-1;75;0
String
ColumnInfo12
1
Vector;NmAsrMessage;-1;93;0
String
ColumnInfo13
1
Vector;TpTxIndex;-1;75;0
TypeRef:3
SerializationVersion
11
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
8
Int32
NumberOfEntries
2
String
ColumnInfo0
1
Vector;GenSigSendType;2;87;0
String
ColumnInfo1
1
Vector;GenSigInactiveValue;3;87;0
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
ScenarioManager
NWlan::VCar2xScenarioManagerWnd 2 Begin_Of_Object
3
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 378 146 1516 586
Car2x Scenario Manager
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 378 146 1516 586
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{C92E6A8F-3C55-4D54-8C67-FA830F4292B7}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
31
APPDIR Vector.CANalyzer.Wlan.ScenarioManager.DLL
Vector.CANalyzer.Wlan.ScenarioManager, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Wlan.ScenarioManagerProxy
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3

1

End_Of_Object NWlan::VCar2xScenarioManagerWnd 2
Multimedia
VMultimediaWrapper 2 Begin_Of_Object
1
0
End_Of_Object VMultimediaWrapper 2
FrameworkData
70
APPDIR Vector.CANalyzer.Framework.DLL
Vector.CANalyzer.Framework, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Framework.SerializationStore
1
1

System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.HashSet`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]
2
WindowFavorites
2

System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.HashSet`1[[Vector.CANalyzer.Framework.FavoritesManager+DialogCommandInfo, Vector.CANalyzer.Framework, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
3
DialogFavorites
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
Int32
Version
7

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.GenericEqualityComparer`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]
5
Comparer
5
Int32
Capacity
0
--TextFormatter: End of Object--
TypeRef:3
3
Int32
Version
7

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.ObjectEqualityComparer`1[[Vector.CANalyzer.Framework.FavoritesManager+DialogCommandInfo, Vector.CANalyzer.Framework, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
6
Comparer
6
Int32
Capacity
0
--TextFormatter: End of Object--
TypeRef:5
5
--TextFormatter: End of Object--
TypeRef:6
6
--TextFormatter: End of Object--
VCommonMacroSettings
VCommonMacroSettings 2 Begin_Of_Object
1
3
YYYY-MM-DD_hh-mm-ss
End_Of_Object VCommonMacroSettings 2
EthernetSettings
NEthernet::VGlobalSettings 2 Begin_Of_Object
3
1
-1
1
End_Of_Object NEthernet::VGlobalSettings 2
VSymbolSelectionDialogSettings
VSymbolSelectionDialogSettings 2 Begin_Of_Object
1
Begin_Of_Multi_Line_String
2
<?xml version="1.0" encoding="utf-8"?>
<SymbolSelectionProperties xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<AreDiagQualifiersShown>true</AreDiagQualifiersShown>
<CurrentViewId>None</CurrentViewId>
<SearchProperties>
<SearchTermHistory>
<SearchTerm>vehicle</SearchTerm>
<SearchTerm>grade</SearchTerm>
</SearchTermHistory>
</SearchProperties>
<ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>5</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>6</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>9</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>7</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>8</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>12</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>13</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>14</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>15</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>16</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>17</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>10</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>11</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>18</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>/</Key>
<NodeTreeProperties />
</ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>1/NetworkSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>130023426/NetworkSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
</ViewProperties>
</SymbolSelectionProperties>
End_Of_Serialized_Data 2
End_Of_Object VSymbolSelectionDialogSettings 2
FunctionBusData
NFunctionBus::NDataModel::VFunctionBusData 2 Begin_Of_Object
8
0
NFunctionBus::NDataModel::VBindingConfiguration 3 Begin_Of_Object
1
4
Abstract
CAPL
C#
Mapping
End_Of_Object NFunctionBus::NDataModel::VBindingConfiguration 3
End_Of_Object NFunctionBus::NDataModel::VFunctionBusData 2
OfflineConfig
VOfflineConfigWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 5 0 1 -1 -1 -8 -31 254 165 1268 614
Offline Mode
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 254 165 1268 614
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
5
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1268 614
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{0F499E6B-AA60-4299-BA24-2C8C272F79C8}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 4
168
APPDIR Vector.CANalyzer.OfflineMode.GUI.DLL
Vector.CANalyzer.OfflineMode.GUI, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.OfflineMode.GUI.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
OverallModel
134
<?xml version="1.0" encoding="utf-8"?>
<OfflineConfigOverallModel xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<DataModel xmlns="" i:type="OfflineConfigDataModel">
<LogFileMergeMode>ByMeasurementTime</LogFileMergeMode>
<RootGroup xmlns="" i:type="RootGroup">
<OfflineSources xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
<d4p1:anyType xmlns="" i:type="LogFile">
<FileName>..\..\..\030723_8P_P_VIN 0095_S1 AIR_R_ANBU_65.blf</FileName>
<CurrentTargetIds>
<d4p1:string>8d5e35d3-5f4c-47d3-8fa8-ab7ce93976c3</d4p1:string>
</CurrentTargetIds>
<Id>ea5c324d-b0dc-4cc4-8031-cc7311e1e31f</Id>
<IsActive>true</IsActive>
<ChannelMappingSetId i:nil="true" />
<UserOffset>0</UserOffset>
</d4p1:anyType>
</OfflineSources>
<Id>7e4ad504-cf9f-4d05-8cb4-ce85101a864d</Id>
<UserOffset>0</UserOffset>
</RootGroup>
<ChannelMappingSets xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
<DefaultChannelMappingSetId i:nil="true" />
<BreakOnCondition>false</BreakOnCondition>
<BreakpointOffset>PT0S</BreakpointOffset>
<BreakpointOffsetMode>NoBreak</BreakpointOffsetMode>
<TimeRangeMode>EntireRange</TimeRangeMode>
<TimeRangeStart i:nil="true" />
<TimeRangeEnd i:nil="true" />
</DataModel>
<GuiModel xmlns="" i:type="OfflineConfigGuiModel">
<ConfigDialogState xmlns="" i:type="ConfigDialogState">
<Left i:nil="true" />
<Top i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
</ConfigDialogState>
<FrontEndViewState xmlns="" i:type="FrontEndViewState">
<ColumnStates xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>IsActive</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>0</VisiblePosition>
<Width>56</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileTitle</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>1</VisiblePosition>
<Width>190</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>MeasurementStart</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>2</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>MeasurementEnd</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>3</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>UserOffset</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>4</VisiblePosition>
<Width>100</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileFormat</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>5</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FormatVersion</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>6</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>DisplayTargetId</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>7</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileSize</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>8</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>UncompressedSize</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>9</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileCreator</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>10</VisiblePosition>
<Width>140</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>ChannelMappingSet</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>11</VisiblePosition>
<Width>100</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>NumberOfObjects</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>12</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>IndexField</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>13</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileName</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>14</VisiblePosition>
<Width>227</Width>
</d4p1:anyType>
</ColumnStates>
<ExtensibleMetadataHeight>150</ExtensibleMetadataHeight>
<IsExtensibleMetadataExpanded>false</IsExtensibleMetadataExpanded>
</FrontEndViewState>
</GuiModel>
</OfflineConfigOverallModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VOfflineConfigWrapper 2
CanDbSettings
VCanDbSettings 2 Begin_Of_Object
1
255































































































































































































































































End_Of_Object VCanDbSettings 2
CANstressNGSettings
CANstressNG::VCANstressNGMgr 2 Begin_Of_Object
1
0
34
255
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
End_Of_Object CANstressNG::VCANstressNGMgr 2
FunctionBusSetup
VFunctionBusSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 1 0 1 -1 -1 -1 -1 254 165 1362 846
Function Bus Setup
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 254 165 1362 846
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{5AD7C5F8-01CC-4DDC-B8C6-F0FB24D57B9E}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 4
236
APPDIR Vector.CANoe.FunctionBus.GUI.DLL
Vector.CANoe.FunctionBus.GUI, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.FunctionBusSetup.FunctionBusSetupComponent
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
DataModel
72
<?xml version="1.0" encoding="utf-8"?>
<DataModel xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<ViewDataModel xmlns="" i:type="ViewDataModel">
<ApplicationModelsViewState xmlns="" i:type="ApplicationModelsViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
</ApplicationModelsViewState>
<BindingViewState xmlns="" i:type="BindingViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SplitterPositionLeft i:nil="true" />
<SplitterPositionRight i:nil="true" />
<SplitterState i:nil="true" />
<ExpandedBindingBlockRawValue i:nil="true" />
<IsMaximized i:nil="true" />
</BindingViewState>
<CommunicationTimingsViewState xmlns="" i:type="CommunicationTimingsViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SelectedTabPageIndex i:nil="true" />
</CommunicationTimingsViewState>
<CommunicationViewState xmlns="" i:type="CommunicationViewState">
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
<SelectedViewIndex i:nil="true" />
</CommunicationViewState>
<TimingsViewState xmlns="" i:type="TimingsViewState">
<SplitterPosition i:nil="true" />
</TimingsViewState>
<DataSourcesViewState xmlns="" i:type="DataSourcesViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SplitterPosition i:nil="true" />
<SymbolSelectionControlState i:nil="true" />
</DataSourcesViewState>
<InterfacesViewState xmlns="" i:type="InterfacesViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</InterfacesViewState>
<ParticipantsViewState xmlns="" i:type="ParticipantsViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
</ParticipantsViewState>
<MainViewState xmlns="" i:type="MainViewState">
<SplitterPosition i:nil="true" />
</MainViewState>
<ParticipantSendModelViewState xmlns="" i:type="ParticipantSendModelViewState">
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</ParticipantSendModelViewState>
</ViewDataModel>
</DataModel>
String
SystemTreePersistency
18
<?xml version="1.0" encoding="utf-8"?>
<Main xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<SystemUnderTest>
<Id>598fa80c-9867-4f85-b13e-dc30aa5349c4</Id>
<Children />
<Name>System Under Test</Name>
</SystemUnderTest>
<TestEnvironment>
<Id>2dd0fc4e-0f39-4e51-bb4a-29c3c81d2dd9</Id>
<Children />
<Name>Test Environment</Name>
</TestEnvironment>
<Custom>
<Id>849260e3-5c5c-4979-9dc2-dbbad8d40301</Id>
<Children />
<Name>User-Defined System Setup</Name>
</Custom>
</Main>
String
UseCaseManagerPersistency
87
<?xml version="1.0" encoding="utf-8"?>
<UseCaseManager xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<AppliedUseCase></AppliedUseCase>
<UseCases>
<UseCase>
<Id>719a35a5-fc21-47f2-857f-94dde4ea1b36</Id>
<Name>Environment Simulation</Name>
<FolderSettings>
<UseCaseFolderSettings>
<FolderId>598fa80c-9867-4f85-b13e-dc30aa5349c4</FolderId>
<State>3</State>
<Measure>2</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
<UseCaseFolderSettings>
<FolderId>2dd0fc4e-0f39-4e51-bb4a-29c3c81d2dd9</FolderId>
<State>2</State>
<Measure>1</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
<UseCaseFolderSettings>
<FolderId>849260e3-5c5c-4979-9dc2-dbbad8d40301</FolderId>
<State>1</State>
<Measure>2</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
</FolderSettings>
</UseCase>
<UseCase>
<Id>92c96ab8-efab-4876-9cad-95ac903bb633</Id>
<Name>Test The Tester</Name>
<FolderSettings>
<UseCaseFolderSettings>
<FolderId>598fa80c-9867-4f85-b13e-dc30aa5349c4</FolderId>
<State>2</State>
<Measure>2</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
<UseCaseFolderSettings>
<FolderId>2dd0fc4e-0f39-4e51-bb4a-29c3c81d2dd9</FolderId>
<State>2</State>
<Measure>1</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
<UseCaseFolderSettings>
<FolderId>849260e3-5c5c-4979-9dc2-dbbad8d40301</FolderId>
<State>1</State>
<Measure>2</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
</FolderSettings>
</UseCase>
<UseCase>
<Id>688bc35f-de96-4dc0-a3f8-9f341033fcb9}</Id>
<Name>Measure</Name>
<FolderSettings>
<UseCaseFolderSettings>
<FolderId>598fa80c-9867-4f85-b13e-dc30aa5349c4</FolderId>
<State>3</State>
<Measure>1</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
<UseCaseFolderSettings>
<FolderId>2dd0fc4e-0f39-4e51-bb4a-29c3c81d2dd9</FolderId>
<State>1</State>
<Measure>1</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
<UseCaseFolderSettings>
<FolderId>849260e3-5c5c-4979-9dc2-dbbad8d40301</FolderId>
<State>1</State>
<Measure>2</Measure>
<Binding>10</Binding>
<DoBinding>Unchanged</DoBinding>
</UseCaseFolderSettings>
</FolderSettings>
</UseCase>
</UseCases>
</UseCaseManager>
String
FolderViewGuiPersistency
9
<?xml version="1.0" encoding="utf-8"?>
<FolderViewGui xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<NameColumnWidth>150</NameColumnWidth>
<StateColumnWidth>100</StateColumnWidth>
<RoleColumnWidth>70</RoleColumnWidth>
<BindingColumnWidth>70</BindingColumnWidth>
<SubSystemColumnWidth>100</SubSystemColumnWidth>
<RtServerColumnWidth>70</RtServerColumnWidth>
</FolderViewGui>
String
SilTypeLibManagerPersistency
4
<?xml version="1.0" encoding="utf-8"?>
<SilTypeLibManager xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<SilTypeLibCollection />
</SilTypeLibManager>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
5
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VFunctionBusSetupWrapper 2
ApplicationModelSetup
VApplicationModelSetup 2 Begin_Of_Object
1
0
End_Of_Object VApplicationModelSetup 2
EthernetPortBasedVLANSettings
NEthernet::VPortBasedVLANSettings 2 Begin_Of_Object
1
0
End_Of_Object NEthernet::VPortBasedVLANSettings 2
DiagnosticParameterWindow
VDiagnosticParameterWindowWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 3 0 1 -1 -1 -1 -1 254 165 1186 840
Diagnostic Parameters
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1920 865
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 254 165 1186 840
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
5
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
5
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 254 165 1018 664
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{E03CE6B6-09E5-48B0-82A3-326DD5D58E34}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 4
50
APPDIR Vector.CANalyzer.DiagnosticParameterWindow.DLL
Vector.CANalyzer.DiagnosticParameterWindow, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DiagnosticParameterWindow.DiagnosticParameterWindow
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
ParameterWindowCtrlPersistence
1
-1;False;0,200,True;1,60,True;2,60,True;3,75,True;4,21,True;5,50,True;6,75,True;
APPDIR Vector.CANalyzer.DiagnosticParameterWindow.DLL
Vector.CANalyzer.DiagnosticParameterWindow, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DiagnosticParameterWindow.Definitions.LastRecentSearchPopupItemList
3
RecentSearchItems
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Int32
Version
1
Int32
Count
0
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VDiagnosticParameterWindowWrapper 2
ParticipantModelSetup
VParticipantModelSetup 2 Begin_Of_Object
2
0
0
End_Of_Object VParticipantModelSetup 2
SimSetupPorts
VSSGlobalPortList 2 Begin_Of_Object
5
0
1
0
0
0
0
End_Of_Object VSSGlobalPortList 2
VttTapClient
VttTapClientPersist 2 Begin_Of_Object
1
0
End_Of_Object VttTapClientPersist 2
VarCodeView
VVarCodeViewWrapperManager 2 Begin_Of_Object
1
0
End_Of_Object VVarCodeViewWrapperManager 2
ConnectivitySettings
Connectivity::VConnectivitySettings 2 Begin_Of_Object
7



0

0



0


1
0


0

0
4
End_Of_Object Connectivity::VConnectivitySettings 2
ErtSysVars
VErtSystemVariableManagerAnlyz 2 Begin_Of_Object
1
0
End_Of_Object VErtSystemVariableManagerAnlyz 2
PDUIGComponentManager
0
MGWSettings
VMGWSettings 2 Begin_Of_Object
1
0
End_Of_Object VMGWSettings 2
FunctionBusInteractiveStimulation
NFunctionBusInteractiveStimulation::VStimulationManager 2 Begin_Of_Object
1
0
End_Of_Object NFunctionBusInteractiveStimulation::VStimulationManager 2
End_Of_Object VGlobalConfiguration 1

import subprocess
import sys
from loguru import logger
import sys
import os

# Create logs directory if it doesn't exist
os.makedirs("logs/call_single_convert", exist_ok=True)

# Configure logger
logger.remove()  # Remove default handler
logger.add(
    "logs/call_single_convert/info.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="INFO",
    rotation="1 day"
)
logger.add(
    "logs/call_single_convert/debug.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="DEBUG",
    rotation="1 day"
)
logger.add(sys.stderr, level="INFO")  # Also log to stderr
# Sample paths (replace with actual test files if available)
config_path = r"C:\Users\<USER>\Documents\CODES\batch_blf_csv_cron\config\gen_1\config.cfg"
blf_path = r"C:\Users\<USER>\Documents\CODES\batch_blf_csv_cron\blf\gen_1\vin_test.blf"
output_path = r"C:\Users\<USER>\Documents\CODES\batch_blf_csv_cron\output\asc\gen_1\vin_test.asc"

cmd = [
    "cscript.exe",
    "convert_single.vbs",
    "--config-path", config_path,
    "--blf-path", blf_path,
    "--output", output_path
]

logger.info(f"Usable command: {' '.join(cmd)}")
try:
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
    print("STDOUT:")
    print(result.stdout)
    print("STDERR:")
    print(result.stderr)
except subprocess.CalledProcessError as e:
    print("Error running convert_single.vbs:")
    print(e)
    sys.exit(1)
# Tasks: Robust BLF Queue and Download Management (14May251950)

- [ ] Update queue database schema:
    - [ ] Add `drive_filename`, `local_filename`, `downloaded`, `subfolder`, `file_id` fields
- [ ] Refactor polling logic:
    - [ ] Recursively scan all subfolders in Drive `BLF_FILES`
    - [ ] Update/insert records in DB for each file
    - [ ] Set `downloaded = False` for new files
- [ ] Implement download logic:
    - [ ] For each record with `downloaded = False`, download to correct local subfolder
    - [ ] Replace illegal Windows filename characters in `local_filename`
    - [ ] Set `downloaded = True` after successful download
- [ ] Update conversion/processing logic:
    - [ ] Only process files with `downloaded = True`
    - [ ] Use `local_filename` and subfolder for local path
    - [ ] Add config mapping for different subdirectories
- [ ] Add sync/resilience features:
    - [ ] Mark as removed in DB if file deleted from Drive
    - [ ] Reset `downloaded` if file re-uploaded/changed
- [ ] Test cases:
    - [ ] Files with illegal characters
    - [ ] Nested subfolders
    - [ ] Multiple new files
- [ ] Update documentation:
    - [ ] README and developer docs for new DB schema and workflow 
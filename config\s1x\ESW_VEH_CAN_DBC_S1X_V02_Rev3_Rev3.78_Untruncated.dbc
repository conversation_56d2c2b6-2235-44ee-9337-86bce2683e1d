VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: Tester_Tool Fast_Charger Slow_Charger Cluster MCU BMS TCU VCU BMS00 HU EOL
VAL_TABLE_ Vtsig_SettingsBottomDisplay 3 "HIGH" 2 "MID" 1 "LOW" 0 "SNA" ;
VAL_TABLE_ Vtsig_SettingsTopDisplay 3 "Failed" 2 "Applied" 1 "Ecomaxset" 0 "SNA" ;
VAL_TABLE_ Vtsig_BBW_setting_from_TCU 3 "High" 2 "Medium" 1 "LOW" 0 "OFF" ;
VAL_TABLE_ Vtsig_ready_to_sleep 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Tow_protection_API 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Take_me_home_lights_API 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_HIL_HOLD_FEATURE_ENABLE 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Early_Beta_Access 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_New_Upgrades 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Mode_Fencing 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Time_Fencing 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Geo_Fencing 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Proximity_L_U 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Energy_Insights 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_TP_HyperCharge 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_TP_FasterCharge 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Advanced_Regen 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_Tech_Pack_Charging 1 "Enable" 0 "Disable" ;
VAL_TABLE_ Vtsig_VIN_Status_Ack 1 "Sent positive ack" 0 "Didn't receive msg" ;
VAL_TABLE_ Vtsig_Eflash_State 1 "Mount Success" 0 "Mount Fail" ;
VAL_TABLE_ Vtsig_Ota_pending_Status 1 "Ota pending On Unlock" 0 "No ota Pending" ;
VAL_TABLE_ Vtsig_Mqtt_Status 1 "Mqtt connection Success" 0 "Mqtt connection Failure" ;
VAL_TABLE_ Vtsig_Flash_mount_status 1 "Mount_Success" 0 "Mount_Fail" ;


BO_ ********** VECTOR__INDEPENDENT_SIG_MSG: 0 Vector__XXX
 SG_ Debug2_0 : 0|16@1- (1,0) [-32767|32767] "" Vector__XXX

BO_ 824 PM_eDrive_stages: 8 BMS
 SG_ PM_eDrive_stage4 : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PM_eDrive_stage3_End : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PM_eDrive_stage3 : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PM_eDrive_stage2 : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PM_eDrive_stage1 : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1637 Setting_Displays: 8 TCU
 SG_ SettingsTopDisplay : 0|8@1+ (1,0) [0|255] ""  Cluster
 SG_ SettingsBottomDisplay : 8|8@1+ (1,0) [0|255] ""  Cluster

BO_ 1056 DIY_Mode_Name: 8 TCU
 SG_ DIY_Mode : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Cluster

BO_ 1684 Hotspot_Passcode: 8 TCU
 SG_ ECOS_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Cluster

BO_ 1049 BMS_Voltage_Out_Sense: 8 BMS00
 SG_ Rat_Bite_Counter : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ AI_PB13_12V_OUT_SENSE : 0|16@1+ (0.001,0) [0|65.535] "" Vector__XXX
 SG_ AI_PB12_3V3_SENSE : 16|16@1+ (0.001,0) [0|65.535] "" Vector__XXX

BO_ 785 MCU_Vehcile_Type_Data: 8 MCU
 SG_ Battrery_Kwh : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Vehicle_Type : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 823 Reset_Signals: 8 MCU
 SG_ NVM_write_Counter : 28|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reset_fault_status : 12|4@1+ (1,0) [0|15] ""  BMS
 SG_ Reset_fault_Counter : 16|4@1+ (1,0) [0|15] ""  BMS
 SG_ Reset_Counter : 20|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reset_Cause : 0|12@1+ (1,0) [0|4095] ""  BMS

BO_ 1587 HU_HW_Version_Info: 8 Cluster
 SG_ LCD_Unique_ID : 0|32@1+ (1,0) [0|4294967295] ""  TCU

BO_ 1927 TCU_Diag_Request: 8 TCU
 SG_ TCU_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS00

BO_ 1047 BMS_info_1: 8 BMS00
 SG_ Reserve : 32|32@1+ (1,0) [0|4294967295] ""  TCU
 SG_ BMS_unique_part_number : 0|32@1+ (1,0) [0|4294967295] ""  TCU

BO_ 821 Auto_tuning_process_Status: 8 MCU
 SG_ B2B_Rotation_Dir_Status : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Offset_Angle_Mechanical_Degree : 8|16@1+ (1,0) [0|360] ""  EOL
 SG_ Calibration_result : 3|3@1+ (1,0) [0|7] ""  EOL
 SG_ Calibration_State : 0|3@1+ (1,0) [0|7] ""  EOL

BO_ 801 Regen_Energy: 8 MCU
 SG_ Total_Regen_Energy_WH : 32|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ BBW_Regen_Energy_WH : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 822 ML_Info: 8 MCU
 SG_ ML_RegenTrq_inp : 1|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ML_RefTrq : 25|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ML_ReduceFlag_bool : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ML_RearBrakeVol : 33|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ML_ABSRefSpeed : 9|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1636 TP_FeatureV2_Available: 8 BMS
 SG_ Reserved_TP5 : 63|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CALLING : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CRUISE_CONTROL : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ADVANCED_REGEN_V2 : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FAST_CHARGING_V2 : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HYPER_CHARGING_V2 : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HILL_HOLD_V2 : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP4 : 62|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP3 : 61|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP2 : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP1 : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FREE_ROAM_VIEW : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ INTER_CITY_TRIP_PLANNER : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ROAD_TRIP : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PUSH_LOCATION_TO_SCOOTER : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MAPS_ON_HMI : 54|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MOODS : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CONCERT_MODE : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PLAY_MUSIC : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PARTY_MODE : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CUSTOM_MOTOR_SOUNDS : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MEGAPHONE : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SCOOTER_WIDGETS : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NOTIFICATION_CENTER : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FALL_DETECTION : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LIVE_LOCATION_SHARING : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HAZARD_LIGHTS : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EMERGENCY_SOS : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HARDWARE_FAILURE_PREDICTION : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RIDE_JOURNAL : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RIDE_STATS : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ COMMUNITY : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FIND_MY_SCOOTER : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PROFILES : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DOCS_ON_SCOOTER : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FASTER_CHARGING : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SLOW_CHARGING : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BRAKE_BY_WIRE_SETTINGS : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BRAKE_BY_WIRE : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AUTO_INDICATOR : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TRACTION_CONTROL : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FORCED_REGEN : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ COAST_REGEN : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SPEED_LIMIT_ALERT : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RANGE_PREDICTION : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SMART_PARK : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DIY_MODE : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ REVERSE_MODE : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ECO_MODE : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NORMAL_MODE : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SPORT_MODE : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CAPP_LOCK_UNLOCK : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HYPER_MODE : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CAPP_VEHICLE_STATE : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PASSCODE_UNLOCK_V2 : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EARLY_ACCESS_BETA_V2 : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NEW_UPGRADES_V2 : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PROXIMITY_V2 : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ENERGY_INSIGHTS_V2 : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MODE_FENCING_V2 : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TIME_FENCING_V2 : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GEO_FENCING_V2 : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TOW_TAMPER_V2 : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TAKE_ME_HOME_LIGHTS_V2 : 4|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 793 MCU_Unique_ID: 8 MCU
 SG_ Reserve : 32|32@1+ (1,0) [0|4294967295] ""  TCU
 SG_ MCU_unique_part_number : 0|32@1+ (1,0) [0|4294967295] ""  TCU

BO_ 1783 Move_OS_Tec_Pack_Status: 8 BMS
 SG_ Move_OS_Pack_Status : 0|8@1+ (1,0) [0|255] ""  BMS

BO_ 1046 Acceleration_Profile: 8 BMS00
 SG_ Range_Full_Chrg_Soc : 48|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ Range_Full_Chrg_Dist : 32|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ Acceleration_time_2 : 16|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ Acceleration_time_1 : 0|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool

BO_ 1632 Vehicle_Config_Data_8: 8 TCU
 SG_ Take_me_home_lights_API : 24|1@1+ (1,0) [0|1] ""  TCU
 SG_ Tow_protection_API : 23|1@1+ (1,0) [0|1] ""  TCU
 SG_ HIL_HOLD_FEATURE_ENABLE : 22|1@1+ (1,0) [0|1] ""  TCU
 SG_ Early_Beta_Access : 21|1@1+ (1,0) [0|1] ""  TCU
 SG_ New_Upgrades : 20|1@1+ (1,0) [0|1] ""  TCU
 SG_ Mode_Fencing : 19|1@1+ (1,0) [0|1] ""  TCU
 SG_ Time_Fencing : 18|1@1+ (1,0) [0|1] ""  TCU
 SG_ Geo_Fencing : 17|1@1+ (1,0) [0|1] ""  TCU
 SG_ Proximity_L_U : 16|1@1+ (1,0) [0|1] ""  TCU
 SG_ Energy_Insights : 15|1@1+ (1,0) [0|1] ""  TCU
 SG_ TP_HyperCharge : 14|1@1+ (1,0) [0|1] ""  TCU
 SG_ TP_FasterCharge : 13|1@1+ (1,0) [0|1] ""  TCU
 SG_ Advanced_Regen : 12|1@1+ (1,0) [0|1] ""  TCU
 SG_ Reserved_2 : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Reserved_1 : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1750 TP_TCU_Mesage: 8 TCU
 SG_ BBW_setting_from_TCU : 16|2@1+ (1,0) [0|3] ""  BMS
 SG_ OMS_Status : 8|8@1+ (1,0) [0|255] ""  BMS00,Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool
 SG_ Regen_setting_from_TCU : 4|2@1+ (1,0) [0|3] ""  BMS00
 SG_ Vacation_mode : 6|1@1+ (1,0) [0|1] ""  BMS
 SG_ HyperModeAvailable : 3|1@1+ (1,0) [0|1] ""  BMS00
 SG_ SportModeAvailable : 2|1@1+ (1,0) [0|1] ""  BMS00
 SG_ TimeFenceBreached : 1|1@1+ (1,0) [0|1] ""  BMS00
 SG_ GeoFenceBreached : 0|1@1+ (1,0) [0|1] ""  BMS00

BO_ 1622 Vehicle_Monitor_Mode: 8 BMS
 SG_ RESTRICT_ODO_IN_SERVICE : 11|1@1+ (1,0) [0|1] ""  TCU
 SG_ Parts_mismatch_speed_limit : 56|8@1+ (1,0) [0|255] ""  TCU
 SG_ Parts_mismatch_odo_limit : 48|8@1+ (1,0) [0|255] ""  TCU
 SG_ service_speed_limit : 40|8@1+ (1,0) [0|255] ""  TCU
 SG_ service_odo_limit : 32|8@1+ (1,0) [0|255] ""  TCU
 SG_ in_purchase_speed_limit : 24|8@1+ (1,0) [0|255] ""  TCU
 SG_ in_purchase_odo_limit : 16|8@1+ (1,0) [0|255] ""  TCU
 SG_ Monitor_Mode_Part_Mismatch : 10|1@1+ (1,0) [0|1] ""  TCU
 SG_ Monitor_Mode_Job_Open : 9|1@1+ (1,0) [0|1] ""  TCU
 SG_ Monitor_Mode_In_Purchase : 8|1@1+ (1,0) [0|1] ""  TCU
 SG_ MODE_Restriction : 0|8@1+ (1,0) [0|255] ""  TCU

BO_ 563 Network_Time_From_TCU: 8 TCU
 SG_ Network_Time_From_TCU : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS

BO_ 1081 Hyper_Charger_Due: 8 TCU
 SG_ Due_Status : 0|4@1+ (1,0) [0|15] ""  BMS

BO_ 1044 Spike_Status: 8 BMS
 SG_ Low_regen_nudge_ratio : 16|8@1+ (0.01,0) [0|2.55] "" Vector__XXX
 SG_ Low_regen_nudge_Counter : 8|8@1+ (1,0) [0|100] "" Vector__XXX
 SG_ Low_regen_nudge : 5|1@1+ (1,0) [0|1] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ Partmismatch_range_over : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ InPurchase_range_over : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ service_over_flag : 2|1@1+ (1,0) [0|1] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ VOOR_cutoff_flg_cspike : 1|1@1+ (1,0) [0|1] ""  Cluster,MCU,Tester_Tool
 SG_ cspike_fault : 0|1@1+ (1,0) [0|1] ""  Cluster,MCU,Tester_Tool

BO_ 1043 VIN_Status_Ack: 8 TCU
 SG_ VIN_Status_Ack : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1042 VIN_Status: 8 BMS
 SG_ VIN_Status : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1992 Cluster_Diagnostic_Response: 8 Cluster
 SG_ Cluster_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  TCU

BO_ 1562 ACK_CommandFromAPP: 8 BMS
 SG_ ACK_Command_From_APP : 0|8@1+ (1,0) [0|255] ""  TCU

BO_ 1697 CommandFromAPP: 8 TCU
 SG_ Command_From_APP : 0|8@1+ (1,0) [0|255] ""  BMS

BO_ 1674 Custom_mode_info: 8 TCU
 SG_ Eco_Max_Status : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Top_speed : 24|8@1+ (1,0) [0|255] ""  BMS
 SG_ Throttle_Sensitivity : 21|3@1+ (1,0) [0|7] ""  BMS
 SG_ Regen_Intensity : 18|3@1+ (1,0) [0|7] ""  BMS
 SG_ Default_Custom_Mode : 17|1@1+ (1,0) [0|1] ""  BMS
 SG_ Custom_Torque : 0|16@1+ (1,0) [0|65535] ""  BMS
 SG_ Custom_Mode_En : 16|1@1+ (1,0) [0|1] ""  BMS

BO_ 786 FoC_Debug_and_Faults: 8 MCU
 SG_ CAN_Tx_Error_Counter : 24|16@1+ (1,0) [0|65535] ""  BMS
 SG_ CAN_Failure_Counter : 40|16@1+ (1,0) [0|65535] ""  BMS
 SG_ Hardware_PhC_OverCurrent_Fault : 11|1@1+ (1,0) [0|1] ""  BMS
 SG_ Hardware_PhB_OverCurrent_Fault : 10|1@1+ (1,0) [0|1] ""  BMS
 SG_ AB_Pulse_Missing_Fault : 9|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseC_Ter_Tightening_Fault : 8|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseC_RMS_Overcurrent_Fault : 5|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseC_Current_Offset_Fault : 3|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseB_Ter_Tightening_Fault : 7|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseA_Ter_Tightening_Fault : 6|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseCpeak_Overcurrent_Fault : 1|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseBpeak_Overcurrent_Fault : 0|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseB_RMS_Overcurrent_Fault : 4|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_PhaseB_Current_Offset_Fault : 2|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_Can_Driver_Err_sts : 12|8@1+ (1,0) [0|255] ""  BMS

BO_ 791 MCU_DebugSignal1: 8 MCU
 SG_ MCU_TempA : 0|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ MCU_TempB : 16|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ MCU_TempC : 32|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ MCU_ADC_ThrottlePercent : 48|16@1- (1,0) [-32767|32767] ""  BMS

BO_ 790 MCU_DebugSignal4: 8 MCU
 SG_ Park_overspeed_flag : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Abnormal_brake_sensor_flag : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Flag_12V_Abnormal_In_Drive : 49|1@1+ (1,0) [0|1] ""  BMS
 SG_ Throttle_STB_Fault : 53|1@1+ (1,0) [0|1] ""  BMS
 SG_ Ignition_STB_Fault : 52|1@1+ (1,0) [0|1] ""  BMS
 SG_ Voltage_quadrature_axis : 0|16@1- (1,0) [-32768|32767] ""  BMS
 SG_ Voltage_direct_axis : 16|16@1- (1,0) [-32768|32767] ""  BMS
 SG_ Throttle_STG_Fault : 54|1@1+ (1,0) [0|1] ""  BMS
 SG_ Motor_tempsensor_STG_Fault : 55|1@1+ (1,0) [0|1] ""  BMS
 SG_ Supply_5V : 56|8@1+ (0.1,0) [0|25.5] ""  BMS
 SG_ Supply_3V3 : 40|9@1+ (0.01,0) [0|5.11] ""  BMS
 SG_ MCU_Ignition_12V : 32|8@1+ (0.1,0) [0|25.5] ""  BMS

BO_ 788 MCU_DebugSignal2: 8 MCU
 SG_ Mechanical_Brake_Pad_Wear_Warning : 48|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_Model_OP_sts1 : 0|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ MCU_Model_OP_sts2 : 16|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ MCU_Model_OP_Torq_Ref : 32|16@1- (1,0) [-32767|32767] ""  BMS

BO_ 769 PhaseCurrent: 8 MCU
 SG_ PhaseCurrentA : 0|16@1- (0.1,0) [-1000|1000] "Amps"  BMS
 SG_ PhaseCurrentB : 16|16@1- (0.1,0) [-1000|1000] "Amps"  BMS
 SG_ PhaseCurrentC : 32|16@1- (0.1,0) [-1000|1000] "Amps"  BMS

BO_ 802 MCU_BBW_Data: 8 MCU
 SG_ Avg_Brake_Volt : 16|16@1+ (0.1,0) [0|6553.5] "" Vector__XXX
 SG_ Brake_Time : 0|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ BBW_Brake_Energy : 32|24@1+ (1,0) [0|16777215] "" Vector__XXX

BO_ 800 MCU_Data_8: 8 MCU
 SG_ CA_DispBrake : 38|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Rear_Brake_Sensor_STGB_Fault : 37|1@1+ (1,0) [0|1] ""  BMS
 SG_ Front_Brake_Sensor_STGB_Fault : 36|1@1+ (1,0) [0|1] ""  BMS
 SG_ Brake_Press : 35|1@1+ (1,0) [0|1] ""  BMS
 SG_ Front_Brake_Analog_Voltage : 16|16@1+ (0.01,0) [0|655.35] ""  BMS
 SG_ RearWheelAntilock_Torque_Status : 32|3@1+ (1,0) [0|7] ""  BMS
 SG_ Rear_Brake_Analog_Voltage : 0|16@1+ (0.01,0) [0|655.35] ""  BMS

BO_ 775 Motor_Param2: 8 MCU
 SG_ Motor_ABS_Status : 51|8@1+ (1,0) [0|255] ""  BMS
 SG_ Can_Rx_OK : 50|1@1+ (1,0) [0|1] ""  BMS
 SG_ Can_Tx_OK : 49|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_Inverter_Enable_Cmd : 48|1@1+ (1,0) [0|1] ""  BMS
 SG_ MechAngle : 32|16@1+ (0.01,0) [0|360] "Degree"  BMS
 SG_ RPM : 16|16@1- (0.1,0) [-6000|6000] "RPM"  BMS
 SG_ Angle_digitalcount : 0|16@1+ (1,0) [0|2048] ""  BMS

BO_ 772 FOC_Currents: 8 MCU
 SG_ Id_reference : 0|16@1- (0.1,0) [-1000|1000] "Amps"  BMS
 SG_ Iq_reference : 16|16@1- (0.1,0) [-1000|1000] "Amps"  BMS
 SG_ Id_measured : 32|16@1- (0.1,0) [-1000|1000] "Amps"  BMS
 SG_ Iq_measured : 48|16@1- (0.1,0) [-1000|1000] "Amps"  BMS

BO_ 787 MCU_Data_1: 8 MCU
 SG_ Vehicle_speed_in_kmph : 0|16@1+ (0.1,0) [0|200] "kmph"  HU,BMS
 SG_ MCU_Temperature : 16|16@1+ (0.01,0) [0|600] "degC"  BMS
 SG_ Motor_Temprature : 32|16@1+ (0.01,0) [0|600] "degC"  BMS
 SG_ Motor_RPM : 48|16@1+ (1,0) [0|65535] "rpm"  BMS

BO_ 1758 BMS00_Cloud_Data_4: 8 BMS00
 SG_ Total_Amphr_Charged_in_the_Mode : 0|16@1+ (0.01,0) [0|100] "Amphr"  TCU
 SG_ Total_Kwhr_Charged_in_the_Mode : 16|16@1+ (0.01,0) [0|65] "Kwhr"  TCU
 SG_ Total_Active_Duration_of_the_Mode : 32|16@1+ (1,0) [0|65535] "Minutes"  TCU
 SG_ Total_Regenerative_Amphr : 48|16@1+ (0.01,0) [0|43] "Amphr"  TCU

BO_ 1742 Display_Sw_Version_Info: 8 BMS
 SG_ Display_Sw_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  TCU

BO_ 851 Display_Info: 8 TCU
 SG_ Brightness_Level_required_by_User : 56|8@1+ (1,0) [0|100] "%"  BMS

BO_ 1987 Cluster_Diagnostic_Request: 8 TCU
 SG_ Cluster_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Cluster

BO_ 1657 TCU_311_SW_Version_info: 8 TCU
 SG_ TCU_311_SW_Version : 0|24@1+ (1,0) [0|16777215] "" Vector__XXX
 SG_ EFlash_State : 56|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 562 Gps_Satellite_Data: 8 TCU
 SG_ Ota_Pending_status : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Mqtt_Status : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Flash_mount_status : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ LTE_State : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Number_of_Satellite_in_Use : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Number_of_Satellite : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ GPS_FIX : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 792 MCU_DebugSignal5: 8 MCU
 SG_ CPU_timer_10ms : 0|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ CPU_timer_100us : 16|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ Front_brake_offset : 32|16@1- (1,0) [-32767|32767] ""  BMS
 SG_ Rear_brake_offset : 48|16@1- (1,0) [-32767|32767] ""  BMS

BO_ 1782 Short_circuit_fault_data: 8 BMS
 SG_ Overcurrent_short_circuit_fault : 49|1@1+ (1,0) [0|1] ""  Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool
 SG_ Bus_voltage_fault_value : 33|16@1+ (0.001,0) [0|65.535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool
 SG_ Timer_fault_value : 17|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool
 SG_ Pack_voltage_fault_value : 1|16@1+ (0.1,0) [0|6553.5] ""  Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool
 SG_ Precharge_sht_circuit_fault_volt : 0|1@1+ (1,0) [0|1] ""  Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool

BO_ 1777 Malfunction_indicator: 8 BMS
 SG_ MIL_WL : 0|4@1+ (1,0) [0|2] ""  Cluster

BO_ 559 NetworkTimeAck: 8 BMS
 SG_ Network_Time_Ack : 0|2@1+ (1,0) [0|3] ""  Tester_Tool

BO_ 558 NetworkTime: 8 Tester_Tool
 SG_ Network_Time : 0|56@1+ (1,0) [0|7.20575940379279E+016] ""  BMS

BO_ 557 DTC_Fault_Request: 8 Tester_Tool
 SG_ Fault_Request : 0|1@1+ (1,0) [0|1] ""  BMS
 SG_ Fault_Clear : 1|1@1+ (1,0) [0|1] ""  BMS

BO_ 555 DTC_Info: 8 BMS
 SG_ DTC_Fault_Flag : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DTC_Fault_Time : 7|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ DTC_Fault : 0|6@1+ (1,0) [0|63] "" Vector__XXX

BO_ 554 HBC_Button_Status: 8 BMS
 SG_ Reverse_Btn_Sts : 2|2@1+ (1,0) [0|3] ""  Cluster
 SG_ Cruise_Btn_Sts : 0|2@1+ (1,0) [0|3] ""  Cluster

BO_ 552 BMS_DATA: 8 BMS
 SG_ Odo_diff_service : 48|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ CPW_12v : 24|1@1+ (1,0) [0|1] ""  Cluster
 SG_ TripMeter : 32|16@1+ (1,0) [0|65535] ""  Cluster
 SG_ OdometerValue : 0|24@1+ (0.1,0) [0|1677721.5] ""  Cluster

BO_ 1281 EV_Parameter_2: 8 BMS
 SG_ Reserved_5 : 48|16@1+ (1,0) [0|65535] ""  Fast_Charger
 SG_ Estimated_charging_time : 32|16@1+ (1,0) [0|65535] ""  Fast_Charger
 SG_ Maximum_Charging_time : 16|16@1+ (1,0) [0|65535] ""  Fast_Charger
 SG_ Charging_rate : 8|8@1+ (1,0) [0|100] ""  Fast_Charger
 SG_ Control_protocol_number : 0|8@1+ (1,0) [0|254] ""  Fast_Charger

BO_ 1280 EV_Parameter_1: 8 BMS
 SG_ DC_output_voltage_target_parameter : 32|16@1+ (0.1,0) [0|120] ""  Fast_Charger
 SG_ DC_output_voltage_limit_parameter : 48|16@1+ (0.1,0) [0|120] ""  Fast_Charger
 SG_ requested_DC_output_current : 16|16@1+ (0.1,0) [0|200] ""  Fast_Charger
 SG_ Reserved_4 : 14|2@1+ (1,0) [0|3] ""  Fast_Charger
 SG_ Digital_communication_toggle : 13|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Wait_request_to_delay_energy_transfer : 12|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_contactor_status : 9|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_charging_stop_control : 11|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_charging_position : 10|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ EV_charging_enabled : 8|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Reserved_3 : 6|2@1+ (1,0) [0|3] ""  Fast_Charger
 SG_ High_battery_temperature : 4|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_voltage_deviation_error : 5|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_current_deviation_error : 3|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_undervoltage : 2|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Battery_overvoltage : 1|1@1+ (1,0) [0|1] ""  Fast_Charger
 SG_ Energy_transfer_system_error : 0|1@1+ (1,0) [0|1] ""  Fast_Charger

BO_ 548 MCU_Data_4: 8 MCU
 SG_ Rotor_Angle : 21|9@1+ (1,0) [0|360] "" Vector__XXX
 SG_ Position_Counter : 10|11@1+ (1,0) [0|2047] "" Vector__XXX
 SG_ Motor_Frequency : 0|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ AC_Current_MCU : 47|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ Vehicle_Dir_flag : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DC_Current_MCU : 37|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ Bus_Voltage_MCU : 30|7@1+ (1,0) [0|100] "" Vector__XXX

BO_ 1754 BMS00_Cloud_Data_2: 8 BMS00
 SG_ BMS_NVM_Failure_Status : 20|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS00_9V_Supply_Status : 0|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS00_12V_Supply_voltage_Status : 1|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ SBC_Limp_Input : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ AFE_Fault_Input : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Charge_Over_Current : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Discharge_Over_current : 5|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ RTC_Interrupt : 6|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ RTC_Clock_IN : 7|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Charger_Plug_Sense : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Status : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS00_12V_Enable : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS00_9V_Supply_Disable : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS00_5V_Peripheral_Enable : 12|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Gate_Drivers_Enable : 13|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Charge_MOSFET_Enable : 14|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Discharge_MOSFET_Enable : 15|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Pre_Charge_Enable : 16|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ AFE_Reset_Command : 17|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Overload_Clear : 18|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Enable_3V3_Measure : 19|1@1+ (1,0) [0|1] ""  Tester_Tool

BO_ 1779 VehicleDiagMode_State: 8 Tester_Tool
 SG_ VehicleDiagMode : 0|4@1+ (1,0) [0|4] ""  BMS,Cluster

BO_ 1781 Test_Tool_Alive_Status: 8 Tester_Tool
 SG_ Test_Tool_Alive : 0|1@1+ (1,0) [0|1] ""  BMS

BO_ 2000 App_Cmd_Diagnostic_request: 8 BMS
 SG_ Vehicle_Type_Extended : 51|8@1+ (1,0) [0|255] ""  Tester_Tool

BO_ 1778 Indicator_Status: 8 Cluster
 SG_ HighBeam : 2|1@1+ (1,0) [0|1] ""  BMS
 SG_ LeftIndicator : 1|1@1+ (1,0) [0|1] ""  BMS
 SG_ RightIndicator : 0|1@1+ (1,0) [0|1] ""  BMS

BO_ 1757 BBW_Strength_info: 8 MCU
 SG_ BBW_Strength : 0|8@1+ (1,0) [0|255] ""  BMS

BO_ 31 IGN_Status_by_Cluster: 8 Cluster
 SG_ Cluster_IGN_Status : 0|3@1+ (1,0) [0|4] ""  MCU,BMS

BO_ 3 Cluster_NM_Message: 8 Cluster
 SG_ Key_Status : 8|8@1+ (1,0) [0|2] ""  BMS
 SG_ WakeUp_Sleep_Request : 0|3@1+ (1,0) [0|2] ""  MCU,BMS

BO_ 63 Cluster_to_Battery_Authentication_Request: 8 Cluster
 SG_ Cluster_Seed : 0|32@1+ (1,0) [0|4294967295] ""  BMS

BO_ 61 Battery_to_Cluster_Authentication_Response: 8 BMS
 SG_ BMS_Key : 0|32@1+ (1,0) [0|4294967295] ""  Cluster

BO_ 79 Cluster_to_Battery_Authentication_Status: 8 Cluster
 SG_ Auth_Status_BMS : 0|1@1+ (1,0) [0|1] ""  BMS

BO_ 64 MBMS_WakeUp_Reason: 8 BMS
 SG_ MBMS_Wake_Up_Reason : 0|3@1+ (1,0) [0|7] ""  Slow_Charger,Cluster,MCU

BO_ 151 MCUSleepWakeUpACK: 8 MCU
 SG_ MCU_Sleep_WakeUp_ACK : 0|2@1+ (1,0) [0|3] ""  Tester_Tool

BO_ 155 MCU_to_VCU_Authentication_Response: 8 MCU
 SG_ MCU_Key : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 325 Display_Readings: 8 Cluster
 SG_ Odometer : 0|24@1+ (1,0) [0|16777215] "" Vector__XXX

BO_ 850 Display_Info_4: 8 BMS
 SG_ Remaining_odorange : 17|8@1+ (1,0) [0|255] ""  Cluster
 SG_ Bootup_Screen : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Display_SoC : 0|16@1+ (1,0) [0|100] "%"  Cluster

BO_ 400 BMS_Parameter_6: 8 BMS
 SG_ Time_to_chargeSC_80 : 0|16@1+ (1,0) [0|65535] "Min"  Cluster
 SG_ Time_to_Full_chargeSC_100 : 16|16@1+ (1,0) [0|65535] "Min"  Cluster
 SG_ Time_to_chargeFC_80 : 32|16@1+ (1,0) [0|65535] "Min"  Cluster
 SG_ Time_to_chargeFC_100 : 48|16@1+ (1,0) [0|65535] "Min"  Cluster

BO_ 1744 CommandToCluster: 8 BMS
 SG_ Issue_number : 32|8@1+ (1,0) [0|255] ""  Cluster,Fast_Charger,MCU,Slow_Charger,Tester_Tool
 SG_ Command_To_Cluster : 0|8@1+ (1,0) [0|255] ""  Cluster
 SG_ Hazard_Icon : 24|4@1+ (1,0) [0|2] ""  Cluster

BO_ 1774 TCU_SW_Version_info: 8 TCU
 SG_ Scooter_SW_Info : 24|24@1+ (1,0) [0|16777215] "" Vector__XXX
 SG_ TCU_SW_version : 0|24@1+ (1,0) [0|16777215] ""  Tester_Tool

BO_ 1724 CommandFromTCU: 8 TCU
 SG_ Wifi_Connection_Status : 32|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ OTA_Installation_Progress : 16|8@1+ (1,0) [0|100] ""  Tester_Tool
 SG_ OTA_Installation_Message : 24|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Command_From_TCU : 0|16@1+ (1,0) [0|65535] ""  Tester_Tool

BO_ 1581 Charger_Connection_Status: 8 Cluster
 SG_ Charger_Connected : 0|2@1+ (1,0) [0|3] ""  BMS
 SG_ Charger_Plugged_In : 2|1@1+ (1,0) [0|1] ""  BMS

BO_ 1080 TIME: 8 BMS
 SG_ Year : 16|8@1+ (1,0) [0|255] ""  Cluster,Tester_Tool
 SG_ Month : 8|8@1+ (1,0) [0|12] ""  Cluster,Tester_Tool
 SG_ Date : 0|8@1+ (1,0) [0|31] ""  Cluster,Tester_Tool
 SG_ Hours : 48|8@1+ (1,0) [0|24] ""  Cluster,Tester_Tool
 SG_ Minutes : 56|8@1+ (1,0) [0|60] ""  Cluster,Tester_Tool

BO_ 288 Overall_Battery_Status_Info: 8 BMS
 SG_ FC_contactor_Positive_supply_command : 51|1@1+ (1,0) [0|1] ""  Slow_Charger
 SG_ Overall_Battery_Current : 0|16@1+ (0.01,-300) [-300|300] "Amp"  MCU
 SG_ Overall_SOC : 16|16@1+ (0.01,0) [0|100] "%"  MCU
 SG_ Overall_Charge_Voltage_limit : 32|16@1+ (0.001,0) [0|65.535] "V"  MCU
 SG_ Charger_Mode_Request : 48|3@1+ (1,0) [0|7] ""  MCU

BO_ 289 Overall_Battery_Current_Limit: 8 BMS
 SG_ Overall_Charge_current_limit : 0|16@1+ (0.01,0) [0|600] "Amp"  MCU
 SG_ Overall_Discharge_current_limit : 16|16@1+ (0.01,-400) [-400|0] "Amp"  MCU
 SG_ Overall_Regen_Power_Limit : 32|16@1+ (1,0) [0|65535] "Watts"  MCU
 SG_ Overall_Discharge_Power_Limit : 48|16@1+ (1,-50000) [-50000|0] "Watts"  MCU

BO_ 320 BMS_Parameter_Limit_2: 8 BMS
 SG_ Battery_Current_1 : 48|16@1+ (0.1,-3000) [-3000|3553.5] ""  Cluster,EOL,Fast_Charger,HU,MCU,Slow_Charger,TCU,Tester_Tool,VCU
 SG_ Regen_Power_Limit : 0|16@1+ (1,0) [0|65535] "Watts"  MCU
 SG_ Discharge_Power_Limit : 16|16@1+ (1,-50000) [-50000|0] "Watts"  MCU
 SG_ Battery_Current : 32|16@1+ (0.01,-300) [-300|300] "Amp"  MCU

BO_ 1780 Vehicle_Range: 8 BMS
 SG_ Vehicle_Display_range : 48|16@1+ (1,0) [0|65535] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool
 SG_ Vehicle_Range_ECO : 0|16@1+ (1,0) [0|65535] "Km"  Cluster
 SG_ Vehicle_Range_Sport : 16|16@1+ (1,0) [0|65535] "Km"  Cluster
 SG_ Vehicle_Range_Normal : 32|16@1+ (1,0) [0|65535] "Km"  Cluster

BO_ 275 MCU_Data2: 8 MCU
 SG_ MCU_Power_Status : 0|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ Regen_Status : 2|3@1+ (1,0) [0|4] "" Vector__XXX
 SG_ Throttle_Value1 : 8|16@1+ (0.01,0) [0|20] "V" Vector__XXX
 SG_ Throttle_Value2 : 24|16@1+ (0.01,0) [0|20] "V" Vector__XXX
 SG_ Cruise_Control_Status : 40|1@1+ (1,0) [0|1] ""  Cluster,BMS
 SG_ Vehicle_Discharge_Mode : 41|4@1+ (1,0) [0|15] ""  Cluster,BMS
 SG_ Hill_Hold_Response : 45|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MCU_Voltage : 48|16@1+ (0.001,0) [0|65] "V" Vector__XXX

BO_ 547 MCU_Data3: 8 MCU
 SG_ Motor_Current : 0|16@1+ (1,-1000) [-1000|1000] "A"  BMS
 SG_ Motor_Torque : 16|16@1- (0.001,0) [0|65] "Nm"  BMS
 SG_ Distance_covered_for_current_trip : 32|8@1+ (1,0) [0|255] "Km"  BMS
 SG_ MCU_DC_Current : 40|16@1+ (0.01,-300) [-300|300] "Amp"  BMS

BO_ 803 MCU_Disconnect_ACK: 8 MCU
 SG_ Disconnect_ACK : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 819 MCU_Fault_Data: 8 MCU
 SG_ MCU_Config_Incorrect_Error : 57|1@1+ (1,0) [0|1] ""  BMS,BMS00,TCU
 SG_ BSW_Fault : 56|1@1+ (1,0) [0|1] ""  BMS00,TCU,BMS
 SG_ Id_Iq_Ref_Table_Error : 53|1@1+ (1,0) [0|1] ""  BMS
 SG_ Quadrature_Position_Counter_Erro : 52|1@1+ (1,0) [0|1] ""  BMS
 SG_ Quadrature_Phase_Error : 51|1@1+ (1,0) [0|1] ""  BMS
 SG_ Front_brake_sensor_fault : 54|1@1+ (1,0) [0|1] ""  BMS
 SG_ Rear_brake_sensor_fault : 55|1@1+ (1,0) [0|1] ""  BMS
 SG_ Software_overcurrent_protection_Grade_3 : 0|1@1+ (1,0) [0|1] ""  BMS
 SG_ Software_overvoltage_fault_Grade_3 : 1|1@1+ (1,0) [0|1] ""  BMS
 SG_ Drive_Protection : 2|1@1+ (1,0) [0|1] ""  BMS
 SG_ Failure_for_motor_parameter_tuning : 3|1@1+ (1,0) [0|1] ""  BMS
 SG_ Drive_Overload_Grade_3 : 4|1@1+ (1,0) [0|1] ""  BMS
 SG_ U_phase_hall_fault : 5|1@1+ (1,0) [0|1] ""  BMS
 SG_ Drive_overtemperature_Grade_3 : 6|1@1+ (1,0) [0|1] ""  BMS
 SG_ Motor_overtemperature_Grade_3 : 7|1@1+ (1,0) [0|1] ""  BMS
 SG_ Encoder_disconnection_Grade_3 : 8|1@1+ (1,0) [0|1] ""  BMS
 SG_ Overvoltagebaseline_of_hardware_is_wrong_Grade_3 : 9|1@1+ (1,0) [0|1] ""  BMS
 SG_ Stalling_Fault_Grade_3 : 10|1@1+ (1,0) [0|1] ""  BMS
 SG_ DC_Bus_undervoltage_fault_Grade_3 : 11|1@1+ (1,0) [0|1] ""  BMS
 SG_ CAN_communication_abnormal_failure : 12|1@1+ (1,0) [0|1] ""  BMS
 SG_ Motor_over_speed_fault_Grade_3 : 13|1@1+ (1,0) [0|1] ""  BMS
 SG_ Motor_temperature_sensor_disconnection_fault_Grade_3 : 14|1@1+ (1,0) [0|1] ""  BMS
 SG_ Hardware_overcurrent_fault : 15|1@1+ (1,0) [0|1] ""  BMS
 SG_ Hardware_overvoltage_fault : 16|1@1+ (1,0) [0|1] ""  BMS
 SG_ Drive_power_undervoltage_fault : 17|1@1+ (1,0) [0|1] ""  BMS
 SG_ The_resolver_connector_is_loose_and_abnormal : 18|1@1+ (1,0) [0|1] ""  BMS
 SG_ Controller_drive_abnormal_fault : 19|1@1+ (1,0) [0|1] ""  BMS
 SG_ Power_supplyof_drive_board_exceeds_the_upper_limit : 20|1@1+ (1,0) [0|1] ""  BMS
 SG_ Lowvoltage_power_supply_under_voltage_fault : 21|1@1+ (1,0) [0|1] ""  BMS
 SG_ U_phase_Hall_disconnection_fault : 22|1@1+ (1,0) [0|1] ""  BMS
 SG_ V_phase_Hall_disconnection_fault : 23|1@1+ (1,0) [0|1] ""  BMS
 SG_ W_phase_Hall_disconnection_fault : 24|1@1+ (1,0) [0|1] ""  BMS
 SG_ V_phase_Hall_abnormal_fault : 25|1@1+ (1,0) [0|1] ""  BMS
 SG_ W_phase_Hall_abnormal_fault : 26|1@1+ (1,0) [0|1] ""  BMS
 SG_ Throttle_Wiper1_Fail : 27|1@1+ (1,0) [0|1] ""  BMS
 SG_ Throttle_Wiper2_Fail : 28|1@1+ (1,0) [0|1] ""  BMS
 SG_ MCU_calibration_status : 29|3@1+ (1,0) [0|7] ""  BMS
 SG_ Successful_Calibration_Counter : 32|8@1+ (1,0) [0|255] ""  BMS
 SG_ Attempted_Calibration_Counter : 40|8@1+ (1,0) [0|255] ""  BMS
 SG_ MCU_temperature_derating_flag : 48|1@1+ (1,0) [0|1] ""  BMS
 SG_ Motor_temperature_derating_flag : 49|1@1+ (1,0) [0|1] ""  BMS
 SG_ Vehicle_Wrong_Direction_Flag : 50|1@1+ (1,0) [0|1] ""  BMS

BO_ 277 Regen_Set_Percentage: 8 MCU
 SG_ Forced_Regen_Percentage : 0|16@1+ (0.1,0) [0|2] "%"  BMS
 SG_ Coast_Regen_Percentage : 16|16@1+ (0.1,0) [0|2] "%"  BMS
 SG_ Brake_Regen_Percentage : 32|16@1+ (0.1,0) [0|2] "%"  BMS

BO_ 276 Throttle_Calibration_Message: 8 MCU
 SG_ IDLE_Throttle_Count : 0|16@1+ (1,0) [0|16384] "Count" Vector__XXX
 SG_ Drive_Start_Throttle_Count : 16|16@1+ (1,0) [0|16384] "Count" Vector__XXX

BO_ 550 MCU_Data_6: 8 MCU
 SG_ MCU_HH_Activation_Counter : 0|16@1+ (1,0) [0|65535] "Count" Vector__XXX

BO_ 1571 MCU_Version_Info: 8 MCU
 SG_ MCU_HW_Version : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ MCU_Firmware_Version : 32|24@1+ (1,0) [0|16777215] "" Vector__XXX
 SG_ Motor_Type : 56|4@1+ (1,0) [0|2] "" Vector__XXX

BO_ 1572 MCU_BL_Version_Info: 8 MCU
 SG_ MCU_Bootloader_Version : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ MCU_Bootloader_Version_Gtake : 32|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1973 S1x_switch_status: 8 MCU
 SG_ Start_Stop_switch_status : 0|1@1+ (1,0) [0|1] ""  BMS
 SG_ Side_stand_Switch_status : 1|1@1+ (1,0) [0|1] ""  Cluster,BMS
 SG_ Brake_switch_status : 2|1@1+ (1,0) [0|1] ""  BMS
 SG_ Reverse_switch_status : 3|1@1+ (1,0) [0|1] ""  BMS
 SG_ Cruise_Switch_status : 4|1@1+ (1,0) [0|1] ""  BMS
 SG_ Mode_switch1_status : 5|1@1+ (1,0) [0|1] ""  BMS
 SG_ Mode_switch2_status : 6|1@1+ (1,0) [0|1] ""  BMS

BO_ 1986 S1X_Reboot_Command: 8 MCU
 SG_ Hard_Reboot : 0|1@1+ (1,0) [0|1] ""  BMS

BO_ 1573 MCU_Data_7: 8 MCU
 SG_ Fast_Charger_Disconnection : 0|1@1+ (1,0) [0|1] ""  Cluster,BMS
 SG_ Hold_Brake_To_Drive : 1|1@1+ (1,0) [0|1] ""  BMS
 SG_ Close_The_Side_Stand : 2|1@1+ (1,0) [0|1] ""  BMS

BO_ 272 Battery_IDs: 8 BMS
 SG_ Multiplexer_Battery_Serial_No M : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Battery_Serial_No_1 m0 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ Battery_Serial_No_2 m1 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX

BO_ 284 BMS_Parameter_1: 8 BMS
 SG_ Ready_to_sleep : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Contactor_state : 0|4@1+ (1,0) [0|15] ""  Cluster,MCU
 SG_ Battery_voltage_OCV : 8|16@1+ (0.001,0) [0|65] "V"  Cluster,MCU
 SG_ Pack_SOC : 24|16@1+ (0.01,0) [0|100] "%"  Cluster,MCU
 SG_ BMS_Mode : 40|4@1+ (1,0) [0|15] ""  Cluster,MCU
 SG_ Battery_Precharge_failure_status_10ms : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Battery_charge_inhibit_10ms : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Battery_discharge_inhibit_10ms : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Battery_Derate_Drive_Current_Flag_10ms : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Battery_Derate_Charge_Current_Flag_10ms : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Battery_Inhibit_Regen_Fault_10ms : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Battery_Permanent_Fault_10ms : 54|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS00_Short_circuit_detection_error_10ms : 55|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 304 BMS_Parameter_Limit_1: 8 BMS
 SG_ Charge_current_limit : 0|16@1+ (0.01,0) [0|300] "Amp"  Tester_Tool
 SG_ Discharge_current_limit : 16|16@1+ (0.01,-400) [-400|0] "Amp"  Tester_Tool
 SG_ Charge_Voltage_limit : 32|16@1+ (0.001,0) [0|65] "V"  Tester_Tool
 SG_ Charging_Mode : 48|3@1+ (1,0) [0|7] ""  Tester_Tool

BO_ 336 BMS_Parameter_3: 8 BMS
 SG_ Range_Multiplier : 48|8@1+ (0.01,0) [0|2.55] ""  Cluster,EOL,Fast_Charger,HU,MCU,Slow_Charger,TCU,Tester_Tool,VCU
 SG_ Bus_Voltage : 0|16@1+ (0.001,0) [0|65] "V"  Tester_Tool
 SG_ Delta_Voltage : 16|16@1+ (0.001,0) [0|65] "V"  Tester_Tool
 SG_ Available_Capacity : 32|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ Pack_SoH : 40|8@1+ (1,0) [0|100] "%" Vector__XXX

BO_ 561 HU_Display_Message: 8 TCU
 SG_ FC_Amount_Trigger : 16|1@1+ (1,0) [0|1] ""  BMS,Cluster
 SG_ FC_Charging_Payment_Amount : 0|16@1+ (1,0) [0|65535] ""  BMS,Cluster

BO_ 290 Overall_Battery_Fault: 8 BMS
 SG_ Battery_Precharge_failure_status : 0|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_charge_inhibit : 1|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_discharge_inhibit : 2|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_Derate_Drive_Current_Flag : 3|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_Derate_Charge_Current_Flag : 4|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_Inhibit_Regen_Fault : 5|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_Permanent_Fault : 6|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Battery_Voltage_Deviation_Error : 7|1@1+ (1,0) [0|1] ""  Cluster,MCU

BO_ 352 BMS_Parameter_4: 8 BMS
 SG_ Balancing_Status : 0|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Effective_Battery_Temperature : 8|16@1+ (0.1,-50) [-50|78] "degC"  Cluster,MCU
 SG_ Battery_Temperature_Min : 24|16@1+ (0.1,-50) [-50|78] "degC"  Cluster,MCU
 SG_ Battery_Temperature_Max : 40|16@1+ (0.1,-50) [-50|78] "degC"  Cluster,MCU
 SG_ Reset_SOC_from_OCV : 56|1@1+ (1,0) [0|1] ""  Cluster,MCU

BO_ 384 BMS_Parameter_5: 8 BMS
 SG_ ECOS_Charging_Flag : 40|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Charging_time_Max : 0|16@1+ (1,0) [0|65535] "Min" Vector__XXX
 SG_ DC_output_voltage_limit_parameter : 16|16@1+ (0.1,0) [0|120] "V" Vector__XXX
 SG_ Charging_Rate : 32|8@1+ (1,0) [0|100] "%" Vector__XXX

BO_ 401 BMS_Parameter_8: 8 BMS
 SG_ Calculated_current_based_on_coulomb : 0|32@1- (1E-006,0) [-2147.483648|2147.483647] "Amp" Vector__XXX
 SG_ Aggregated_current_Ah : 32|32@1- (1E-006,0) [-2147.483648|2147.483647] "Ah" Vector__XXX

BO_ 1795 EOL_Timestamp: 8 BMS
 SG_ EOL_BMS_TimeStamp : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1040 BMS_Fault_1: 8 BMS
 SG_ BMS_Voltage_sensor_failureCell1 : 0|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell2 : 1|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell3 : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell4 : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell5 : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell6 : 5|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell7 : 6|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell8 : 7|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell9 : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell10 : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell11 : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell12 : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell13 : 12|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell1 : 13|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell2 : 14|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell3 : 15|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell4 : 16|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell5 : 17|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell6 : 18|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell7 : 19|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell8 : 20|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell9 : 21|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell10 : 22|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell11 : 23|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell12 : 24|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell13 : 25|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature1_sensor_failure : 26|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature2_sensor_failure : 27|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature3_sensor_failure : 28|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature4_sensor_failure : 29|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature1_out_of_range : 30|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature2_out_of_range : 31|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature3_out_of_range : 32|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Battery_pack_temperature4_out_of_range : 33|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Current_sensor_failure_Open_circuit : 34|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Current_sensor_failure_Short_circuit : 35|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_current_charge_Warning : 36|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_VOOR_CutOff_Error : 37|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Precharge_too_fast_Info : 38|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Precharge_too_slow_Info : 39|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Precharge_failure : 40|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_MOSFETs_connection_failed : 41|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_MOSFETs_disconnection_failed : 42|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_PDU_temperature_warning_info : 43|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_PDU_temperature_error : 44|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_voltage_charge_warning_info : 45|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_voltage_charge_Error : 46|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_voltage_charge_Permanent_Fault : 47|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_voltage_regen_warning_info : 48|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_voltage_regen_Error : 49|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Under_voltage_warning_info : 50|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Under_voltage_Error : 51|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Under_voltage_Permanent_Fault : 52|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_temperature_charge_warning_info : 53|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_temperature_charge_Error : 54|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_temperature_drive_warning_info : 55|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_temperature_drive_Error : 56|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_temperature_due_to_Cell_vent_error : 57|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_temperature_due_to_Cell_vent_Failure : 58|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Short_circuit_detection_error_info : 59|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Short_circuit_detection_permanent_fault : 60|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Cell_failure_permanent_fault : 61|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Over_current_charge_Error : 62|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Low_temperature_during_charging_warning_info : 63|1@1+ (1,0) [0|1] ""  Tester_Tool

BO_ 1041 BMS_Fault_2: 8 BMS
 SG_ VIN_Variant_Mismatch_Fault : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Cell_Variant_mismatch_fault : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Low_temperature_during_charging_Error : 0|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Low_temperature_during_driving_Warning : 1|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Low_temperature_during_driving_Error : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Over_time_to_fast_charge_Error : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_sensor_failureCell14 : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Voltage_out_of_range_Cell14 : 5|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_pack_temperature5_sensor_failure : 6|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_pack_temperature6_sensor_failure : 7|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_pack_temperature5_out_of_range : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_pack_temperature6_out_of_range : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Rat_Bite_Fault : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ CellDip_Error : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ CellDip_Fault : 12|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ CompSpikeDtctn_Fault : 13|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ CompSpike_Error : 14|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ TempRegen_Warning : 15|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ SOCRegenWarn : 16|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ TempRiseDriveDtctn_60 : 17|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ TempRiseChrgDtctn_60 : 18|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ PDUerrorCutOff_60 : 19|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ SBC_FAULT_ACTIVE : 20|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ SUPPLY_12V_NOT_GOOD : 21|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Supply_9V_Not_Good : 22|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ AFE_COM_LOST : 23|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ External_Flash_COM_Lost : 24|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_Temp_Fault_during_Slp : 25|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_Temp_Increase_flt_sleep : 26|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_3_3V_Fault_Not_Good : 27|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ PDU_Temperature_Rise_Detection_Sleep : 28|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ PDU_Temperature_Rise_Detection : 29|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ PDU_Temperature_Error_during_Sleep : 30|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_Pack_Temperature_Rise_Sleep : 31|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_Pack_Temperature_Error_Sleep : 32|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Battery_Temperature_Blanket_Cut_off_Grade4 : 33|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Overcharge_Protection : 34|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Charger_ReAuth_Warning : 35|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Charger_ReAuth_Error : 36|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Cell_Voltage_Rise_Deration_in_HC : 37|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Thermal_runaway_fault : 38|1@1+ (1,0) [0|1] ""  Tester_Tool,Cluster
 SG_ Thermal_runaway_shadow_fault : 38|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Cell_Voltage_Rise_Deration_in_HC_1P5C : 40|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Freq_Fault_Trigger : 41|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ PDU_TRNED_DRIVE : 42|8@1+ (1,0) [0|1] ""  Tester_Tool

BO_ 368 BMS_Cell_Status: 8 BMS
 SG_ BMS_Balancing_Status_Battery_Cell_1 : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_2 : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_3 : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_4 : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_5 : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_6 : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_7 : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_8 : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_9 : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_10 : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_11 : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_12 : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_13 : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Balancing_Status_Battery_Cell_14 : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Cell_Min_Voltage : 16|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ BMS_Cell_Max_Voltage : 32|16@1+ (0.001,0) [0|65] "V" Vector__XXX

BO_ 1746 Available_Drive_mode: 8 BMS
 SG_ Cruise_Availability : 8|1@1+ (1,0) [0|1] ""  Cluster,MCU
 SG_ Available_Drive_modes : 0|8@1+ (1,0) [0|10] ""  Cluster,MCU

BO_ 1617 BMS_Configuration_Info: 8 BMS
 SG_ Battery_Cell_Type : 0|4@1+ (1,0) [0|15] ""  Cluster
 SG_ Battery_cell_in_Series : 8|8@1+ (1,0) [0|255] ""  Cluster
 SG_ Battery_cell_in_Parallel : 16|8@1+ (1,0) [0|255] ""  Cluster
 SG_ CAN_Database : 24|32@1+ (1,0) [0|4294967295] ""  Cluster
 SG_ BMS_Hardware_Variant : 56|2@1+ (1,0) [0|1] ""  Cluster
 SG_ BMS_DipSwitch : 58|4@1+ (1,0) [0|3] ""  Cluster

BO_ 820 MCU_Fault_Data2: 8 MCU
 SG_ Front_brake_offset_error : 28|1@1+ (1,0) [0|1] ""  BMS
 SG_ Rear_brake_offset_error : 27|1@1+ (1,0) [0|1] ""  BMS
 SG_ Unintended_Accel_Fault : 26|1@1+ (1,0) [0|1] ""  BMS
 SG_ Vcc_STG_Fault : 25|1@1+ (1,0) [0|1] ""  BMS
 SG_ Vcc_STB_Fault : 24|1@1+ (1,0) [0|1] ""  BMS
 SG_ PWMMissingFault : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZIPulse_Missing_Fault : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Pulse_Missing_Num : 11|12@1+ (1,0) [0|2048] "" Vector__XXX
 SG_ AB_Pulse_Mism_ZIPulse_Fault : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Pos_Cnt_Mism_PWM_Val : 1|9@1+ (1,0) [0|360] "" Vector__XXX
 SG_ Pos_Cnt_Mism_PWM_Fault : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 420 BMS_Pack_Temp: 8 BMS
 SG_ BMS_Pack_Temperature_01 : 0|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,Cluster
 SG_ BMS_Pack_Temperature_02 : 16|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,Cluster
 SG_ BMS_Pack_Temperature_03 : 32|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,Cluster
 SG_ BMS_Pack_Temperature_04 : 48|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,Cluster

BO_ 424 BMS_PDU_Temp: 8 BMS
 SG_ BMS_PDU_Temperature_01 : 0|11@1+ (0.1,-40) [-40|120] "DegC"  Tester_Tool,MCU
 SG_ BMS_PDU_Temperature_02 : 16|11@1+ (0.1,-40) [-40|120] "DegC"  Tester_Tool,MCU
 SG_ BMS_Battery_Pack_Voltage_Measured : 32|16@1+ (0.1,0) [0|65] "V"  Tester_Tool,MCU
 SG_ BMS_Cell_Balancing_Temperature : 48|16@1+ (0.01,-40) [-40|615] "DegC"  Tester_Tool,MCU

BO_ 423 BMS_Pack_Temp_2: 8 BMS
 SG_ BMS_Pack_Temperature_05 : 0|16@1+ (0.1,-50) [-50|120] "DegC"  MCU
 SG_ BMS_Pack_Temperature_06 : 16|16@1+ (0.1,-50) [-50|120] "DegC"  MCU
 SG_ BMS_PDU_Delta_Temperature : 32|16@1+ (0.5,-50) [-50|32718] "DegC"  MCU
 SG_ BMS_Battery_Temperature_Display : 48|8@1+ (1,-50) [-50|2] "DegC"  MCU

BO_ 672 EPOCH_Time: 8 BMS
 SG_ BMS_EPOCH_Time : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 421 BMS_Cell_Voltage_1: 8 BMS
 SG_ Cell_Voltage_Cell_1 : 0|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_2 : 16|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_3 : 32|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_4 : 48|16@1+ (0.001,0) [0|65] "V" Vector__XXX

BO_ 422 BMS_Cell_Voltage_2: 8 BMS
 SG_ Cell_Voltage_Cell_5 : 0|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_6 : 16|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_7 : 32|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_8 : 48|16@1+ (0.001,0) [0|65] "V" Vector__XXX

BO_ 425 BMS_Cell_Voltage_3: 8 BMS
 SG_ Cell_Voltage_Cell_9 : 0|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_10 : 16|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_11 : 32|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_12 : 48|16@1+ (0.001,0) [0|65] "V" Vector__XXX

BO_ 426 BMS_Cell_Voltage_4: 8 BMS
 SG_ Cell_Voltage_Cell_13 : 0|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Cell_14 : 16|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Cell_Voltage_Avg_1_14 : 32|16@1+ (0.001,0) [0|65] "V" Vector__XXX

BO_ 1618 BMS_Configuration_Info2: 8 BMS
 SG_ SAF_MV_CHG_OVERVG_ERROR : 0|13@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_CHG_OVERVG_HEAL : 13|13@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_UNDERVG_ERROR : 26|12@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_UNDERVG_RESM : 38|12@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_UNDERVG_WARNG : 50|12@1+ (1,0) [0|4500] "" Vector__XXX

BO_ 1619 BMS_Configuration_Info3: 8 BMS
 SG_ SLC_MV_THDVTG_CCEND_CELL : 0|13@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SLC_MV_THDVTG_CHRGEND_CELL : 13|13@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SOC_AH_USECAP_CELL : 26|12@1+ (0.01,0) [0|40.95] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISEPDU : 40|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_CutOff : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_CNT_ERRORCNTR_COMPSPIKE : 56|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 537 BMS_Configuration_Info4: 8 BMS
 SG_ SAF_DEGC_OVERTEMP_DRVWARNG : 0|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISECHRG : 7|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_DRVERROR : 11|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_CHGWARNG : 18|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISEDRV : 25|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_CHGERROR : 29|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ BCD_DEGC_THSDTEMP_PDUWARNG : 36|8@1+ (1,0) [0|150] "" Vector__XXX
 SG_ BCD_DEGC_DIFFTEMP_PDUWARNG : 44|8@1+ (1,0) [0|150] "" Vector__XXX
 SG_ BCD_DEGC_THSDTEMP_PDUERROR : 52|8@1+ (1,0) [0|150] "" Vector__XXX

BO_ 544 BMS_Configuration_Info5: 8 BMS
 SG_ BCD_DEGC_THSDTEMP_PDU_CUTOFF : 0|8@1+ (1,0) [0|150] "" Vector__XXX
 SG_ SLC_MIN_CVEND_TIMER : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_SLEEPWARNG : 16|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISESLEEP : 23|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SAF_MV_CELLDIPTHDDELTA : 27|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ SAF_MV_COMPSPIKETHD : 37|11@1+ (1,0) [0|2000] "" Vector__XXX
 SG_ ACC_i_MAXDSCHCURRLIM_16P : 48|9@1+ (1,0) [0|300] "" Vector__XXX

BO_ 1620 BMS_Configuration_Info6: 8 BMS
 SG_ SAF_CNT_ERRORCNTR_CELLDIP : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_CNT_OVERVLTGCHRGPERM : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_CNT_ERRORCNTR_SHRTCRCT : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SLC_A_OVERCURR_TOLCHG : 24|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ SAF_DEGC_REGENALLOWED : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_PCT_SOCREGENALLOWED : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ACC_i_MAXDSCHCURRLIM_12P : 48|9@1+ (1,0) [0|300] "" Vector__XXX

BO_ 2018 BMS_AFE_Temperature_Debug: 8 BMS
 SG_ AFE_Min_Temp : 0|16@1+ (0.1,-50) [-50|6503.5] ""  Tester_Tool
 SG_ AFE_Max_Temp : 16|16@1+ (0.1,-50) [-50|6503.5] ""  Tester_Tool
 SG_ AFE_fault_temperature_sensor : 32|4@1+ (1,0) [0|15] ""  Tester_Tool

BO_ 2019 BMS_AFE_Fault_Debug: 8 BMS
 SG_ AFE_Fault_Status1 : 0|16@1+ (1,0) [0|65535] ""  Tester_Tool
 SG_ AFE_Fault_Status2 : 16|16@1+ (1,0) [0|65535] ""  Tester_Tool
 SG_ AFE_Fault_Status3 : 32|16@1+ (1,0) [0|65535] ""  Tester_Tool

BO_ 2020 BMS_Address_Register_Value3: 8 BMS
 SG_ Register_R12 : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_LR : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2021 BMS_Address_Register_Value4: 8 BMS
 SG_ Register_PC : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_PSR : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2022 BMS_Reset1: 8 BMS
 SG_ BMS_Low_Voltage_Detect_Reset : 1|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Loss_of_Clock_Reset : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Loss_of_Lock_Reset : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_CMU_Lossof_Clock_Reset : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Watchdog : 5|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_External_Reset_Pin : 6|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Power_On_Reset : 7|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_JTAG_generated_reset : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Core_Lockup : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Software : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_MDM_AP_System_Reset_Request : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Stop_Acknowledge_Error : 13|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Memory_Allocation_Failed : 16|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ BMS_System_Reset_Counter : 24|32@1+ (1,0) [-2147483648|2147483647] ""  Tester_Tool
 SG_ BMS_Memory_Leakage : 56|8@1+ (1,0) [0|255] "kb"  Tester_Tool

BO_ 2023 BMS_Reset2: 8 BMS
 SG_ Stack_Overflow : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1568 BMS_FW_Version_Info: 8 BMS
 SG_ MBMS_Firmware_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1554 BMS_BL_Version_Info: 8 BMS
 SG_ BMS_Bootloader_version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1590 BMS_BL_Info: 8 BMS
 SG_ BMS_Bootloader_Info : 0|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 1616 BMS_ASW_Version_Info: 8 BMS
 SG_ BMS_ASW_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1934 BMS0_Diagnostic_Request: 8 BMS
 SG_ BMS0_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1969 BMS0_Diagnostic_Response: 8 BMS
 SG_ BMS0_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1958 MCU_Diagnostic_request: 8 MCU
 SG_ MCU_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1972 MCU_Diagnostic_response: 8 MCU
 SG_ MCU_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1982 SCDiagnosticRequest: 8 Slow_Charger
 SG_ SC_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1975 SCDiagnosticResponse: 8 Slow_Charger
 SG_ SC_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1328 EV_Keep_Alive_Message: 8 BMS
 SG_ Vehicle_Alive : 0|1@1+ (1,0) [0|1] "-"  Tester_Tool,Fast_Charger

BO_ 1329 Fast_Charger_Keep_Alive_Message: 8 Fast_Charger
 SG_ Fast_Charger_Alive : 0|1@1+ (1,0) [0|1] "-"  BMS

BO_ 1330 EV_Unlatch_Request: 8 BMS
 SG_ Request_to_Unlatch : 0|1@1+ (1,0) [0|1] "-"  Tester_Tool,Fast_Charger

BO_ 1332 Fast_Charger_Authentication_Request: 8 BMS
 SG_ Random_Number_Over_ISOTP_2 : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Tester_Tool,Fast_Charger

BO_ 1347 Fast_Charger_Authentication_Response: 8 Fast_Charger
 SG_ Message_Authentication_Code_MAC_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Tester_Tool,BMS

BO_ 1333 BMS_Authentication_Request: 8 Fast_Charger
 SG_ Random_Number_Over_ISOTP_1 : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Tester_Tool,BMS

BO_ 1363 BMS_Authentication_Response: 8 BMS
 SG_ Message_Authentication_Code_MAC_Over_ISOTP_1 : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Tester_Tool,Fast_Charger

BO_ 1334 EV_Signature_Request: 8 Fast_Charger
 SG_ Random_Number_Over_ISOTP : 0|64@1+ (1,0) [0|1.84467440737096E+019] "-"  Cluster

BO_ 1379 EV_Signature_Response: 8 Cluster
 SG_ Digital_Signature_Length_Over_ISOTP : 0|32@1+ (1,0) [0|4294967295] "-"  Fast_Charger
 SG_ Digital_Signature_of_EV_Over_ISOTP : 32|32@1+ (1,0) [0|4294967295] "-"  Fast_Charger

BO_ 1282 EV_Voltage_Control: 8 BMS
 SG_ EV_Voltage_Control : 0|1@1+ (1,0) [0|1] ""  Fast_Charger

BO_ 1408 EV_Identification_Prm_1: 8 BMS
 SG_ EV_identification_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1409 EV_Identification_Prm_2: 8 BMS
 SG_ EV_identification_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1410 EV_Protocol_1: 8 BMS
 SG_ Protocol_identifier_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1411 EV_Protocol_2: 8 BMS
 SG_ Protocol_identifier_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Fast_Charger

BO_ 1296 FC_Voltage_Control: 8 Fast_Charger
 SG_ Voltage_Control_Option : 0|1@1+ (1,0) [0|1] ""  Tester_Tool,BMS

BO_ 1412 EV_Identification_1: 8 Fast_Charger
 SG_ EVSE_identification_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool,BMS

BO_ 1413 EV_Identification_2: 8 Fast_Charger
 SG_ EVSE_identification_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool,BMS

BO_ 1414 Protocol_1: 8 Fast_Charger
 SG_ Protocol_identifier_low_byte_1 : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool,BMS

BO_ 1415 Protocol_2: 8 Fast_Charger
 SG_ Protocol_identifier_high_byte_1 : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool,BMS

BO_ 1288 Fast_Charger_Parameter_1: 8 Fast_Charger
 SG_ Charging_system_error : 0|1@1+ (1,0) [0|1] ""  BMS
 SG_ EV_supply_equipment_malfunction : 1|1@1+ (1,0) [0|1] ""  BMS
 SG_ EV_incompatibility : 2|1@1+ (1,0) [0|1] ""  BMS
 SG_ Reserved_6 : 3|5@1+ (1,0) [0|31] ""  BMS
 SG_ EV_supply_equipment_stop_control : 8|1@1+ (1,0) [0|1] ""  BMS
 SG_ EV_supply_equipment_status : 9|1@1+ (1,0) [0|1] ""  BMS
 SG_ Vehicle_connector_latched : 10|1@1+ (1,0) [0|1] ""  BMS
 SG_ EV_supply_equipment_ready : 11|1@1+ (1,0) [0|1] ""  BMS
 SG_ Waiting_state_before_charging_start : 12|1@1+ (1,0) [0|1] ""  BMS
 SG_ EVSE_Emergency_Stop : 13|2@1+ (1,0) [0|1] ""  BMS
 SG_ rated_DC_output_voltage : 16|16@1+ (0.1,0) [0|120] "V"  BMS
 SG_ Available_DC_output_current : 32|16@1+ (0.1,0) [0|200] "A"  BMS
 SG_ Confirmed_DC_output_voltage_limit : 48|16@1+ (0.1,0) [0|120] "V"  BMS

BO_ 1289 Fast_Charger_Parameter_2: 8 Fast_Charger
 SG_ Control_protocol_number : 0|8@1+ (1,0) [0|254] ""  BMS
 SG_ Available_DC_output_power : 8|8@1+ (50,0) [0|12750] "W"  BMS
 SG_ Output_voltage : 16|16@1+ (0.1,0) [0|250] "V"  BMS
 SG_ DC_output_current : 32|16@1+ (0.1,0) [0|2] "A"  BMS
 SG_ Remaining_charging_time : 48|16@1+ (1,0) [0|65534] "min"  BMS

BO_ 128 BMS_to_Slow_Charger_Authentication_Request: 8 BMS
 SG_ BMS_To_SC_Seed : 0|32@1+ (1,0) [0|4294967295] ""  Slow_Charger

BO_ 131 Slow_Charger_to_BMS_Authentication_Response: 8 Slow_Charger
 SG_ SC_Key : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool,BMS

BO_ 129 BMS_to_Slow_Charger_Authentication_Status: 8 BMS
 SG_ BMS_Auth_Response_to_SC_ : 0|1@1+ (1,0) [0|1] ""  Tester_Tool,Slow_Charger

BO_ 11 Slow_Charger_to_battery_wakeup: 8 Slow_Charger
 SG_ Battery_Wakeup_Request : 0|2@1+ (1,0) [0|3] ""  BMS
 SG_ Serial_Number : 8|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ Firmware_version : 40|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Hardware_version : 44|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Manufacturer_Code : 48|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Bootloader_version : 52|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 531 Charger_Status: 8 Slow_Charger
 SG_ Charger_Status_Signal : 0|1@1+ (1,0) [0|1] ""  BMS
 SG_ Charger_Mode : 1|3@1+ (1,0) [0|8] ""  BMS
 SG_ Charging_Voltage_Available_range : 4|16@1+ (0.1,0) [0|120] "V"  BMS
 SG_ Charging_Current_Available_range : 20|16@1+ (0.1,0) [0|500] "Amp"  BMS
 SG_ Slow_Charger_Fault_Information : 36|2@1+ (1,0) [0|3] ""  BMS
 SG_ Charger_output_Short_circuit_error : 40|1@1+ (1,0) [0|1] ""  BMS
 SG_ Charger_under_temperature_error : 41|1@1+ (1,0) [0|1] ""  BMS
 SG_ Charger_over_temperature_error : 42|1@1+ (1,0) [0|1] ""  BMS
 SG_ Low_AC_voltage_error : 43|1@1+ (1,0) [0|1] ""  BMS
 SG_ Rectifier_hardware_error : 44|1@1+ (1,0) [0|1] ""  BMS
 SG_ Authentication_Error : 45|1@1+ (1,0) [0|1] ""  BMS
 SG_ Battery_Parameter_Time_out_error : 46|1@1+ (1,0) [0|1] ""  BMS
 SG_ Data_Corruption_error : 47|1@1+ (1,0) [0|1] ""  BMS
 SG_ Charge_control_message_timeout_error : 48|1@1+ (1,0) [0|1] ""  BMS
 SG_ BoostCharger_billing_status : 50|1@1+ (1,0) [0|1] ""  BMS00

BO_ 1621 BMS_Configuration_Info7: 8 BMS
 SG_ Force_charge_mode : 56|1@1+ (1,0) [0|1] ""  Cluster,Fast_Charger,MCU,Slow_Charger,TCU,Tester_Tool,VCU
 SG_ SOC_value_from_minimum_OCV : 0|16@1+ (0.01,0) [0|655.36] "%" Vector__XXX
 SG_ SOC_debug_1 : 16|16@1+ (0.01,0) [0|655.36] "%" Vector__XXX
 SG_ SLC_CNT_CHRG_RESTART : 32|8@1+ (1,0) [0|10] "Count" Vector__XXX
 SG_ SLC_MV_MINVG_STRTCELLBAL : 40|16@1+ (1,0) [0|5000] "V" Vector__XXX

BO_ 1651 BMS_Debug_Msg: 8 BMS
 SG_ BMS_Debug1 : 0|16@1+ (0.01,-300) [-300|300] "Amp" Vector__XXX
 SG_ BMS_Debug2 : 16|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ BMS_Bool_Debug3 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Bool_Debug4 : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Debug5 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BMS_Debug6 : 48|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1607 AmpHr_Data_00: 8 BMS
 SG_ Charge_AmpHr_00 : 0|16@1+ (0.01,0) [0|655] "Amphr" Vector__XXX
 SG_ Discharge_AmpHr_00 : 16|16@1+ (0.1,-6553) [-6553|0] "Amphr" Vector__XXX

BO_ 300 kalman_filter_debug_signal: 8 BMS
 SG_ smd_pct_ECM_SOC : 0|16@1+ (0.01,0) [0|655.35] "" Vector__XXX
 SG_ smd_pct_Estimated_SoC : 16|16@1+ (0.01,0) [0|655.35] "" Vector__XXX
 SG_ V1 : 32|16@1+ (0.01,0) [0|655.35] "" Vector__XXX
 SG_ V2 : 48|16@1+ (0.01,0) [0|655.35] "" Vector__XXX

BO_ 2024 Kalman_filter_debug_1: 8 BMS
 SG_ KK_1 : 0|16@1- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ KK_2 : 16|16@1- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ KK_3 : 32|16@1- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ Innov_matrix_1 : 48|16@1- (0.01,0) [-327.68|327.67] "" Vector__XXX

BO_ 2025 Kalman_filter_Debug_2: 8 BMS
 SG_ Vt_b : 0|16@1- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ Estimated_error : 16|16@1- (0.01,0) [-327.68|327.67] "" Vector__XXX

BO_ 1685 TTC_Array: 8 BMS
 SG_ Estimated_TTC_Array_1 : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Estimated_TTC_Array_2 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Estimated_TTC_Array_3 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Estimated_TTC_Array_4 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Estimated_TTC_Array_5 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Estimated_TTC_Array_6 : 40|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1625 SOH_Signals: 8 BMS
 SG_ Cycle_SOH : 0|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ SOH_Array1 : 8|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ SOH_Array2 : 16|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ SOH_Array3 : 24|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ SOH_Array4 : 32|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ SOH_Array5 : 40|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ SOH_update_index : 48|4@1+ (1,0) [0|10] "" Vector__XXX

BO_ 1600 BMS00_Parameter_7: 8 BMS
 SG_ BMS00_Short_circuit_detection_error : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS00_Precharge_too_fast_Info_10ms : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS00_Precharge_too_slow_Info_10ms : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS00_Precharge_failure_10ms : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS00_MOSFETs_connection_failed_10ms : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS00_MOSFETs_disconnection_failed_10ms : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved1 : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Fault_status_1_00 : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Fault_status_2_00 : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Fault_status_3_00 : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AFE_FAULT_STATUS_00 : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EEPROM_Status_00 : 12|4@1+ (1,0) [0|3] "" Vector__XXX
 SG_ Precharge_Switch_demand_00 : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Charge_Switch_demand_00 : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Discharge_Switch_demand_00 : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SBC_LIMP_Status_00 : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Bus_Voltage_00_10ms : 24|16@1+ (0.001,0) [0|65] "V" Vector__XXX
 SG_ Short_Circuit_Permanent_Precharge_Fault : 40|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 427 DCIR_Cell_1: 8 BMS
 SG_ Cell_DCIR_1 : 0|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_2 : 16|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_3 : 32|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_4 : 48|16@1+ (1,0) [0|65535] "uohm" Vector__XXX

BO_ 428 DCIR_Cell_2: 8 BMS
 SG_ Cell_DCIR_5 : 0|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_6 : 16|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_7 : 32|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_8 : 48|16@1+ (1,0) [0|65535] "uohm" Vector__XXX

BO_ 429 DCIR_Cell_3: 8 BMS
 SG_ Cell_DCIR_9 : 0|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_10 : 16|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_11 : 32|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_12 : 48|16@1+ (1,0) [0|65535] "uohm" Vector__XXX

BO_ 430 DCIR_Cell_4: 8 BMS
 SG_ Cell_DCIR_13 : 0|16@1+ (1,0) [0|65535] "uohm" Vector__XXX
 SG_ Cell_DCIR_14 : 16|16@1+ (1,0) [0|65535] "uohm" Vector__XXX

BO_ 1765 BMS00_Energy_data: 8 BMS
 SG_ PDU_TRNED_VAL : 32|16@1+ (0.01,0) [0|655.35] "" Vector__XXX
 SG_ Total_Available_Energy : 0|16@1+ (1,0) [0|5000] "Whr" Vector__XXX
 SG_ Instantaneous_Available_Energy : 16|16@1+ (0.001,-5) [-5|5] "Whr" Vector__XXX

BO_TX_BU_ 1783 : BMS,TCU;
BO_TX_BU_ 1632 : TCU,BMS;
BO_TX_BU_ 1674 : HU,TCU;


CM_ BO_ ********** "This is a message for not used signals, created by Vector CANdb++ DBC OLE DB Provider.";
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 100000;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","OnChangeAndIfActive","OnChangeAndIfActiveWithRepetition","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 100000000000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 50000;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 1000;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 999999;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","Cyclic and OnChange","Cyclic and OnWrite","Cyclic and IfActive","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed","NotUsed";
BA_DEF_ BO_  "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_  "NmMessage" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 50000;
BA_DEF_ BU_  "NmNode" ENUM  "No","Yes";
BA_DEF_ BU_  "NmStationAddress" INT 0 63;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_  "DBName" STRING ;
BA_DEF_  "NWMWakeupAllowed" ENUM  "No","Yes";
BA_DEF_  "BusType" STRING ;
BA_DEF_ SG_  "SystemSignalLongSymbol" STRING ;
BA_DEF_ BO_  "SystemMessageLongSymbol" STRING ;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "NmMessage" "No";
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "NmNode" "No";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "NWMWakeupAllowed" "Yes";
BA_DEF_DEF_  "BusType" "CAN";
BA_DEF_DEF_  "SystemSignalLongSymbol" "";
BA_DEF_DEF_  "SystemMessageLongSymbol" "";
BA_ "DBName" "ESW_VEH_CM_CAN_V01_S1X";
BA_ "GenMsgCycleTime" BO_ 824 100;
BA_ "GenMsgCycleTime" BO_ 1056 5000;
BA_ "GenMsgCycleTime" BO_ 1049 100;
BA_ "GenMsgCycleTime" BO_ 785 5000;
BA_ "GenMsgCycleTime" BO_ 823 5000;
BA_ "GenMsgCycleTime" BO_ 1587 5000;
BA_ "GenMsgCycleTime" BO_ 1047 5000;
BA_ "GenMsgCycleTime" BO_ 821 100;
BA_ "GenMsgCycleTime" BO_ 801 100;
BA_ "GenMsgCycleTime" BO_ 822 100;
BA_ "GenMsgCycleTime" BO_ 1636 500;
BA_ "GenMsgCycleTime" BO_ 793 5000;
BA_ "GenMsgCycleTime" BO_ 1783 100;
BA_ "GenMsgCycleTime" BO_ 1046 100;
BA_ "GenMsgCycleTime" BO_ 1632 500;
BA_ "GenMsgCycleTime" BO_ 1750 100;
BA_ "GenMsgCycleTime" BO_ 1622 5000;
BA_ "GenMsgCycleTime" BO_ 563 1000;
BA_ "GenMsgCycleTime" BO_ 1081 100;
BA_ "GenMsgSendType" BO_ 1081 8;
BA_ "GenMsgCycleTime" BO_ 1044 100;
BA_ "GenMsgCycleTime" BO_ 1043 1000;
BA_ "GenMsgCycleTime" BO_ 1042 1000;
BA_ "GenMsgSendType" BO_ 1562 8;
BA_ "GenMsgSendType" BO_ 1697 8;
BA_ "GenMsgCycleTime" BO_ 1674 100;
BA_ "GenMsgCycleTime" BO_ 786 10;
BA_ "GenMsgCycleTime" BO_ 791 10;
BA_ "GenMsgCycleTime" BO_ 790 10;
BA_ "GenMsgCycleTime" BO_ 788 10;
BA_ "GenMsgCycleTime" BO_ 769 10;
BA_ "GenMsgCycleTime" BO_ 802 100;
BA_ "GenMsgCycleTime" BO_ 800 100;
BA_ "GenMsgCycleTime" BO_ 775 10;
BA_ "GenMsgCycleTime" BO_ 772 10;
BA_ "GenMsgCycleTime" BO_ 787 100;
BA_ "GenMsgCycleTime" BO_ 1758 5000;
BA_ "GenMsgCycleTime" BO_ 1742 5000;
BA_ "GenMsgCycleTime" BO_ 851 100;
BA_ "GenMsgCycleTime" BO_ 1657 1000;
BA_ "GenMsgCycleTime" BO_ 562 1000;
BA_ "GenMsgCycleTime" BO_ 792 10;
BA_ "GenMsgCycleTime" BO_ 1782 10;
BA_ "GenMsgCycleTime" BO_ 1777 100;
BA_ "GenMsgCycleTime" BO_ 554 100;
BA_ "GenMsgCycleTime" BO_ 552 100;
BA_ "GenMsgCycleTime" BO_ 1281 100;
BA_ "GenMsgCycleTime" BO_ 1280 100;
BA_ "GenMsgCycleTime" BO_ 548 100;
BA_ "GenMsgCycleTime" BO_ 1754 10;
BA_ "GenMsgCycleTime" BO_ 1779 100;
BA_ "GenMsgCycleTime" BO_ 1781 100;
BA_ "GenMsgCycleTime" BO_ 2000 100;
BA_ "GenMsgCycleTime" BO_ 1778 100;
BA_ "GenMsgCycleTime" BO_ 31 100;
BA_ "GenMsgCycleTime" BO_ 3 100;
BA_ "GenMsgCycleTime" BO_ 63 0;
BA_ "GenMsgCycleTime" BO_ 61 0;
BA_ "GenMsgCycleTime" BO_ 79 0;
BA_ "GenMsgCycleTime" BO_ 64 100;
BA_ "GenMsgCycleTime" BO_ 151 100;
BA_ "GenMsgCycleTime" BO_ 155 0;
BA_ "GenMsgCycleTime" BO_ 325 100;
BA_ "GenMsgCycleTime" BO_ 850 100;
BA_ "GenMsgCycleTime" BO_ 400 100;
BA_ "GenMsgCycleTime" BO_ 1744 0;
BA_ "GenMsgCycleTime" BO_ 1774 5000;
BA_ "GenMsgSendType" BO_ 1724 8;
BA_ "GenMsgCycleTime" BO_ 1581 100;
BA_ "GenMsgCycleTime" BO_ 1080 0;
BA_ "GenMsgCycleTime" BO_ 288 100;
BA_ "GenMsgCycleTime" BO_ 289 100;
BA_ "GenMsgCycleTime" BO_ 320 100;
BA_ "GenMsgCycleTime" BO_ 1780 1000;
BA_ "GenMsgCycleTime" BO_ 275 100;
BA_ "GenMsgCycleTime" BO_ 547 100;
BA_ "GenMsgCycleTime" BO_ 803 0;
BA_ "GenMsgCycleTime" BO_ 819 100;
BA_ "GenMsgCycleTime" BO_ 277 100;
BA_ "GenMsgCycleTime" BO_ 276 100;
BA_ "GenMsgCycleTime" BO_ 550 100;
BA_ "GenMsgCycleTime" BO_ 1571 5000;
BA_ "GenMsgCycleTime" BO_ 1572 5000;
BA_ "GenMsgCycleTime" BO_ 1973 100;
BA_ "GenMsgCycleTime" BO_ 1986 0;
BA_ "GenMsgCycleTime" BO_ 1573 0;
BA_ "GenMsgCycleTime" BO_ 272 5000;
BA_ "GenMsgCycleTime" BO_ 284 5000;
BA_ "GenMsgCycleTime" BO_ 304 100;
BA_ "GenMsgCycleTime" BO_ 336 100;
BA_ "GenMsgCycleTime" BO_ 561 100;
BA_ "GenMsgCycleTime" BO_ 290 100;
BA_ "GenMsgCycleTime" BO_ 352 100;
BA_ "GenMsgCycleTime" BO_ 384 100;
BA_ "GenMsgCycleTime" BO_ 401 100;
BA_ "GenMsgCycleTime" BO_ 1795 0;
BA_ "GenMsgCycleTime" BO_ 1040 500;
BA_ "GenMsgCycleTime" BO_ 1041 500;
BA_ "GenMsgCycleTime" BO_ 368 100;
BA_ "GenMsgCycleTime" BO_ 1746 100;
BA_ "GenMsgCycleTime" BO_ 1617 0;
BA_ "GenMsgCycleTime" BO_ 820 100;
BA_ "GenMsgCycleTime" BO_ 420 100;
BA_ "GenMsgCycleTime" BO_ 424 100;
BA_ "GenMsgCycleTime" BO_ 423 100;
BA_ "GenMsgCycleTime" BO_ 672 100;
BA_ "GenMsgCycleTime" BO_ 421 100;
BA_ "GenMsgCycleTime" BO_ 422 100;
BA_ "GenMsgCycleTime" BO_ 425 100;
BA_ "GenMsgCycleTime" BO_ 426 100;
BA_ "GenMsgCycleTime" BO_ 1618 2000;
BA_ "GenMsgCycleTime" BO_ 1619 2000;
BA_ "GenMsgCycleTime" BO_ 537 2000;
BA_ "GenMsgCycleTime" BO_ 544 2000;
BA_ "GenMsgCycleTime" BO_ 1620 2000;
BA_ "GenMsgCycleTime" BO_ 2018 0;
BA_ "GenMsgCycleTime" BO_ 2019 0;
BA_ "GenMsgCycleTime" BO_ 2020 0;
BA_ "GenMsgCycleTime" BO_ 2021 0;
BA_ "GenMsgCycleTime" BO_ 2022 0;
BA_ "GenMsgCycleTime" BO_ 2023 0;
BA_ "GenMsgCycleTime" BO_ 1568 5000;
BA_ "GenMsgCycleTime" BO_ 1554 5000;
BA_ "GenMsgCycleTime" BO_ 1590 5000;
BA_ "GenMsgCycleTime" BO_ 1616 5000;
BA_ "GenMsgCycleTime" BO_ 1934 0;
BA_ "GenMsgCycleTime" BO_ 1969 0;
BA_ "GenMsgCycleTime" BO_ 1958 0;
BA_ "GenMsgCycleTime" BO_ 1972 0;
BA_ "GenMsgCycleTime" BO_ 1982 0;
BA_ "GenMsgCycleTime" BO_ 1975 0;
BA_ "GenMsgCycleTime" BO_ 1328 100;
BA_ "GenMsgCycleTime" BO_ 1329 100;
BA_ "GenMsgCycleTime" BO_ 1330 0;
BA_ "GenMsgCycleTime" BO_ 1332 0;
BA_ "GenMsgCycleTime" BO_ 1347 0;
BA_ "GenMsgCycleTime" BO_ 1333 0;
BA_ "GenMsgCycleTime" BO_ 1363 0;
BA_ "GenMsgCycleTime" BO_ 1334 0;
BA_ "GenMsgCycleTime" BO_ 1379 0;
BA_ "GenMsgCycleTime" BO_ 1282 100;
BA_ "GenMsgCycleTime" BO_ 1408 100;
BA_ "GenMsgCycleTime" BO_ 1409 100;
BA_ "GenMsgCycleTime" BO_ 1410 100;
BA_ "GenMsgCycleTime" BO_ 1411 100;
BA_ "GenMsgCycleTime" BO_ 1296 100;
BA_ "GenMsgCycleTime" BO_ 1412 100;
BA_ "GenMsgCycleTime" BO_ 1413 100;
BA_ "GenMsgCycleTime" BO_ 1414 100;
BA_ "GenMsgCycleTime" BO_ 1415 100;
BA_ "GenMsgCycleTime" BO_ 1288 100;
BA_ "GenMsgCycleTime" BO_ 1289 100;
BA_ "GenMsgCycleTime" BO_ 128 0;
BA_ "GenMsgCycleTime" BO_ 131 0;
BA_ "GenMsgCycleTime" BO_ 129 0;
BA_ "GenMsgCycleTime" BO_ 11 100;
BA_ "GenMsgCycleTime" BO_ 531 100;
BA_ "GenMsgCycleTime" BO_ 1621 2000;
BA_ "GenMsgCycleTime" BO_ 1651 2000;
BA_ "GenMsgCycleTime" BO_ 1607 1000;
BA_ "GenMsgCycleTime" BO_ 300 100;
BA_ "GenMsgCycleTime" BO_ 2024 100;
BA_ "GenMsgCycleTime" BO_ 2025 100;
BA_ "GenMsgCycleTime" BO_ 1685 100;
BA_ "GenMsgCycleTime" BO_ 1625 100;
BA_ "GenMsgCycleTime" BO_ 1600 10;
BA_ "GenMsgCycleTime" BO_ 427 100;
BA_ "GenMsgCycleTime" BO_ 428 100;
BA_ "GenMsgCycleTime" BO_ 429 100;
BA_ "GenMsgCycleTime" BO_ 430 100;
BA_ "GenMsgCycleTime" BO_ 1765 100;
BA_ "GenSigStartValue" SG_ 821 Offset_Angle_Mechanical_Degree 0;
BA_ "GenSigStartValue" SG_ 1562 ACK_Command_From_APP 0;
BA_ "GenSigSendType" SG_ 1562 ACK_Command_From_APP 7;
BA_ "GenSigStartValue" SG_ 1697 Command_From_APP 0;
BA_ "GenSigSendType" SG_ 1697 Command_From_APP 7;
BA_ "GenSigStartValue" SG_ 787 Vehicle_speed_in_kmph 0;
BA_ "GenSigStartValue" SG_ 787 MCU_Temperature 0;
BA_ "GenSigStartValue" SG_ 787 Motor_Temprature 0;
BA_ "GenSigStartValue" SG_ 787 Motor_RPM 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Amphr_Charged_in_the_Mode 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Kwhr_Charged_in_the_Mode 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Active_Duration_of_the_Mode 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Regenerative_Amphr 0;
BA_ "GenSigStartValue" SG_ 851 Brightness_Level_required_by_User 0;
BA_ "GenSigStartValue" SG_ 1281 Control_protocol_number 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_9V_Supply_Status 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_12V_Supply_voltage_Status 0;
BA_ "GenSigStartValue" SG_ 1754 SBC_Limp_Input 0;
BA_ "GenSigStartValue" SG_ 1754 AFE_Fault_Input 0;
BA_ "GenSigStartValue" SG_ 1754 Charge_Over_Current 0;
BA_ "GenSigStartValue" SG_ 1754 Discharge_Over_current 0;
BA_ "GenSigStartValue" SG_ 1754 RTC_Interrupt 0;
BA_ "GenSigStartValue" SG_ 1754 RTC_Clock_IN 0;
BA_ "GenSigStartValue" SG_ 1754 Charger_Plug_Sense 0;
BA_ "GenSigStartValue" SG_ 1754 BMS_Status 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_12V_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_9V_Supply_Disable 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_5V_Peripheral_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Gate_Drivers_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Charge_MOSFET_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Discharge_MOSFET_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Pre_Charge_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 AFE_Reset_Command 0;
BA_ "GenSigStartValue" SG_ 1754 Overload_Clear 0;
BA_ "GenSigStartValue" SG_ 1754 Enable_3V3_Measure 0;
BA_ "GenSigStartValue" SG_ 31 Cluster_IGN_Status 0;
BA_ "GenSigStartValue" SG_ 3 WakeUp_Sleep_Request 0;
BA_ "GenSigStartValue" SG_ 63 Cluster_Seed 0;
BA_ "GenSigStartValue" SG_ 61 BMS_Key 0;
BA_ "GenSigStartValue" SG_ 79 Auth_Status_BMS 0;
BA_ "GenSigStartValue" SG_ 64 MBMS_Wake_Up_Reason 0;
BA_ "GenSigStartValue" SG_ 151 MCU_Sleep_WakeUp_ACK 0;
BA_ "GenSigStartValue" SG_ 155 MCU_Key 0;
BA_ "GenSigStartValue" SG_ 325 Odometer 0;
BA_ "GenSigStartValue" SG_ 850 Display_SoC 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_chargeSC_80 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_Full_chargeSC_100 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_chargeFC_80 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_chargeFC_100 0;
BA_ "GenSigStartValue" SG_ 1744 Command_To_Cluster 0;
BA_ "GenSigStartValue" SG_ 1744 Hazard_Icon 0;
BA_ "GenSigSendType" SG_ 1724 Command_From_TCU 7;
BA_ "GenSigStartValue" SG_ 1581 Charger_Connected 0;
BA_ "GenSigStartValue" SG_ 1581 Charger_Plugged_In 0;
BA_ "GenSigStartValue" SG_ 1080 Hours 0;
BA_ "GenSigStartValue" SG_ 1080 Minutes 0;
BA_ "GenSigStartValue" SG_ 288 Overall_Battery_Current 0;
BA_ "GenSigStartValue" SG_ 288 Overall_SOC 0;
BA_ "GenSigStartValue" SG_ 288 Overall_Charge_Voltage_limit 0;
BA_ "GenSigStartValue" SG_ 288 Charger_Mode_Request 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Charge_current_limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Discharge_current_limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Regen_Power_Limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Discharge_Power_Limit 0;
BA_ "GenSigStartValue" SG_ 320 Battery_Current_1 30000;
BA_ "GenSigStartValue" SG_ 320 Regen_Power_Limit 0;
BA_ "GenSigStartValue" SG_ 320 Discharge_Power_Limit 0;
BA_ "GenSigStartValue" SG_ 320 Battery_Current 0;
BA_ "GenSigStartValue" SG_ 1780 Vehicle_Range_ECO 0;
BA_ "GenSigStartValue" SG_ 1780 Vehicle_Range_Sport 0;
BA_ "GenSigStartValue" SG_ 1780 Vehicle_Range_Normal 0;
BA_ "GenSigStartValue" SG_ 275 MCU_Power_Status 0;
BA_ "GenSigStartValue" SG_ 275 Regen_Status 0;
BA_ "GenSigStartValue" SG_ 275 Throttle_Value1 0;
BA_ "GenSigStartValue" SG_ 275 Throttle_Value2 0;
BA_ "GenSigStartValue" SG_ 275 Cruise_Control_Status 0;
BA_ "GenSigStartValue" SG_ 275 Vehicle_Discharge_Mode 0;
BA_ "GenSigStartValue" SG_ 275 Hill_Hold_Response 0;
BA_ "GenSigStartValue" SG_ 275 MCU_Voltage 0;
BA_ "GenSigStartValue" SG_ 547 Motor_Current 0;
BA_ "GenSigStartValue" SG_ 547 Motor_Torque 0;
BA_ "GenSigStartValue" SG_ 547 Distance_covered_for_current_trip 0;
BA_ "GenSigStartValue" SG_ 547 MCU_DC_Current 0;
BA_ "GenSigStartValue" SG_ 803 Disconnect_ACK 0;
BA_ "GenSigStartValue" SG_ 819 Software_overcurrent_protection_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Software_overvoltage_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Drive_Protection 0;
BA_ "GenSigStartValue" SG_ 819 Failure_for_motor_parameter_tuning 0;
BA_ "GenSigStartValue" SG_ 819 Drive_Overload_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 U_phase_hall_fault 0;
BA_ "GenSigStartValue" SG_ 819 Drive_overtemperature_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Motor_overtemperature_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Encoder_disconnection_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Overvoltagebaseline_of_hardware_is_wrong_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Stalling_Fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 DC_Bus_undervoltage_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 CAN_communication_abnormal_failure 0;
BA_ "GenSigStartValue" SG_ 819 Motor_over_speed_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Motor_temperature_sensor_disconnection_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Hardware_overcurrent_fault 0;
BA_ "GenSigStartValue" SG_ 819 Hardware_overvoltage_fault 0;
BA_ "GenSigStartValue" SG_ 819 Drive_power_undervoltage_fault 0;
BA_ "GenSigStartValue" SG_ 819 The_resolver_connector_is_loose_and_abnormal 0;
BA_ "GenSigStartValue" SG_ 819 Controller_drive_abnormal_fault 0;
BA_ "GenSigStartValue" SG_ 819 Power_supplyof_drive_board_exceeds_the_upper_limit 0;
BA_ "GenSigStartValue" SG_ 819 Lowvoltage_power_supply_under_voltage_fault 0;
BA_ "GenSigStartValue" SG_ 819 U_phase_Hall_disconnection_fault 0;
BA_ "GenSigStartValue" SG_ 819 V_phase_Hall_disconnection_fault 0;
BA_ "GenSigStartValue" SG_ 819 W_phase_Hall_disconnection_fault 0;
BA_ "GenSigStartValue" SG_ 819 V_phase_Hall_abnormal_fault 0;
BA_ "GenSigStartValue" SG_ 819 W_phase_Hall_abnormal_fault 0;
BA_ "GenSigStartValue" SG_ 819 Throttle_Wiper1_Fail 0;
BA_ "GenSigStartValue" SG_ 819 Throttle_Wiper2_Fail 0;
BA_ "GenSigStartValue" SG_ 819 MCU_calibration_status 0;
BA_ "GenSigStartValue" SG_ 819 Successful_Calibration_Counter 0;
BA_ "GenSigStartValue" SG_ 819 Attempted_Calibration_Counter 0;
BA_ "GenSigStartValue" SG_ 819 MCU_temperature_derating_flag 0;
BA_ "GenSigStartValue" SG_ 819 Motor_temperature_derating_flag 0;
BA_ "GenSigStartValue" SG_ 819 Vehicle_Wrong_Direction_Flag 0;
BA_ "GenSigStartValue" SG_ 277 Forced_Regen_Percentage 0;
BA_ "GenSigStartValue" SG_ 277 Coast_Regen_Percentage 0;
BA_ "GenSigStartValue" SG_ 277 Brake_Regen_Percentage 0;
BA_ "GenSigStartValue" SG_ 276 IDLE_Throttle_Count 0;
BA_ "GenSigStartValue" SG_ 276 Drive_Start_Throttle_Count 0;
BA_ "GenSigStartValue" SG_ 550 MCU_HH_Activation_Counter 0;
BA_ "GenSigStartValue" SG_ 1571 MCU_HW_Version 0;
BA_ "GenSigStartValue" SG_ 1571 MCU_Firmware_Version 0;
BA_ "GenSigStartValue" SG_ 1571 Motor_Type 0;
BA_ "GenSigStartValue" SG_ 1572 MCU_Bootloader_Version 0;
BA_ "GenSigStartValue" SG_ 1973 Start_Stop_switch_status 0;
BA_ "GenSigStartValue" SG_ 1973 Side_stand_Switch_status 0;
BA_ "GenSigStartValue" SG_ 1973 Brake_switch_status 0;
BA_ "GenSigStartValue" SG_ 1973 Reverse_switch_status 0;
BA_ "GenSigStartValue" SG_ 1973 Cruise_Switch_status 0;
BA_ "GenSigStartValue" SG_ 1973 Mode_switch1_status 0;
BA_ "GenSigStartValue" SG_ 1973 Mode_switch2_status 0;
BA_ "GenSigStartValue" SG_ 1986 Hard_Reboot 0;
BA_ "GenSigStartValue" SG_ 1573 Fast_Charger_Disconnection 0;
BA_ "GenSigStartValue" SG_ 1573 Hold_Brake_To_Drive 0;
BA_ "GenSigStartValue" SG_ 1573 Close_The_Side_Stand 0;
BA_ "GenSigStartValue" SG_ 272 Battery_Serial_No_1 0;
BA_ "GenSigStartValue" SG_ 272 Battery_Serial_No_2 0;
BA_ "GenSigStartValue" SG_ 284 Contactor_state 0;
BA_ "GenSigStartValue" SG_ 284 Battery_voltage_OCV 0;
BA_ "GenSigStartValue" SG_ 284 Pack_SOC 0;
BA_ "GenSigStartValue" SG_ 284 BMS_Mode 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Precharge_failure_status_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_charge_inhibit_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_discharge_inhibit_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Derate_Drive_Current_Flag_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Derate_Charge_Current_Flag_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Inhibit_Regen_Fault_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Permanent_Fault_10ms 0;
BA_ "GenSigStartValue" SG_ 284 BMS00_Short_circuit_detection_error_10ms 0;
BA_ "GenSigStartValue" SG_ 304 Charge_current_limit 0;
BA_ "GenSigStartValue" SG_ 304 Discharge_current_limit 0;
BA_ "GenSigStartValue" SG_ 304 Charge_Voltage_limit 0;
BA_ "GenSigStartValue" SG_ 304 Charging_Mode 0;
BA_ "GenSigStartValue" SG_ 336 Bus_Voltage 0;
BA_ "GenSigStartValue" SG_ 336 Delta_Voltage 0;
BA_ "GenSigStartValue" SG_ 336 Available_Capacity 0;
BA_ "GenSigStartValue" SG_ 336 Pack_SoH 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Precharge_failure_status 0;
BA_ "GenSigStartValue" SG_ 290 Battery_charge_inhibit 0;
BA_ "GenSigStartValue" SG_ 290 Battery_discharge_inhibit 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Drive_Current_Flag 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Charge_Current_Flag 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Inhibit_Regen_Fault 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Permanent_Fault 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Voltage_Deviation_Error 0;
BA_ "GenSigStartValue" SG_ 352 Balancing_Status 0;
BA_ "GenSigStartValue" SG_ 352 Effective_Battery_Temperature 0;
BA_ "GenSigStartValue" SG_ 352 Battery_Temperature_Min 0;
BA_ "GenSigStartValue" SG_ 352 Battery_Temperature_Max 0;
BA_ "GenSigStartValue" SG_ 352 Reset_SOC_from_OCV 0;
BA_ "GenSigStartValue" SG_ 384 Charging_time_Max 0;
BA_ "GenSigStartValue" SG_ 384 DC_output_voltage_limit_parameter 0;
BA_ "GenSigStartValue" SG_ 384 Charging_Rate 0;
BA_ "GenSigStartValue" SG_ 401 Calculated_current_based_on_coulomb 0;
BA_ "GenSigStartValue" SG_ 401 Aggregated_current_Ah 0;
BA_ "GenSigStartValue" SG_ 1795 EOL_BMS_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell1 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell2 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell3 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell4 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell5 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell6 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell7 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell8 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell9 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell10 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell11 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell12 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_sensor_failureCell13 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell1 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell2 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell3 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell4 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell5 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell6 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell7 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell8 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell9 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell10 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell11 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell12 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Voltage_out_of_range_Cell13 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature1_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature2_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature3_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature4_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature1_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature2_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature3_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Battery_pack_temperature4_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Current_sensor_failure_Open_circuit 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Current_sensor_failure_Short_circuit 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_current_charge_Warning 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_VOOR_CutOff_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Precharge_too_fast_Info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Precharge_too_slow_Info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Precharge_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_MOSFETs_connection_failed 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_MOSFETs_disconnection_failed 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_PDU_temperature_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_PDU_temperature_error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_voltage_charge_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_voltage_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_voltage_charge_Permanent_Fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_voltage_regen_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_voltage_regen_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Under_voltage_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Under_voltage_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Under_voltage_Permanent_Fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_temperature_charge_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_temperature_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_temperature_drive_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_temperature_drive_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_temperature_due_to_Cell_vent_error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_temperature_due_to_Cell_vent_Failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Short_circuit_detection_error_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Short_circuit_detection_permanent_fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Cell_failure_permanent_fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Over_current_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS_Low_temperature_during_charging_warning_info 0;
BA_ "GenSigStartValue" SG_ 1041 BMS_Low_temperature_during_charging_Error 0;
BA_ "GenSigStartValue" SG_ 1041 BMS_Low_temperature_during_driving_Warning 0;
BA_ "GenSigStartValue" SG_ 1041 BMS_Low_temperature_during_driving_Error 0;
BA_ "GenSigStartValue" SG_ 1041 Over_time_to_fast_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1041 BMS_Voltage_sensor_failureCell14 0;
BA_ "GenSigStartValue" SG_ 1041 BMS_Voltage_out_of_range_Cell14 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature5_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature6_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature5_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature6_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1041 Rat_Bite_Fault 0;
BA_ "GenSigStartValue" SG_ 1041 CellDip_Error 0;
BA_ "GenSigStartValue" SG_ 1041 CellDip_Fault 0;
BA_ "GenSigStartValue" SG_ 1041 CompSpikeDtctn_Fault 0;
BA_ "GenSigStartValue" SG_ 1041 CompSpike_Error 0;
BA_ "GenSigStartValue" SG_ 1041 TempRegen_Warning 0;
BA_ "GenSigStartValue" SG_ 1041 SOCRegenWarn 0;
BA_ "GenSigStartValue" SG_ 1041 TempRiseDriveDtctn_60 0;
BA_ "GenSigStartValue" SG_ 1041 TempRiseChrgDtctn_60 0;
BA_ "GenSigStartValue" SG_ 1041 PDUerrorCutOff_60 0;
BA_ "GenSigStartValue" SG_ 1041 SBC_FAULT_ACTIVE 0;
BA_ "GenSigStartValue" SG_ 1041 SUPPLY_12V_NOT_GOOD 0;
BA_ "GenSigStartValue" SG_ 1041 Supply_9V_Not_Good 0;
BA_ "GenSigStartValue" SG_ 1041 AFE_COM_LOST 0;
BA_ "GenSigStartValue" SG_ 1041 External_Flash_COM_Lost 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_Temp_Fault_during_Slp 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_Temp_Increase_flt_sleep 0;
BA_ "GenSigStartValue" SG_ 1041 BMS_3_3V_Fault_Not_Good 0;
BA_ "GenSigStartValue" SG_ 1041 PDU_Temperature_Rise_Detection_Sleep 0;
BA_ "GenSigStartValue" SG_ 1041 PDU_Temperature_Rise_Detection 0;
BA_ "GenSigStartValue" SG_ 1041 PDU_Temperature_Error_during_Sleep 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_Pack_Temperature_Rise_Sleep 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_Pack_Temperature_Error_Sleep 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_Temperature_Blanket_Cut_off_Grade4 0;
BA_ "GenSigStartValue" SG_ 1041 Overcharge_Protection 0;
BA_ "GenSigStartValue" SG_ 1041 Charger_ReAuth_Warning 0;
BA_ "GenSigStartValue" SG_ 1041 Charger_ReAuth_Error 0;
BA_ "GenSigStartValue" SG_ 1041 Cell_Voltage_Rise_Deration_in_HC 0;
BA_ "GenSigStartValue" SG_ 1041 Thermal_runaway_fault 0;
BA_ "GenSigStartValue" SG_ 1041 Thermal_runaway_shadow_fault 0;
BA_ "GenSigStartValue" SG_ 1041 Cell_Voltage_Rise_Deration_in_HC_1P5C 0;
BA_ "GenSigStartValue" SG_ 1041 Freq_Fault_Trigger 0;
BA_ "GenSigStartValue" SG_ 1041 PDU_TRNED_DRIVE 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_1 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_2 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_3 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_4 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_5 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_6 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_7 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_8 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_9 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_10 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_11 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_12 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_13 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Balancing_Status_Battery_Cell_14 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Cell_Min_Voltage 0;
BA_ "GenSigStartValue" SG_ 368 BMS_Cell_Max_Voltage 0;
BA_ "GenSigStartValue" SG_ 1746 Available_Drive_modes 0;
BA_ "GenSigStartValue" SG_ 1617 Battery_Cell_Type 0;
BA_ "GenSigStartValue" SG_ 1617 Battery_cell_in_Series 0;
BA_ "GenSigStartValue" SG_ 1617 Battery_cell_in_Parallel 0;
BA_ "GenSigStartValue" SG_ 1617 CAN_Database 0;
BA_ "GenSigStartValue" SG_ 1617 BMS_Hardware_Variant 0;
BA_ "GenSigStartValue" SG_ 1617 BMS_DipSwitch 0;
BA_ "GenSigStartValue" SG_ 420 BMS_Pack_Temperature_01 0;
BA_ "GenSigStartValue" SG_ 420 BMS_Pack_Temperature_02 0;
BA_ "GenSigStartValue" SG_ 420 BMS_Pack_Temperature_03 0;
BA_ "GenSigStartValue" SG_ 420 BMS_Pack_Temperature_04 0;
BA_ "GenSigStartValue" SG_ 424 BMS_PDU_Temperature_01 0;
BA_ "GenSigStartValue" SG_ 424 BMS_PDU_Temperature_02 0;
BA_ "GenSigStartValue" SG_ 424 BMS_Battery_Pack_Voltage_Measured 0;
BA_ "GenSigStartValue" SG_ 424 BMS_Cell_Balancing_Temperature 0;
BA_ "GenSigStartValue" SG_ 423 BMS_Pack_Temperature_05 0;
BA_ "GenSigStartValue" SG_ 423 BMS_Pack_Temperature_06 0;
BA_ "GenSigStartValue" SG_ 423 BMS_PDU_Delta_Temperature 0;
BA_ "GenSigStartValue" SG_ 423 BMS_Battery_Temperature_Display 0;
BA_ "GenSigStartValue" SG_ 672 BMS_EPOCH_Time 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_1 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_2 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_3 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_4 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_5 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_6 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_7 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_8 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_9 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_10 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_11 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_12 0;
BA_ "GenSigStartValue" SG_ 426 Cell_Voltage_Cell_13 0;
BA_ "GenSigStartValue" SG_ 426 Cell_Voltage_Cell_14 0;
BA_ "GenSigStartValue" SG_ 426 Cell_Voltage_Avg_1_14 0;
BA_ "GenSigStartValue" SG_ 1618 SAF_MV_CHG_OVERVG_ERROR 0;
BA_ "GenSigStartValue" SG_ 1618 SAF_MV_CHG_OVERVG_HEAL 0;
BA_ "GenSigStartValue" SG_ 1618 SAF_MV_UNDERVG_ERROR 0;
BA_ "GenSigStartValue" SG_ 1618 SAF_MV_UNDERVG_RESM 0;
BA_ "GenSigStartValue" SG_ 1618 SAF_MV_UNDERVG_WARNG 0;
BA_ "GenSigStartValue" SG_ 1619 SLC_MV_THDVTG_CCEND_CELL 0;
BA_ "GenSigStartValue" SG_ 1619 SLC_MV_THDVTG_CHRGEND_CELL 0;
BA_ "GenSigStartValue" SG_ 1619 SOC_AH_USECAP_CELL 0;
BA_ "GenSigStartValue" SG_ 1619 SAF_DEGC_TEMPRISEPDU 0;
BA_ "GenSigStartValue" SG_ 1619 SAF_DEGC_OVERTEMP_CutOff 0;
BA_ "GenSigStartValue" SG_ 1619 SAF_CNT_ERRORCNTR_COMPSPIKE 0;
BA_ "GenSigStartValue" SG_ 537 SAF_DEGC_OVERTEMP_DRVWARNG 0;
BA_ "GenSigStartValue" SG_ 537 SAF_DEGC_TEMPRISECHRG 0;
BA_ "GenSigStartValue" SG_ 537 SAF_DEGC_OVERTEMP_DRVERROR 0;
BA_ "GenSigStartValue" SG_ 537 SAF_DEGC_OVERTEMP_CHGWARNG 0;
BA_ "GenSigStartValue" SG_ 537 SAF_DEGC_TEMPRISEDRV 0;
BA_ "GenSigStartValue" SG_ 537 SAF_DEGC_OVERTEMP_CHGERROR 0;
BA_ "GenSigStartValue" SG_ 537 BCD_DEGC_THSDTEMP_PDUWARNG 0;
BA_ "GenSigStartValue" SG_ 537 BCD_DEGC_DIFFTEMP_PDUWARNG 0;
BA_ "GenSigStartValue" SG_ 537 BCD_DEGC_THSDTEMP_PDUERROR 0;
BA_ "GenSigStartValue" SG_ 544 BCD_DEGC_THSDTEMP_PDU_CUTOFF 0;
BA_ "GenSigStartValue" SG_ 544 SLC_MIN_CVEND_TIMER 0;
BA_ "GenSigStartValue" SG_ 544 SAF_DEGC_OVERTEMP_SLEEPWARNG 0;
BA_ "GenSigStartValue" SG_ 544 SAF_DEGC_TEMPRISESLEEP 0;
BA_ "GenSigStartValue" SG_ 544 SAF_MV_CELLDIPTHDDELTA 0;
BA_ "GenSigStartValue" SG_ 544 SAF_MV_COMPSPIKETHD 0;
BA_ "GenSigStartValue" SG_ 544 ACC_i_MAXDSCHCURRLIM_16P 0;
BA_ "GenSigStartValue" SG_ 1620 SAF_CNT_ERRORCNTR_CELLDIP 0;
BA_ "GenSigStartValue" SG_ 1620 SAF_CNT_OVERVLTGCHRGPERM 0;
BA_ "GenSigStartValue" SG_ 1620 SAF_CNT_ERRORCNTR_SHRTCRCT 0;
BA_ "GenSigStartValue" SG_ 1620 SLC_A_OVERCURR_TOLCHG 0;
BA_ "GenSigStartValue" SG_ 1620 SAF_DEGC_REGENALLOWED 0;
BA_ "GenSigStartValue" SG_ 1620 SAF_PCT_SOCREGENALLOWED 0;
BA_ "GenSigStartValue" SG_ 1620 ACC_i_MAXDSCHCURRLIM_12P 0;
BA_ "GenSigStartValue" SG_ 2018 AFE_Min_Temp 0;
BA_ "GenSigStartValue" SG_ 2018 AFE_Max_Temp 0;
BA_ "GenSigStartValue" SG_ 2018 AFE_fault_temperature_sensor 0;
BA_ "GenSigStartValue" SG_ 2019 AFE_Fault_Status1 0;
BA_ "GenSigStartValue" SG_ 2019 AFE_Fault_Status2 0;
BA_ "GenSigStartValue" SG_ 2019 AFE_Fault_Status3 0;
BA_ "GenSigStartValue" SG_ 2020 Register_R12 0;
BA_ "GenSigStartValue" SG_ 2020 Register_LR 0;
BA_ "GenSigStartValue" SG_ 2021 Register_PC 0;
BA_ "GenSigStartValue" SG_ 2021 Register_PSR 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Low_Voltage_Detect_Reset 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Loss_of_Clock_Reset 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Loss_of_Lock_Reset 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_CMU_Lossof_Clock_Reset 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Watchdog 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_External_Reset_Pin 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Power_On_Reset 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_JTAG_generated_reset 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Core_Lockup 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Software 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_MDM_AP_System_Reset_Request 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Stop_Acknowledge_Error 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Memory_Allocation_Failed 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_System_Reset_Counter 0;
BA_ "GenSigStartValue" SG_ 2022 BMS_Memory_Leakage 0;
BA_ "GenSigStartValue" SG_ 2023 Stack_Overflow 0;
BA_ "GenSigStartValue" SG_ 1568 MBMS_Firmware_Version 0;
BA_ "GenSigStartValue" SG_ 1554 BMS_Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 1590 BMS_Bootloader_Info 0;
BA_ "GenSigStartValue" SG_ 1616 BMS_ASW_Version 0;
BA_ "GenSigStartValue" SG_ 1934 BMS0_Diag_Request 0;
BA_ "GenSigStartValue" SG_ 1969 BMS0_Diag_Response 0;
BA_ "GenSigStartValue" SG_ 1958 MCU_Diag_Request 0;
BA_ "GenSigStartValue" SG_ 1972 MCU_Diag_Response 0;
BA_ "GenSigStartValue" SG_ 1328 Vehicle_Alive 0;
BA_ "GenSigStartValue" SG_ 1329 Fast_Charger_Alive 0;
BA_ "GenSigStartValue" SG_ 1330 Request_to_Unlatch 0;
BA_ "GenSigStartValue" SG_ 1288 Charging_system_error 0;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_malfunction 0;
BA_ "GenSigStartValue" SG_ 1288 EV_incompatibility 0;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_stop_control 1;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_status 0;
BA_ "GenSigStartValue" SG_ 1288 Vehicle_connector_latched 0;
BA_ "GenSigStartValue" SG_ 1288 EVSE_Emergency_Stop 0;
BA_ "GenSigStartValue" SG_ 1288 rated_DC_output_voltage 0;
BA_ "GenSigStartValue" SG_ 1288 Available_DC_output_current 0;
BA_ "GenSigStartValue" SG_ 1288 Confirmed_DC_output_voltage_limit 0;
BA_ "GenSigStartValue" SG_ 1289 Control_protocol_number 0;
BA_ "GenSigStartValue" SG_ 1289 Available_DC_output_power 0;
BA_ "GenSigStartValue" SG_ 1289 Output_voltage 0;
BA_ "GenSigStartValue" SG_ 1289 DC_output_current 0;
BA_ "GenSigStartValue" SG_ 1289 Remaining_charging_time 0;
BA_ "GenSigStartValue" SG_ 128 BMS_To_SC_Seed 0;
BA_ "GenSigStartValue" SG_ 131 SC_Key 0;
BA_ "GenSigStartValue" SG_ 129 BMS_Auth_Response_to_SC_ 0;
BA_ "GenSigStartValue" SG_ 11 Battery_Wakeup_Request 0;
BA_ "GenSigStartValue" SG_ 11 Serial_Number 0;
BA_ "GenSigStartValue" SG_ 11 Firmware_version 0;
BA_ "GenSigStartValue" SG_ 11 Hardware_version 0;
BA_ "GenSigStartValue" SG_ 11 Manufacturer_Code 0;
BA_ "GenSigStartValue" SG_ 11 Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 531 Charger_Status_Signal 0;
BA_ "GenSigStartValue" SG_ 531 Charger_Mode 0;
BA_ "GenSigStartValue" SG_ 531 Charging_Voltage_Available_range 0;
BA_ "GenSigStartValue" SG_ 531 Charging_Current_Available_range 0;
BA_ "GenSigStartValue" SG_ 531 Slow_Charger_Fault_Information 0;
BA_ "GenSigStartValue" SG_ 531 Charger_output_Short_circuit_error 0;
BA_ "GenSigStartValue" SG_ 531 Charger_under_temperature_error 0;
BA_ "GenSigStartValue" SG_ 531 Charger_over_temperature_error 0;
BA_ "GenSigStartValue" SG_ 531 Low_AC_voltage_error 0;
BA_ "GenSigStartValue" SG_ 531 Rectifier_hardware_error 0;
BA_ "GenSigStartValue" SG_ 531 Authentication_Error 0;
BA_ "GenSigStartValue" SG_ 531 Battery_Parameter_Time_out_error 0;
BA_ "GenSigStartValue" SG_ 531 Data_Corruption_error 0;
BA_ "GenSigStartValue" SG_ 531 Charge_control_message_timeout_error 0;
BA_ "GenSigStartValue" SG_ 1621 SOC_value_from_minimum_OCV 0;
BA_ "GenSigStartValue" SG_ 1621 SOC_debug_1 0;
BA_ "GenSigStartValue" SG_ 1621 SLC_CNT_CHRG_RESTART 0;
BA_ "GenSigStartValue" SG_ 1621 SLC_MV_MINVG_STRTCELLBAL 0;
BA_ "GenSigStartValue" SG_ 1651 BMS_Debug1 0;
BA_ "GenSigStartValue" SG_ 1651 BMS_Debug2 0;
BA_ "GenSigStartValue" SG_ 1651 BMS_Bool_Debug3 0;
BA_ "GenSigStartValue" SG_ 1651 BMS_Bool_Debug4 0;
BA_ "GenSigStartValue" SG_ 1651 BMS_Debug5 0;
BA_ "GenSigStartValue" SG_ 1651 BMS_Debug6 0;
BA_ "GenSigStartValue" SG_ 1607 Charge_AmpHr_00 0;
BA_ "GenSigStartValue" SG_ 1607 Discharge_AmpHr_00 65530;
BA_ "GenSigStartValue" SG_ 300 smd_pct_ECM_SOC 0;
BA_ "GenSigStartValue" SG_ 300 smd_pct_Estimated_SoC 0;
BA_ "GenSigStartValue" SG_ 300 V1 0;
BA_ "GenSigStartValue" SG_ 300 V2 0;
BA_ "GenSigStartValue" SG_ 2024 KK_1 0;
BA_ "GenSigStartValue" SG_ 2024 KK_2 0;
BA_ "GenSigStartValue" SG_ 2024 KK_3 0;
BA_ "GenSigStartValue" SG_ 2024 Innov_matrix_1 0;
BA_ "GenSigStartValue" SG_ 2025 Vt_b 0;
BA_ "GenSigStartValue" SG_ 2025 Estimated_error 0;
BA_ "GenSigStartValue" SG_ 1685 Estimated_TTC_Array_1 0;
BA_ "GenSigStartValue" SG_ 1685 Estimated_TTC_Array_2 0;
BA_ "GenSigStartValue" SG_ 1685 Estimated_TTC_Array_3 0;
BA_ "GenSigStartValue" SG_ 1685 Estimated_TTC_Array_4 0;
BA_ "GenSigStartValue" SG_ 1685 Estimated_TTC_Array_5 0;
BA_ "GenSigStartValue" SG_ 1685 Estimated_TTC_Array_6 0;
BA_ "GenSigStartValue" SG_ 1625 Cycle_SOH 0;
BA_ "GenSigStartValue" SG_ 1625 SOH_Array1 0;
BA_ "GenSigStartValue" SG_ 1625 SOH_Array2 0;
BA_ "GenSigStartValue" SG_ 1625 SOH_Array3 0;
BA_ "GenSigStartValue" SG_ 1625 SOH_Array4 0;
BA_ "GenSigStartValue" SG_ 1625 SOH_Array5 0;
BA_ "GenSigStartValue" SG_ 1625 SOH_update_index 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Short_circuit_detection_error 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Precharge_too_fast_Info_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Precharge_too_slow_Info_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Precharge_failure_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_MOSFETs_connection_failed_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_MOSFETs_disconnection_failed_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 Reserved 0;
BA_ "GenSigStartValue" SG_ 1600 Reserved1 0;
BA_ "GenSigStartValue" SG_ 1600 Fault_status_1_00 0;
BA_ "GenSigStartValue" SG_ 1600 Fault_status_2_00 0;
BA_ "GenSigStartValue" SG_ 1600 Fault_status_3_00 0;
BA_ "GenSigStartValue" SG_ 1600 AFE_FAULT_STATUS_00 0;
BA_ "GenSigStartValue" SG_ 1600 EEPROM_Status_00 0;
BA_ "GenSigStartValue" SG_ 1600 Precharge_Switch_demand_00 0;
BA_ "GenSigStartValue" SG_ 1600 Charge_Switch_demand_00 0;
BA_ "GenSigStartValue" SG_ 1600 Discharge_Switch_demand_00 0;
BA_ "GenSigStartValue" SG_ 1600 SBC_LIMP_Status_00 0;
BA_ "GenSigStartValue" SG_ 1600 Bus_Voltage_00_10ms 0;
BA_ "GenSigStartValue" SG_ 427 Cell_DCIR_1 0;
BA_ "GenSigStartValue" SG_ 427 Cell_DCIR_2 0;
BA_ "GenSigStartValue" SG_ 427 Cell_DCIR_3 0;
BA_ "GenSigStartValue" SG_ 427 Cell_DCIR_4 0;
BA_ "GenSigStartValue" SG_ 428 Cell_DCIR_5 0;
BA_ "GenSigStartValue" SG_ 428 Cell_DCIR_6 0;
BA_ "GenSigStartValue" SG_ 428 Cell_DCIR_7 0;
BA_ "GenSigStartValue" SG_ 428 Cell_DCIR_8 0;
BA_ "GenSigStartValue" SG_ 429 Cell_DCIR_9 0;
BA_ "GenSigStartValue" SG_ 429 Cell_DCIR_10 0;
BA_ "GenSigStartValue" SG_ 429 Cell_DCIR_11 0;
BA_ "GenSigStartValue" SG_ 429 Cell_DCIR_12 0;
BA_ "GenSigStartValue" SG_ 430 Cell_DCIR_13 0;
BA_ "GenSigStartValue" SG_ 430 Cell_DCIR_14 0;
BA_ "GenSigStartValue" SG_ 1765 Total_Available_Energy 0;
BA_ "GenSigStartValue" SG_ 1765 Instantaneous_Available_Energy 0;
VAL_ 1637 SettingsTopDisplay 3 "Failed" 2 "Applied" 1 "Ecomaxset" 0 "SNA" ;
VAL_ 1637 SettingsBottomDisplay 3 "HIGH" 2 "MID" 1 "LOW" 0 "SNA" ;
VAL_ 821 B2B_Rotation_Dir_Status 1 "Clock Wise" 0 "Anti Clock Wise" ;
VAL_ 821 Calibration_result 3 "Failure" 2 "Running" 1 "Success" 0 "No_Operation" ;
VAL_ 821 Calibration_State 4 "Autotune stop" 3 "Autotune_Running" 2 "Autotune start" 1 "Calibration Start" 0 "No Operation" ;
VAL_ 1636 Reserved_TP5 1 "Enable" 0 "Disable" ;
VAL_ 1636 CALLING 1 "Enable" 0 "Disable" ;
VAL_ 1636 CRUISE_CONTROL 1 "Enable" 0 "Disable" ;
VAL_ 1636 ADVANCED_REGEN_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 FAST_CHARGING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 HYPER_CHARGING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 HILL_HOLD_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP4 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP3 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP2 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP1 1 "Enable" 0 "Disable" ;
VAL_ 1636 FREE_ROAM_VIEW 1 "Enable" 0 "Disable" ;
VAL_ 1636 INTER_CITY_TRIP_PLANNER 1 "Enable" 0 "Disable" ;
VAL_ 1636 ROAD_TRIP 1 "Enable" 0 "Disable" ;
VAL_ 1636 PUSH_LOCATION_TO_SCOOTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 MAPS_ON_HMI 1 "Enable" 0 "Disable" ;
VAL_ 1636 MOODS 1 "Enable" 0 "Disable" ;
VAL_ 1636 CONCERT_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 PLAY_MUSIC 1 "Enable" 0 "Disable" ;
VAL_ 1636 PARTY_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 CUSTOM_MOTOR_SOUNDS 1 "Enable" 0 "Disable" ;
VAL_ 1636 MEGAPHONE 1 "Enable" 0 "Disable" ;
VAL_ 1636 SCOOTER_WIDGETS 1 "Enable" 0 "Disable" ;
VAL_ 1636 NOTIFICATION_CENTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 FALL_DETECTION 1 "Enable" 0 "Disable" ;
VAL_ 1636 LIVE_LOCATION_SHARING 1 "Enable" 0 "Disable" ;
VAL_ 1636 HAZARD_LIGHTS 1 "Enable" 0 "Disable" ;
VAL_ 1636 EMERGENCY_SOS 1 "Enable" 0 "Disable" ;
VAL_ 1636 HARDWARE_FAILURE_PREDICTION 1 "Enable" 0 "Disable" ;
VAL_ 1636 RIDE_JOURNAL 1 "Enable" 0 "Disable" ;
VAL_ 1636 RIDE_STATS 1 "Enable" 0 "Disable" ;
VAL_ 1636 COMMUNITY 1 "Enable" 0 "Disable" ;
VAL_ 1636 FIND_MY_SCOOTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 PROFILES 1 "Enable" 0 "Disable" ;
VAL_ 1636 DOCS_ON_SCOOTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 FASTER_CHARGING 1 "Enable" 0 "Disable" ;
VAL_ 1636 SLOW_CHARGING 1 "Enable" 0 "Disable" ;
VAL_ 1636 BRAKE_BY_WIRE_SETTINGS 1 "Enable" 0 "Disable" ;
VAL_ 1636 BRAKE_BY_WIRE 1 "Enable" 0 "Disable" ;
VAL_ 1636 AUTO_INDICATOR 1 "Enable" 0 "Disable" ;
VAL_ 1636 TRACTION_CONTROL 1 "Enable" 0 "Disable" ;
VAL_ 1636 FORCED_REGEN 1 "Enable" 0 "Disable" ;
VAL_ 1636 COAST_REGEN 1 "Enable" 0 "Disable" ;
VAL_ 1636 SPEED_LIMIT_ALERT 1 "Enable" 0 "Disable" ;
VAL_ 1636 RANGE_PREDICTION 1 "Enable" 0 "Disable" ;
VAL_ 1636 SMART_PARK 1 "Enable" 0 "Disable" ;
VAL_ 1636 DIY_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 REVERSE_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 ECO_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 NORMAL_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 SPORT_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 CAPP_LOCK_UNLOCK 1 "Enable" 0 "Disable" ;
VAL_ 1636 HYPER_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 CAPP_VEHICLE_STATE 1 "Enable" 0 "Disable" ;
VAL_ 1636 PASSCODE_UNLOCK_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 EARLY_ACCESS_BETA_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 NEW_UPGRADES_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 PROXIMITY_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 ENERGY_INSIGHTS_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 MODE_FENCING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 TIME_FENCING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 GEO_FENCING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 TOW_TAMPER_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 TAKE_ME_HOME_LIGHTS_V2 1 "Enable" 0 "Disable" ;
VAL_ 1783 Move_OS_Pack_Status 4 "TRIAL_ENDING" 3 "EXPIRED" 2 "EXPIRING" 1 "ACTIVATED" 0 "SNA" ;
VAL_ 1632 Take_me_home_lights_API 1 "Enable" 0 "Disable" ;
VAL_ 1632 Tow_protection_API 1 "Enable" 0 "Disable" ;
VAL_ 1632 HIL_HOLD_FEATURE_ENABLE 1 "Enable" 0 "Disable" ;
VAL_ 1632 Early_Beta_Access 1 "Enable" 0 "Disable" ;
VAL_ 1632 New_Upgrades 1 "Enable" 0 "Disable" ;
VAL_ 1632 Mode_Fencing 1 "Enable" 0 "Disable" ;
VAL_ 1632 Time_Fencing 1 "Enable" 0 "Disable" ;
VAL_ 1632 Geo_Fencing 1 "Enable" 0 "Disable" ;
VAL_ 1632 Proximity_L_U 1 "Enable" 0 "Disable" ;
VAL_ 1632 Energy_Insights 1 "Enable" 0 "Disable" ;
VAL_ 1632 TP_HyperCharge 1 "Enable" 0 "Disable" ;
VAL_ 1632 TP_FasterCharge 1 "Enable" 0 "Disable" ;
VAL_ 1632 Advanced_Regen 1 "Enable" 0 "Disable" ;
VAL_ 1750 BBW_setting_from_TCU 3 "High" 2 "Medium" 1 "LOW" 0 "OFF" ;
VAL_ 1750 OMS_Status 2 "JC_CLOSE" 1 "JC_OPEN" 0 "NOT_MAPPED" ;
VAL_ 1750 Regen_setting_from_TCU 3 "High" 2 "Default" 1 "Low" 0 "Off" ;
VAL_ 1750 Vacation_mode 1 "Enable" 0 "Disable" ;
VAL_ 1622 RESTRICT_ODO_IN_SERVICE 1 "Enable" 0 "Disable" ;
VAL_ 1081 Due_Status 3 "CONNECTION_FAILED" 2 "NO_DUES" 1 "DUES_PENDING" 0 "SNA" ;
VAL_ 1043 VIN_Status_Ack 1 "Sent positive ack" 0 "Didn't receive msg" ;
VAL_ 1042 VIN_Status 1 "Vin has changed" 0 "No Change" ;
VAL_ 1562 ACK_Command_From_APP 0 "  No Command " 1 " Vehicle UNLOCK " 2 " Vehicle LOCK" 3 "  Vehicle HeadLamp ON" 4 " Vehicle HeadLamps OFF" 5 " Trunk Open " 6 " Vehicle Remote Immobilize ON " 7 " Vehicle Remote Immobilize OFF " 8 " Lock without Steering " 9 "  Reserved." 10 " Diagnostic Request " 11 " Find My Bike Light Turn ON Request " 12 " Find My Bike Sound Turn ON Request " ;
VAL_ 1697 Command_From_APP 0 "  No Command " 1 " Vehicle UNLOCK " 2 " Vehicle LOCK" 3 "  Vehicle HeadLamp ON" 4 " Vehicle HeadLamps OFF" 5 " Trunk Open " 6 " Vehicle Remote Immobilize ON " 7 " Vehicle Remote Immobilize OFF " 8 " Lock without Steering " 9 "  Reserved." 10 " Diagnostic Request " 11 " Find My Bike Light Turn ON Request " 12 " Find My Bike Sound Turn ON Request " ;
VAL_ 1674 Eco_Max_Status 1 "Enable" 0 "Disable" ;
VAL_ 800 RearWheelAntilock_Torque_Status 3 "Increase" 2 "Decrease" 1 "Hold" 0 "No_Operation" ;
VAL_ 1657 EFlash_State 1 "Mount Success" 0 "Mount Fail" ;
VAL_ 562 Ota_Pending_status 1 "Ota pending On Unlock" 0 "No ota Pending" ;
VAL_ 562 Mqtt_Status 1 "Mqtt connection Success" 0 "Mqtt connection Failure" ;
VAL_ 562 Flash_mount_status 1 "Mount_Success" 0 "Mount_Fail" ;
VAL_ 1777 MIL_WL 2 "MIL On" 1 "MIL Off" 0 "SNA" ;
VAL_ 559 Network_Time_Ack 2 "RTC_cfg_passed" 1 "RTC_cfg_failed" 0 "SNA" ;
VAL_ 557 Fault_Request 1 "Fault_Request" 0 "SNA" ;
VAL_ 557 Fault_Clear 1 "Fault_Clear" 0 "SNA" ;
VAL_ 555 DTC_Fault_Flag 1 "Fault_Triggered" 0 "Fault_Cleared" ;
VAL_ 555 DTC_Fault 59 "BMS_VARIANT_MISMATCH_FAULT" 58 "BMS_BATTERY_TEMPERATURE_BLANKET_CUT_OFF_GRADE_4" 57 "  BMS_BATTERY_PACK_TEMPERATURE_ERROR_SLEEP," 56 "  BMS_BATTERY_PACK_TEMPERATURE_RISE_SLEEP," 55 "BMS_PDU_TEMPERATURE_ERROR_DURING_SLEEP" 54 "BMS_PDU_TEMPERATURE_RISE_DETECTION" 53 "BMS_PDU_TEMPERATURE_RISE_DETECTION_SLEEP" 52 "BMS_BATTERY_TEMP_INCREASE_FLT_SLEEP" 51 "BMS_BATTERY_TEMP_FAULT_DURING_SLEEP" 50 "BMS_AFE_COM_LOST" 49 " BMS_PDU_ERROR_CUTOFF_60" 48 " BMS_TEMP_RISE_CHARGE_DTCTN_60" 47 "BMS_TEMP_RISE_DRIVE_DTCTN_60" 46 " BMS_SHORT_CIRCUIT_DETECTION_PERMANENT_FAULT," 45 " BMS_SHORT_CIRCUIT_DETECTION_ERROR_INFO" 44 " BMS_OVER_TEMPERATURE_DRIVE_ERROR" 43 "BMS_OVER_TEMPERATURE_CHARGE_ERROR" 42 " BMS_UNDER_VOLTAGE_PERMANENT_FAULT" 41 "BMS_UNDER_VOLTAGE_ERROR" 40 " BMS_UNDER_VOLTAGE_WARNING" 39 " BMS_OVER_VOLTAGE_CHARGE_PERMANENT_FAULT" 38 " BMS_OVER_VOLTAGE_CHARGE_ERROR" 37 "BMS_PDU_TEMPERATURE_ERROR" 36 "BMS_MOSFETS_DISCONNECTION_FAILED" 35 " BMS_MOSFETS_CONNECTION_FAILED" 34 "   BMS_PRECHARGE_FAILURE" 33 "BMS_BATTERY_PACK_TEMPERATURE_OUT_OF_RANGE" 32 "BMS_VOLTAGE_OUT_OF_RANGE_CELL" 31 "  MCU_PWM_MISSING_FAULT," 30 "MCU_POS_CNT_MISM_PWM_FAULT" 29 "MCU_ZIPULSE_MISSING_FAULT" 28 "  MCU_AB_PULSE_MISM_ZIPULSE_FAULT" 27 "MCU_THROTTLE_WIPER2_FAIL" 26 "MCU_THROTTLE_WIPER1_FAIL" 25 "MCU_W_PHASE_HALL_ABNORMAL_FAULT" 24 "MCU_V_PHASE_HALL_ABNORMAL_FAULT," 23 "MCU_W_PHASE_HALL_DISCONNECTION_FAULT" 22 " MCU_V_PHASE_HALL_DISCONNECTION_FAULT" 21 "MCU_U_PHASE_HALL_DISCONNECTION_FAULT" 20 "MCU_LOW_VOLTAGE_POWER_SUPPLY_UNDER_VOLTAGE_FAULT" 19 " MCU_POWER_SUPPLYOF_DRIVE_BOARD_EXCEEDS_THE_UPPER_LIMIT," 18 "MCU_CONTROLLER_DRIVE_ABNORMAL_FAULT" 17 "MCU_DRIVE_POWER_UNDER_VOLTAGE_FAULT" 16 "MCU_HARDWARE_OVERVOLTAGE_FAULT" 15 "MCU_HARDWARE_OVERCURRENT_FAULT" 14 "MCU_MOTOR_TEMPERATURE_SENSOR_DISCONNECTION_FAULT_GRADE_3" 13 "MCU_MOTOR_OVER_SPEED_FAULT_GRADE_3" 12 "MCU_CAN_COMMUNICATION_ABNORMAL_FAILURE" 11 "MCU_DC_BUS_UNDERVOLTAGE_FAULT_GRADE_3" 10 "MCU_STALLING_FAULT_GRADE_3" 9 "MCU_OVERVOLTAGEBASELINE_OF_HARDWARE_IS_WRONG_GRADE_3" 8 "MCU_ENCODER_DISCONNECTION_GRADE_3" 7 "MCU_MOTOR_OVERTEMPERATURE_GRADE_3" 6 "MCU_DRIVE_OVERTEMPERATURE_GRADE_3" 5 "MCU_U_PHASE_HALL_FAULT" 4 "MCU_DRIVE_OVERLOAD_GRADE_3" 3 "MCU_FAILURE_FOR_MOTOR_PARAMETER_TUNING" 2 "MCU_DRIVE_PROTECTION" 1 "MCU_SOFTWARE_OVERVOLTAGE_FAULT_GRADE_3

     " 0 "MCU_SOFTWARE_OVERCURRENT_PROTECTION_GRADE_3 = 0" ;
VAL_ 554 Reverse_Btn_Sts 2 "LONG_PRESS" 1 "SHORT_PRESS" 0 "NOT_PRESSED" ;
VAL_ 554 Cruise_Btn_Sts 2 "LONG_PRESS" 1 "SHORT_PRESS" 0 "NOT_PRESSED" ;
VAL_ 552 CPW_12v 1 "Enabled" 0 "Disabled" ;
VAL_ 1281 Estimated_charging_time 65535 "Default Value" ;
VAL_ 1281 Maximum_Charging_time 65535 "Default Value" ;
VAL_ 1281 Charging_rate 255 "Default Value" ;
VAL_ 1280 DC_output_voltage_target_parameter 65535 "Default Value" ;
VAL_ 1280 DC_output_voltage_limit_parameter 65535 "Default Value" ;
VAL_ 1280 Digital_communication_toggle 1 " request to stop communication" 0 "Normal Condition during communication " ;
VAL_ 1280 Wait_request_to_delay_energy_transfer 1 "request to wait" 0 "No request" ;
VAL_ 1280 EV_contactor_status 1 " EV contactor open before charging " 0 "others" ;
VAL_ 1280 EV_charging_stop_control 1 "After Transmission" 0 "Before Transmission" ;
VAL_ 1280 EV_charging_position 1 "Inappropriate Position" 0 "Appropriate Position" ;
VAL_ 1280 EV_charging_enabled 1 "Enabled" 0 "Disabled " ;
VAL_ 1280 High_battery_temperature 1 "Error" 0 "Error Free" ;
VAL_ 1280 Battery_voltage_deviation_error 1 " Error" 0 "Error Free" ;
VAL_ 1280 Battery_current_deviation_error 1 "Error" 0 "Error Free" ;
VAL_ 1280 Battery_undervoltage 1 "Error" 0 "Error Free" ;
VAL_ 1280 Battery_overvoltage 1 "Error" 0 "Error Free" ;
VAL_ 1280 Energy_transfer_system_error 1 "Error" 0 "Error Free" ;
VAL_ 548 Vehicle_Dir_flag 1 "Forward" 0 "Reverse" ;
VAL_ 1754 BMS_NVM_Failure_Status 1 "YES" 0 "NO" ;
VAL_ 1754 BMS00_9V_Supply_Status 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_12V_Supply_voltage_Status 1 "ON" 0 "OFF" ;
VAL_ 1754 SBC_Limp_Input 1 "ON" 0 "OFF" ;
VAL_ 1754 AFE_Fault_Input 1 "ON" 0 "OFF" ;
VAL_ 1754 Charge_Over_Current 1 "ON" 0 "OFF" ;
VAL_ 1754 Discharge_Over_current 1 "ON" 0 "OFF" ;
VAL_ 1754 RTC_Interrupt 1 "ON" 0 "OFF" ;
VAL_ 1754 RTC_Clock_IN 1 "ON" 0 "OFF" ;
VAL_ 1754 Charger_Plug_Sense 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS_Status 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_12V_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_9V_Supply_Disable 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_5V_Peripheral_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Gate_Drivers_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Charge_MOSFET_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Discharge_MOSFET_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Pre_Charge_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 AFE_Reset_Command 1 "ON" 0 "OFF" ;
VAL_ 1754 Overload_Clear 1 "ON" 0 "OFF" ;
VAL_ 1754 Enable_3V3_Measure 1 "ON" 0 "OFF" ;
VAL_ 1779 VehicleDiagMode 5 "Settings" 4 "About" 3 "Dyno" 2 "ECOS" 1 "Diagnostic" 0 "Normal" ;
VAL_ 1781 Test_Tool_Alive 1 "Present" ;
VAL_ 2000 Vehicle_Type_Extended 27 "M3X+_4.5KWh" 26 "S1_Pro+_Gen3_4KWh" 25 "S1_Pro_Gen3_4KWh" 24 "S1_Pro_Gen3_3KWh" 23 "S1X+_Gen3_4KWh" 22 "S1X_Gen3_4KWh" 21 "S1X_Gen3_3KWh" 20 "S1X_Gen3_2KWh" 19 "M3X_4.5KWh" 18 "M3X_3.5KWh" 17 "M3X_2.5KWh" 16 "S1X_4KWh     " 15 "S1X 3KWh      " 14 "S1X 2KWh" 13 "S1X+ Phase2 4KwH" 12 "S1X+ Phase1 4KwH " 11 "Reserved" 10 "S1X+ Phase2" 9 "S1 Pro Gen2 W/O Upgrade" 8 "S1 Air Fleet" 7 "S1X+" 6 "S1 Pro Gen2 With Upgrade" 5 "S1 Air Customer" 4 "S1 2KW" 3 "S1 Pro with upgrade" 2 "S1 Pro wo upgrade" 1 "S1" ;
VAL_ 1778 HighBeam 1 "ON" 0 "OFF" ;
VAL_ 1778 LeftIndicator 1 "ON" 0 "OFF" ;
VAL_ 1778 RightIndicator 1 "ON" 0 "OFF" ;
VAL_ 31 Cluster_IGN_Status 2 "ON" 1 "OFF," 0 "SNA," ;
VAL_ 3 Key_Status 2 "ON" 1 "OFF" 0 "SNA" ;
VAL_ 3 WakeUp_Sleep_Request 2 " All Sleep" 1 "All WakeUp" 0 " SNA" ;
VAL_ 79 Auth_Status_BMS 1 "Successful" 0 "Unsuccessful" ;
VAL_ 64 MBMS_Wake_Up_Reason 7 "EOL WakeUp" 6 "RTC Wakeup" 5 " SoC Push Button Wakeup " 4 " IMU Wakeup " 3 " Charger CAN Wakeup " 2 " Reserved" 1 " Ready to Sleep" 0 " SNA " ;
VAL_ 151 MCU_Sleep_WakeUp_ACK 2 " Invalid" 1 " Positive ACK" 0 " Negative ACK" ;
VAL_ 850 Bootup_Screen 1 "Enable" 0 "Disable" ;
VAL_ 1744 Command_To_Cluster 205 "REVERSE_NA_BUY_MOVEOSPLUS" 204 "CRUISE_NA_BUY_MOVEOSPLUS" 203 "ECO_NA_BUY_MOVEOSPLUS" 202 "SPORTS_NA_BUY_MOVEOSPLUS" 201 "BRAKE_PRESSED" 200 "IN_SERVICE_REMAINING_RANGE" 199 "PM_EDRIV_NUDGE" 198 "DRV_DELAY5_SEC_PM" 197 "RESTRCTD_DRV_MODE_PM" 196 "DRV_DISABLD_PM_WARN" 195 "DRV_DISABLD_PM" 194 "BBW_HIGH" 193 "BBW_MID" 192 "BBW_LOW" 191 "BBW_OFF" 190 "BBW_RIGHT_BRAKE_FAILURE" 189 "BBW_LEFT_BRAKE_FAILURE" 188 "PAY_ON_OLA_ELECTRIC_APP" 187 "DUES_TO_PAY" 186 "CHARGING_NA_CONCTN_FAILED" 185 "DUES_UNPAID_CHARGE_STOPPED" 184 "USE_REGEN_FOR_HIGHER_RANGE" 183 "PART_MISMATCH" 182 "PART_MISMATCH_RANGE_EXHAUSTED" 181 "CLEAR" 180 "CANNOT_GO_TO_DRIVE" 179 "IN_SERVICE" 178 "IN_SERVICE_RANGE_EXHAUST" 177 "SCOOTER_NOT_DELIVERED" 176 "NOT_DELIVERED_EXHAUSTED_RANGE" 175 "SCOOTER_IN_SERVICE_MODE " 174 "SCOOTER_IN_SERVICE_MODE_CALL_SERVICE" 173 "MOVEOS_PACK_TRIAL_ENDING" 172 "MOVEOS_PACK_EXPIRED" 171 "MOVEOS_PACK_EXPIRING" 170 "MOVEOS_PACK_ACTIVATED" 169 "HIGH_REGEN_APPLIED" 168 "DEFAULT_REGEN_APPLIED" 167 "LOW_REGEN_APPLIED" 166 "NO_REGEN_APPLIED" 165 "GEO_FENCE_BREACHED" 164 "TIME_FENCE_BREACHED" 163 "DISABLED_SPORTS_TECH_PACK" 162 "BOOST_CHARGE_BUY_MOVEOSPLUS" 161 "CHARGING_NA_BUY_MOVEOSPLUS" 160 "ENABLE_RIDE_TO_CHANGE_MODE" 159 "ENABLE_RIDE_TO_REVERSE" 152 "ACCIDENT_DETECTED" 151 "FREE_SIGNAL" 148 "PRNTCNTRL_ACS_RESET" 147 "PRNTCNTRL_HYPER_ACS_REVOKED" 146 "PRNTCNTRL_SPORT_ACS_REVOKED" 158 "CHARGING_NA_CALL_SERVICE" 157 "CHARGE_BATT_LOWTEMP_ERR" 156 "CRUISE_DEACT_BATT_CURR_SPIKE" 155 "LOW_BATTERY_CANNOT_ENABLE_CRUISE" 154 "LOW_BATTERY_DEACTIVATING_CRUISE" 153 "TAMPER_PASS_EXIT" 145 " GEOFEN_ACS_REVOKED" 144 " DISABLED_SPORTS_MODE_TEMP" 143 " DISABLED_SPORTS_MODE_SOC" 142 " DISABLED_SPORTS_MODE_FAULT" 141 "THERMAL_RUNAWAY" 140 " VERSION_MISMATCH" 139 " CONFIG_MISMATCH" 138 " ACCS_RVKED_CONTACT_OWNER" 137 " Please_Release_Emergency_Button_To_Start_Charging" 136 " Exiting Vacation Mode" 135 " Entering Vacation Mode" 133 "UNPLUG_HYPER_CHGR_FAULT" 132 " INITIALIZING_HYPERCHARGER" 131 " SAFE_UNPLUG_HYPER_CHARGER" 130 " UNPLUG_CHGR_TO_RIDE" 128 " DCHRG_INHBT_ERROR" 127 " DERATE_DRV_FLT" 126 " DERATE_THROT_ERROR" 125 " RIDE_ACCS_RVKED_AFTER_PARK" 124 " CHGR_LATCH_NOT_CONED" 123 " CHGR_PWR_OFF" 122 " CHGR_TEMPORARY_FAULT" 121 " CHGR_PERMANENT_FAULT" 120 " LOCK_FAIL_CTA" 119 " UNLOCK_FAIL_CTA" 118 " USER_CALIBRATION_FAIL" 117 " USER_CALIBRATION_SUCCESS" 116 " VEHICLE_FALL_ALARM_IGNORED" 115 " VEHICLE_FALL_RECOVERED" 114 " VEHICLE_FALL_DETECTED" 113 " NORMAL_MODE_AVAILABLE" 112 " STOP_CHRG_OVER_TEMP_ERR" 111 " CHRG_CMPLT_UNPLG_CHRGR" 110 " LOCK_IN_PRGS" 109 " OVR_CHRG_PROTECTION" 108 " CNTCR_TOGGLING_ISSUE" 107 " BMS_GRADE4_CUTOFF" 106 "CAN_FAIL_WAIT_CONTACT_CS" 105 "DISABLED_TEMP_MODE_RISE_SWITCH" 104 "DISABLED_SPIKE_MODE_SWITCH" 103 "DISABLED_CELLDIP_MODE_SWITCH" 102 "CHRG_TEMP_RISE_GRADE2" 101 " DRV_TEMP_RISE_GRADE2" 100 " RERATE_HIGHER_MODE_SLP_OT" 99 " SLP_TEMP_RISE_GRADE2" 98 " RERATING_REVIVAL_CAN" 97 " RERATE_HIGHER_MODE_SPIK_ERR" 96 " RERATE_HIGHER_MODE_CELL_DIP" 95 " RERATE_HIGHER_MODE_CHRG_TEMP" 94 " RERATE_HIGHER_MODE_DRV_TEMP" 93 " CAN_FAIL" 92 " SPIKE_ERR_GRADE2" 91 " CELL_DIP_ERR_GRADE2" 90 " Initiate Navigation Ping Test" 89 "Initiate Tripometer Reset" 88 "MCU_GRADE2_DISABLED_HIGHER" 87 "PDU_WARN_CHRG" 86 "DERATE_LOW_TEMP_ERR_START" 85 "DERATE_LOW_TEMP_WARN_START" 84 "CHARGE_BATT_LOWTEMP_WARN" 83 "BATT_TEMP_SNSR_FAIL_ALL_CHRG" 82 "BATT_TEMP_SNSR_FAIL_CHRG" 81 "VLT_SNSR_FAIL_ALL_CHRG" 80 "VLT_SNSR_FAIL_CHRG" 79 "BATT_TEMP_OOR_ALL_CHRG" 78 "BATT_TEMP_OOR_CHRG" 77 "BATT_SNSR_FAIL_CONTACT_CS" 76 " VIN_MISMATCH" 75 "MCU_GRADE3_RESTART" 74 "MCU_GRADE3_PARK" 73 "MCU_GRADE2_CONTACT_CS" 72 "MCU_GRADE2_RESTART" 71 "MCU_GRADE2_ECO" 70 "OVER_CURR_CHRG_WARN" 69 "BATT_TEMP_SNSR_FAIL_ALL" 68 "BATT_TEMP_SNSR_FAIL" 67 "VLT_SNSR_FAIL_ALL" 66 "VLT_SNSR_FAIL" 65 "CURR_SNSR_FAIL_OC_SC" 64 " BATT_TEMP_OOR_ALL" 63 " BATT_TEMP_OOR" 62 " HOLD_BRK_SSB_TO_DRV" 61 " APPLY_SIDE_STAND_TO_LOCK" 60 "SIDE_STAND_OPEN" 59 " CLOSE_SIDE_STAND_TO_DRIVE" 58 "DERATE_CHRG_OVER_TEMP_WARN" 57 " RERATING_REVIVAL_MCU" 56 "RERATING_REVIVAL_MOT" 55 "DERATE_MCU_CHILL_WARN" 54 " DERATE_MOT_CHILL_WARN" 53 " RERATING_HIGHER_MODE_MCU" 52 "RERATING_HIGHER_MODE_MOT" 51 " RERATING_HIGHER_MODE_OT" 50 "DERATE_MCU_TEMP_WARN_START" 49 "DERATE_MOT_TEMP_WARN_START" 48 "DISABLED_MCU_MODE_SWITCH" 47 " DISABLED_MOT_MODE_SWITCH" 46 "DERATE_MCU_COOLDOWN_WAIT" 45 " DERATE_MOT_COOLDOWN_WAIT" 44 " DERATE_MCU_TEMP_ERROR_START" 43 "DERATE_MOT_TEMP_ERROR_START" 42 " CHARGE_NOT_HAPPENING" 41 "RERATING_REVIVAL_OT" 40 "DISABLED_OT_MODE_SWITCH" 39 "DERATE_TEMP_COOLDOWN_WAIT" 38 "DERATE_OVER_TEMP_ERR_INPGRS" 37 "DERATE_OVER_TEMP_ERR_START" 36 "DERATE_OVER_TEMP_WARN_INPGRS" 35 "DERATE_OVER_TEMP_WARN_START" 34 "GENERAL_LOW_BATTERY" 33 "DERATE_SOC_20" 32 "DERATE_SOC_20_WARNING" 31 "DERATE_SOC_15" 25 " ENTERING_INTO_DEEP_SLEEP" 24 " TRUNK_CLOSE_NOTIFCATION" 23 " TRUNK_OPEN_NOTIFICATION" 16 " DERATE_SOC_15_WARNING" 15 " RERATING_HIGHER_MODE." 14 " DERATE_PDU_TEMP_WARN_INPGRS" 13 " DERATE_PDU_TEMP_WARN_START" 12 " DERATE_SOC_3" 11 " DERATE_SOC_3_WARNING" 10 " DERATE_PDU_TEMP_ERROR_INPGRS" 9 "DERATE_PDU_TEMP_ERROR_START" 8 "RERATING_REVIVAL_PDU" 7 "DERATE_AFE_FAULT" 6 " DISABLED_HIGHER_MODE" 5 " DISABLED_PDU_MODE_SWITCH" 4 "DERATE_CHARGE_TO_RIDE" 3 " DERATE_PDU_COOLDOWN_WAIT" 2 " DERATE_AFE_FAULT_CCS" 0 " No Command" ;
VAL_ 1744 Hazard_Icon 2 "ON" 1 "OFF" 0 "SNA" ;
VAL_ 1724 Wifi_Connection_Status 3 "WIFI_CONNECTION_FAILURE" 2 "WIFI_CONNECTING" 1 "WIFI_CONNECTION_SUCCESSFUL" 0 "SNA" ;
VAL_ 1724 OTA_Installation_Message 10 "Please key ON to continue installation" 9 "Installing, Do not key off" 8 "Rollback fail" 7 "Rollback success" 6 "Install Verification" 5 "Install fail" 4 "Install Success" 3 "Installing" 2 "Install not eligible" 1 "Install Eligible" 0 "SNA" ;
VAL_ 1724 Command_From_TCU 97 "OTA_MCU_Reset" 89 "Drive_acess_revoked_disabled" 88 "Drive_acess_revoked_enable" 87 "Alarm_Unlock_Now" 86 "TMH_Lights_Disabled" 85 "TMH_Lights_Enable" 84 "Lock_Now" 83 "Ride_Now" 82 "Smart_Head_Light_SMART_LIGHT_EN_Deselected" 81 "Version_Mismatched" 80 "Exit_Diagnostic_Mode" 79 "HMI_Alarm_ignore_False" 78 "Tow_Theft_High_Sensitivity" 77 "Tow_Theft_Low_Sensitivity" 76 "HMI_Alarm_ignore_True" 75 "Hazard_Light_Turn_OFF" 74 "Hazard_Light_Turn_ON" 73 "User_Calibration_Request" 72 "Delete_Profile" 71 "I_am_Safe" 70 "Need_Help" 69 "Vacation_Mode_OFF" 68 "Vacation_Mode_ON" 67 "Hill_Hold_Request_Disable_Selected" 66 "Hill_Hold_Request_Enable_Selected" 65 "Motor_Cutoff_Delay_Selected_Disable_Selected" 64 "Motor_Cutoff_Delay_Selected_Enable_Selected" 63 "Auto_Indicator_turn_off_feature_Disable_Selected" 62 "Auto_Indicator_turn_off_feature_Enable_Selected" 61 "Hazard_Light_Feature_Disable_Selected" 60 "Hazard_Light_Feature_Enable_Selected" 59 "Auto_Head_Light_Handling_Disable_Selected" 58 "Auto_Head_Light_Handling_Enable_Selected" 57 "Smart_DRL_Light_Disable_Selected" 56 "Smart_DRL_Light_Enable_Selected" 55 "Proximity_Lock_Unlock_Disable_Selected" 54 "Proximity_Lock_Unlock_Enable_Selected" 53 "Tow_Tamper_Detection_Disable_Selected" 52 "Tow_Tamper_Detection_Enable_Selected" 51 "Accident_Detection_Disable_Selected" 50 "Accident_Detection_Enable_Selected" 49 "Boost_Mode_Disable_Selected" 48 "Boost_Mode_Enable_Selected" 47 "Smart_Head_Light_SMART_LIGHT_EN_Selected" 46 "Smart_Head_Light_HIGH_BEAM_EN_Selected" 45 "Smart_Head_Light_LOW_BEAM_EN_Selected     " 44 "Smart_Head_Light_DEFAULT_Selected" 43 "Regen_Type_HIGHWAY_GLIDE_EN_Selected" 42 "Regen_Type_CITY_RIDE_EN_Selected" 41 "Regen_Type_Default_Selected" 40 "Auto_Head_Light_RESERVED" 39 "Auto_Head_Light_SUN_SET_EN" 38 "Auto_Head_Light_SUN_RISE_EN" 37 "Front_DRL_OFF" 36 "Front_DRL_ON" 35 "Right_Indicator_OFF" 34 "Right_Indicator_ON" 33 "Left_Indicator_OFF" 32 "Left_Indicator_ON" 31 "Rear_Light_OFF" 30 "Rear_Light_ON" 29 "Low_Beam_OFF" 28 "Low_Beam_ON" 27 "High_Beam_OFF" 26 "High_Beam_ON" 25 "PASSCODE_AUTH" 24 "CTA Ignore" 23 "CTA Unlock" 22 "CTA Lock" 21 "Trip Reset Succesfull " 20 "Passcode ACK" 19 "HU Reboot" 18 "MCU Reboot" 17 "OTA NONE" 16 "OTA Installation in Progress" 15 "OTA Installation Started" 14 "Scooter Reboot" 13 "Stop Charge" 12 "Rejected Get Home Request" 11 "Accepted Get Home Request" 10 "Headlamp Turn Off reuest" 9 "ALARM Turn Off Request" 8 "Vehicle Shutdown is rejected by User" 7 "ECO Mode accepted by User" 6 "ECO Mode rejected by User" 5 "Headlight turn ON request" 4 "Reverse Mode Activated" 3 "Seat Unlock Cmd" 2 "Lock without Steering" 1 "Lock with Steering" 0 "No Command" ;
VAL_ 1581 Charger_Connected 3 "Boost Charger" 2 " Slow Charger" 1 " Fast Charger" 0 "None (By Default)" ;
VAL_ 1581 Charger_Plugged_In 1 " ON" 0 " OFF (By Default)" ;
VAL_ 288 FC_contactor_Positive_supply_command 1 "ON" 0 "OFF" ;
VAL_ 288 Charger_Mode_Request 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 275 MCU_Power_Status 3 " Error" 2 " Sleep" 1 " Wakeup" 0 " SNA" ;
VAL_ 275 Regen_Status 4 "  Coast_Forced_Brake Regen" 3 " Coast_Brake_Regen" 2 " Coast_Forced_Regen" 1 " Coast_Regen" 0 "Not Active" ;
VAL_ 275 Cruise_Control_Status 1 " Active" 0 "Not Active" ;
VAL_ 275 Vehicle_Discharge_Mode 8 "CUSTOM/DIY" 7 "SERVICE" 6 "LIMPHOME" 5 "REVERSE" 4 "BOOST" 3 "SPORT" 2 "NORMAL" 1 "COAST" 0 "PARK" ;
VAL_ 275 Hill_Hold_Response 3 " Going to deactivate" 2 "Hill Hold Failure to Activate" 1 "Active" 0 "Not Active" ;
VAL_ 803 Disconnect_ACK 1 "Positive ACK" 0 "Negative ACK" ;
VAL_ 819 Software_overcurrent_protection_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Software_overvoltage_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_Protection 1 " Error" 0 " No Error" ;
VAL_ 819 Failure_for_motor_parameter_tuning 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_Overload_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 U_phase_hall_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_overtemperature_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Motor_overtemperature_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Encoder_disconnection_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Overvoltagebaseline_of_hardware_is_wrong_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Stalling_Fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 DC_Bus_undervoltage_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 CAN_communication_abnormal_failure 1 " Error" 0 " No Error" ;
VAL_ 819 Motor_over_speed_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Motor_temperature_sensor_disconnection_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Hardware_overcurrent_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Hardware_overvoltage_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_power_undervoltage_fault 1 " Error" 0 " No Error" ;
VAL_ 819 The_resolver_connector_is_loose_and_abnormal 1 " Error" 0 " No Error" ;
VAL_ 819 Controller_drive_abnormal_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Power_supplyof_drive_board_exceeds_the_upper_limit 1 " Error" 0 " No Error" ;
VAL_ 819 Lowvoltage_power_supply_under_voltage_fault 1 " Error" 0 " No Error" ;
VAL_ 819 U_phase_Hall_disconnection_fault 1 " Error" 0 " No Error" ;
VAL_ 819 V_phase_Hall_disconnection_fault 1 " Error" 0 " No Error" ;
VAL_ 819 W_phase_Hall_disconnection_fault 1 " Error" 0 " No Error" ;
VAL_ 819 V_phase_Hall_abnormal_fault 1 " Error" 0 " No Error" ;
VAL_ 819 W_phase_Hall_abnormal_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Throttle_Wiper1_Fail 1 " Error" 0 " No Error" ;
VAL_ 819 Throttle_Wiper2_Fail 1 " Error" 0 " No Error" ;
VAL_ 819 MCU_calibration_status 3 " Done" 2 " Inprogress" 1 " Pending" 0 " Not Active" ;
VAL_ 819 MCU_temperature_derating_flag 1 " Enable" 0 " Disable" ;
VAL_ 819 Motor_temperature_derating_flag 1 " Enable" 0 " Disable" ;
VAL_ 819 Vehicle_Wrong_Direction_Flag 1 " Error" 0 " No Error" ;
VAL_ 1571 Motor_Type 2 " Anand_Mando_HUB" 1 " OLA_1mm" 0 " SEG" ;
VAL_ 1973 Start_Stop_switch_status 1 " ON" 0 " OFF" ;
VAL_ 1973 Side_stand_Switch_status 1 " ON" 0 " OFF" ;
VAL_ 1973 Brake_switch_status 1 " ON" 0 " OFF" ;
VAL_ 1973 Reverse_switch_status 1 " ON" 0 " OFF" ;
VAL_ 1973 Cruise_Switch_status 1 " ON" 0 " OFF" ;
VAL_ 1973 Mode_switch1_status 1 " ON" 0 " OFF" ;
VAL_ 1973 Mode_switch2_status 1 " ON" 0 " OFF" ;
VAL_ 1573 Fast_Charger_Disconnection 1 " True" 0 " False" ;
VAL_ 1573 Hold_Brake_To_Drive 1 "Pressed" 0 "Not_Pressed" ;
VAL_ 1573 Close_The_Side_Stand 1 "Applied" 0 "Not_Applied" ;
VAL_ 284 Ready_to_sleep 1 "Enable" 0 "Disable" ;
VAL_ 284 Contactor_state 9 " BC_SECONDARY_CONNECT_CHG" 8 " BC_SECONDARY_CONNECT_DSG" 7 "DISCONNECT_PENDING" 6 "CONNECTED" 5 "CONNECT_PENDING" 4 "CONNECT_PRECHARGEMAIN" 3 "CONNECT_PRECHARGE" 2 "DISCONNECTED" 1 " NOT_AVAILABLE" 0 "NOT_APPLICABLE" ;
VAL_ 284 BMS_Mode 8 " Invalid" 7 " Fault / Error " 6 " Sleep " 5 " Discharge " 4 " Charge " 3 " Standby " 2 " Reserved" 1 " Sleep Pending" 0 " Initialise " ;
VAL_ 284 Battery_Precharge_failure_status_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_charge_inhibit_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_discharge_inhibit_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Derate_Drive_Current_Flag_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Derate_Charge_Current_Flag_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Inhibit_Regen_Fault_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Permanent_Fault_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 BMS00_Short_circuit_detection_error_10ms 1 " Fault" 0 "No fault" ;
VAL_ 304 Charging_Mode 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 290 Battery_Precharge_failure_status 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_charge_inhibit 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_discharge_inhibit 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Drive_Current_Flag 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Charge_Current_Flag 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Inhibit_Regen_Fault 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Permanent_Fault 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Voltage_Deviation_Error 1 " Error" 0 "Error Free" ;
VAL_ 352 Balancing_Status 1 "ON" 0 "OFF" ;
VAL_ 352 Reset_SOC_from_OCV 1 " Enable" 0 " Disable" ;
VAL_ 384 ECOS_Charging_Flag 1 "True" 0 "False" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell1 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell2 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell3 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell4 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell5 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell6 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell7 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell8 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell9 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell10 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell11 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell12 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_sensor_failureCell13 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell1 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell2 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell3 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell4 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell5 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell6 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell7 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell8 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell9 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell10 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell11 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell12 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Voltage_out_of_range_Cell13 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature1_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature2_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature3_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature4_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature1_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature2_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature3_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Battery_pack_temperature4_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Current_sensor_failure_Open_circuit 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Current_sensor_failure_Short_circuit 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_current_charge_Warning 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_VOOR_CutOff_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Precharge_too_fast_Info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Precharge_too_slow_Info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Precharge_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_MOSFETs_connection_failed 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_MOSFETs_disconnection_failed 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_PDU_temperature_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_PDU_temperature_error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_voltage_charge_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_voltage_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_voltage_charge_Permanent_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_voltage_regen_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_voltage_regen_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Under_voltage_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Under_voltage_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Under_voltage_Permanent_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_temperature_charge_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_temperature_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_temperature_drive_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_temperature_drive_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_temperature_due_to_Cell_vent_error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_temperature_due_to_Cell_vent_Failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Short_circuit_detection_error_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Short_circuit_detection_permanent_fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Cell_failure_permanent_fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Over_current_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS_Low_temperature_during_charging_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1041 VIN_Variant_Mismatch_Fault 1 "Fault" 0 "No fault" ;
VAL_ 1041 Cell_Variant_mismatch_fault 1 "Fault" 0 "No fault" ;
VAL_ 1041 BMS_Low_temperature_during_charging_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS_Low_temperature_during_driving_Warning 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS_Low_temperature_during_driving_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 Over_time_to_fast_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS_Voltage_sensor_failureCell14 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS_Voltage_out_of_range_Cell14 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature5_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature6_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature5_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature6_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1041 Rat_Bite_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1041 CellDip_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 CellDip_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1041 CompSpikeDtctn_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1041 CompSpike_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 TempRegen_Warning 1 " True" 0 "False" ;
VAL_ 1041 SOCRegenWarn 1 " True" 0 "False" ;
VAL_ 1041 TempRiseDriveDtctn_60 1 " True" 0 "False" ;
VAL_ 1041 TempRiseChrgDtctn_60 1 " True" 0 "False" ;
VAL_ 1041 PDUerrorCutOff_60 1 " True" 0 "False" ;
VAL_ 1041 SBC_FAULT_ACTIVE 1 " True" 0 "False" ;
VAL_ 1041 SUPPLY_12V_NOT_GOOD 1 " True" 0 "False" ;
VAL_ 1041 Supply_9V_Not_Good 1 " True" 0 "False" ;
VAL_ 1041 AFE_COM_LOST 1 " True" 0 "False" ;
VAL_ 1041 External_Flash_COM_Lost 1 " True" 0 "False" ;
VAL_ 1041 Battery_Temp_Fault_during_Slp 1 " True" 0 "False" ;
VAL_ 1041 Battery_Temp_Increase_flt_sleep 1 " True" 0 "False" ;
VAL_ 1041 BMS_3_3V_Fault_Not_Good 1 " True" 0 "False" ;
VAL_ 1041 PDU_Temperature_Rise_Detection_Sleep 1 " Fault" 0 "No fault" ;
VAL_ 1041 PDU_Temperature_Rise_Detection 1 " Fault" 0 "No fault" ;
VAL_ 1041 PDU_Temperature_Error_during_Sleep 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_Pack_Temperature_Rise_Sleep 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_Pack_Temperature_Error_Sleep 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_Temperature_Blanket_Cut_off_Grade4 1 " Fault" 0 "No fault" ;
VAL_ 1041 Overcharge_Protection 1 " Fault" 0 "No fault" ;
VAL_ 1041 Charger_ReAuth_Warning 1 " Fault" 0 "No fault" ;
VAL_ 1041 Charger_ReAuth_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 Cell_Voltage_Rise_Deration_in_HC 1 " Fault" 0 "No fault" ;
VAL_ 1041 Thermal_runaway_fault 1 " Fault" 0 "No fault" ;
VAL_ 1041 Thermal_runaway_shadow_fault 1 " Fault" 0 "No fault" ;
VAL_ 1041 Cell_Voltage_Rise_Deration_in_HC_1P5C 1 " Fault" 0 "No fault" ;
VAL_ 1041 Freq_Fault_Trigger 1 " Fault" 0 "No fault" ;
VAL_ 1041 PDU_TRNED_DRIVE 1 " Fault" 0 "No fault" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_1 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_2 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_3 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_4 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_5 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_6 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_7 1 "O" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_8 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_9 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_10 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_11 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_12 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_13 1 "ON" 0 "OFF" ;
VAL_ 368 BMS_Balancing_Status_Battery_Cell_14 1 "ON" 0 "OFF" ;
VAL_ 1746 Available_Drive_modes 5 "Reserved" 6 "COAST_MODE_AVAILABLE" 4 "SERVICE_MODE" 3 " NO_MODES_AVAILABLE" 2 " NOR_SPO_DIY_NOT_AVAILABLE" 1 "SPO_DIY_NOT_AVAILABLE" 0 " ALL_MODES_AVAILABLE" ;
VAL_ 1617 Battery_Cell_Type 7 " Reserved3" 6 " Reserved2," 5 " Reserved1," 4 " PTLLFP," 3 " StoreDot," 2 " BAK," 1 " LG," 0 " SAMSUNG, " ;
VAL_ 820 PWMMissingFault 1 "Fault" 0 "No Fault" ;
VAL_ 820 ZIPulse_Missing_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 820 AB_Pulse_Missing_Num 0 "Default" ;
VAL_ 820 AB_Pulse_Mism_ZIPulse_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 820 Pos_Cnt_Mism_PWM_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 2022 BMS_Low_Voltage_Detect_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Loss_of_Clock_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Loss_of_Lock_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_CMU_Lossof_Clock_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Watchdog 1 "True" 0 "False" ;
VAL_ 2022 BMS_External_Reset_Pin 1 "True" 0 "False" ;
VAL_ 2022 BMS_Power_On_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_JTAG_generated_reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Core_Lockup 1 "True" 0 "False" ;
VAL_ 2022 BMS_Software 1 "True" 0 "False" ;
VAL_ 2022 BMS_MDM_AP_System_Reset_Request 1 "True" 0 "False" ;
VAL_ 2022 BMS_Stop_Acknowledge_Error 1 "True" 0 "False" ;
VAL_ 1590 BMS_Bootloader_Info 12 " BOOT_STATUS_FW_FAIL" 11 " BOOT_STATUS_ROLL_BACK" 10 " BOOT_STATUS_RELOAD_EFLASH" 9 " BOOT_STATUS_CRC_FAIL_VERIFY" 8 " BOOT_STATUS_COPY_FAIL_EF_TO_IF" 7 " BOOT_STATUS_EFS_INIT_FAIL" 6 " BOOT_STATUS_IFS_READ_FAIL" 5 " BOOT_STATUS_IFS_INIT_FAIL" 4 " BOOT_STATUS_WDOG_INIT_FAIL" 3 " BOOT_STAUS_LOAD_EXTERNAL_WDG" 2 " BOOT_STATUS_SUCCESS" 1 " BOOT_STATUS_LOAD" 0 " BOOT_STATUS_IDLE " ;
VAL_ 1328 Vehicle_Alive 1 " EV Alive" 0 " SNA" ;
VAL_ 1329 Fast_Charger_Alive 1 " FC Alive" 0 " SNA" ;
VAL_ 1330 Request_to_Unlatch 1 " Request to Unlatch" 0 " SNA" ;
VAL_ 1282 EV_Voltage_Control 1 "Voltage Control Enabled" 0 "No Voltage control" ;
VAL_ 1296 Voltage_Control_Option 1 "Voltage control Enabled" 0 "No voltage control " ;
VAL_ 1288 Charging_system_error 1 " Error" 0 "Error Free" ;
VAL_ 1288 EV_supply_equipment_malfunction 1 " Error" 0 "Error Free" ;
VAL_ 1288 EV_incompatibility 1 " Incompatible" 0 "compatible" ;
VAL_ 1288 EV_supply_equipment_stop_control 1 " Shutdown or stop charging" 0 "Operating" ;
VAL_ 1288 EV_supply_equipment_status 1 " charging" 0 "Standby" ;
VAL_ 1288 Vehicle_connector_latched 1 " Latched" 0 "Unlatched" ;
VAL_ 1288 EV_supply_equipment_ready 1 "Ready" 0 "Not Ready" ;
VAL_ 1288 Waiting_state_before_charging_start 1 "Energy Transfer" 0 "Waiting" ;
VAL_ 1288 EVSE_Emergency_Stop 1 " True" 0 " False" ;
VAL_ 129 BMS_Auth_Response_to_SC_ 1 "Successful" 0 "Unsuccessful" ;
VAL_ 11 Battery_Wakeup_Request 1 " Wakeup" 0 " Sleep" ;
VAL_ 11 Firmware_version 1 " V1.4.5" 0 " Default" ;
VAL_ 11 Hardware_version 3 " PIC/GD for Destination Charger" 2 " PCS201 with GD (GD32F103xx)" 1 " PCS201 with PIC (PIC18F66K80)" 0 " Default" ;
VAL_ 11 Manufacturer_Code 2 " OLA" 1 " Steady Load" 0 " Default" ;
VAL_ 11 Bootloader_version 1 " V1.0" 0 " Default" ;
VAL_ 531 Charger_Status_Signal 1 "Ready" 0 "Not Ready" ;
VAL_ 531 Charger_Mode 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 531 Slow_Charger_Fault_Information 3 "Reserved" 2 "Reserved" 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_output_Short_circuit_error 1 "Error" 0 " No Error" ;
VAL_ 531 Charger_under_temperature_error 1 "Error" 0 " No Error" ;
VAL_ 531 Charger_over_temperature_error 1 "Error" 0 " No Error" ;
VAL_ 531 Low_AC_voltage_error 1 "Error" 0 " No Error" ;
VAL_ 531 Rectifier_hardware_error 1 "Error" 0 " No Error" ;
VAL_ 531 Authentication_Error 1 "Error" 0 " No Error" ;
VAL_ 531 Battery_Parameter_Time_out_error 1 "Error" 0 " No Error" ;
VAL_ 531 Data_Corruption_error 1 "Error" 0 " No Error" ;
VAL_ 531 Charge_control_message_timeout_error 1 "Error" 0 " No Error" ;
VAL_ 531 BoostCharger_billing_status 1 "billing required" 0 "billing not required" ;
VAL_ 1651 BMS_Bool_Debug3 1 " True" 0 " False" ;
VAL_ 1651 BMS_Bool_Debug4 1 " True" 0 " False" ;
VAL_ 1600 BMS00_Short_circuit_detection_error 1 "Fault" 0 "No Fault" ;
VAL_ 1600 BMS00_Precharge_too_fast_Info_10ms 1 "Fault" 0 "No Fault" ;
VAL_ 1600 BMS00_Precharge_too_slow_Info_10ms 1 "Fault" 0 "No Fault" ;
VAL_ 1600 BMS00_Precharge_failure_10ms 1 "Fault" 0 "No Fault" ;
VAL_ 1600 BMS00_MOSFETs_connection_failed_10ms 1 "Fault" 0 "No Fault" ;
VAL_ 1600 BMS00_MOSFETs_disconnection_failed_10ms 1 "Fault" 0 "No Fault" ;
VAL_ 1600 Reserved 1 "Fault" 0 "No Fault" ;
VAL_ 1600 Reserved1 1 "Fault" 0 "No Fault" ;
VAL_ 1600 Fault_status_1_00 1 "Fault" 0 "No Fault" ;
VAL_ 1600 Fault_status_2_00 1 "Fault" 0 "No Fault" ;
VAL_ 1600 Fault_status_3_00 1 "Fault" 0 "No Fault" ;
VAL_ 1600 AFE_FAULT_STATUS_00 1 "Fault" 0 "No Fault" ;
VAL_ 1600 EEPROM_Status_00 3 " EE_UNSUCCESSFUL" 2 " EE_COMPROMISED When the data was restored but is old" 1 " EE_SUCCESSFUL When the restoration was successful" 0 " EE_PENDING When no action has yet been taken to restore the data from NVM" ;
VAL_ 1600 Precharge_Switch_demand_00 1 "Enable" 0 "Disable" ;
VAL_ 1600 Charge_Switch_demand_00 1 "Enable" 0 "Disable" ;
VAL_ 1600 Discharge_Switch_demand_00 1 "Enable" 0 "Disable" ;
VAL_ 1600 SBC_LIMP_Status_00 1 "ON" 0 "OFF" ;
VAL_ 1600 Short_Circuit_Permanent_Precharge_Fault 1 "Fault" 0 "No Fault" ;


from dotenv import load_dotenv
load_dotenv() # Load environment variables from .env file
from loguru import logger
import os
import time
from src.queue_manager.core import QueueManager
from src.queue_manager.utils import next_queue_number
from src.queue_manager.interfaces.drive import authenticate_drive, list_files_in_folder

# Import Downloader
from runners.downloader import download_pending_files
# Logging setup
LOG_DIR = os.path.join(os.getcwd(), 'logs', 'watcher')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

# Configurations
DRIVE_BLF_FOLDER_ID = '1BFbahFfHlndjov7TYVM5yvU8r5reMEje'

# Helper to make Windows-safe filenames
ILLEGAL_CHARS = [':', '<', '>', '"', '/', '\\', '|', '?', '*']
def make_windows_safe(filename):
    for ch in ILLEGAL_CHARS:
        filename = filename.replace(ch, '_')
    return filename

# Helper: Retry decorator for network/Google API errors
import functools
from googleapiclient.errors import HttpError
import socket

def retry_on_network_errors(max_retries=3, delay=30):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(1, max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (HttpError, ConnectionAbortedError, ConnectionResetError, socket.error, OSError) as e:
                    logger.warning(f"Network/API error in {func.__name__}: {e}. Attempt {attempt}/{max_retries}.")
                    if attempt == max_retries:
                        logger.error(f"Max retries reached for {func.__name__}. Giving up this cycle.")
                        return [] if 'list' in func.__name__ else None
                    time.sleep(delay)
        return wrapper
    return decorator

@retry_on_network_errors(max_retries=3, delay=30)
def list_files_in_folder_latest(service, folder_id: str):
    files = []
    page_token = None
    while True:
        try:
            response = service.files().list(
                q=f"'{folder_id}' in parents",
                spaces='drive',
                fields='nextPageToken, files(id, name, mimeType, modifiedTime)',
                orderBy='modifiedTime desc',
                pageToken=page_token
            ).execute()
        except Exception as e:
            logger.warning(f"Exception in list_files_in_folder_latest: {e}")
            raise
        files.extend(response.get('files', []))
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    return files

@retry_on_network_errors(max_retries=3, delay=30)
def list_all_blf_files(drive_service, parent_folder_id, parent_path=''):
    files = []
    try:
        items = list_files_in_folder_latest(drive_service, parent_folder_id)
    except Exception as e:
        logger.warning(f"Exception in list_all_blf_files: {e}")
        return []
    for item in items:
        if item.get('mimeType') == 'application/vnd.google-apps.folder':
            subfolder_name = item['name']
            subfolder_id = item['id']
            files.extend(list_all_blf_files(
                drive_service, subfolder_id, os.path.join(parent_path, subfolder_name)
            ))
        elif item['name'].lower().endswith('.blf'):
            files.append({
                'id': item['id'],
                'name': item['name'],
                'path': os.path.join(parent_path, item['name'])
            })
    return files

def poll_and_update_queue():
    queue = QueueManager()
    while True:
        try:
            drive_service = None
            # Retry authentication if it fails
            for attempt in range(1, 4):
                try:
                    drive_service = authenticate_drive()
                    break
                except Exception as e:
                    logger.warning(f"Drive authentication failed: {e}. Attempt {attempt}/3.")
                    if attempt == 3:
                        logger.error("Max authentication retries reached. Sleeping before next cycle.")
                        time.sleep(60)
                        continue
                    time.sleep(30)
            if drive_service is None:
                continue
            blf_files = list_all_blf_files(drive_service, DRIVE_BLF_FOLDER_ID)
            drive_set = {(f['name'], os.path.dirname(f['path'])) for f in blf_files}
            db_set = {(row['drive_filename'], row['subfolder']) for row in queue.get_all()}
            # Add new files to DB
            for file in blf_files:
                subfolder_path = os.path.dirname(file['path'])
                if subfolder_path.startswith('BLF_FILES'):
                    subfolder = subfolder_path[len('BLF_FILES'):].lstrip(os.sep)
                else:
                    subfolder = subfolder_path
                key = (file['name'], subfolder)
                if key not in db_set:
                    queue_num = next_queue_number([row['queue_number'] for row in queue.get_all()])
                    queue.add_file(
                        drive_filename=file['name'],
                        blf_link=file['id'],
                        queue_number=queue_num,
                        subfolder=subfolder,
                        local_filename=make_windows_safe(file['name'])
                    )
                    logger.info(f"Added new file to queue: {file['name']} in {subfolder}")
            # Optionally, mark files as removed if they are in DB but not on Drive
            for db_file in queue.get_all():
                key = (db_file['drive_filename'], db_file['subfolder'])
                local_blf_dir = os.path.join(os.getcwd(), 'blf', db_file['subfolder']) if db_file['subfolder'] else os.path.join(os.getcwd(), 'blf')
                local_blf_path = os.path.join(local_blf_dir, db_file['local_filename'])
                if db_file.get('downloaded', 0) == 1 and not os.path.exists(local_blf_path):
                    logger.warning(f"File marked as downloaded in DB but missing locally: {local_blf_path}. Resetting downloaded flag for item: {db_file.get('drive_filename')}.")
                    queue.set_downloaded(db_file, False) # Pass the whole db_file item
                if key not in drive_set:
                    # This part of the logic might need further review:
                    # If a file is in DB but not on Drive, what should happen?
                    # For now, just logging. Consider if status should be updated (e.g., 'removed_from_drive')
                    logger.info(f"File present in DB but not found on Drive: {db_file['drive_filename']} in {db_file['subfolder']}. No action taken by watcher for this.")
            logger.info("Polling complete. Sleeping...")
            # Trigger download of pending files every cycle
            download_pending_files()
        except Exception as e:
            logger.error(f"Unhandled exception in poll_and_update_queue: {e}", exc_info=True)
            logger.info("Sleeping 60 seconds before retrying main loop.")
            time.sleep(60)
        # Sleep for 60 seconds
        time.sleep(60)

if __name__ == '__main__':
    poll_and_update_queue() 
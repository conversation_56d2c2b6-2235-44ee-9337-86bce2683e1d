from loguru import logger
import sqlite3
from typing import Optional, List, Dict, Any
import os
from datetime import datetime
from googleapiclient.errors import HttpError

# Assuming interfaces.sheets is in the same directory or adjust path
try:
    from .interfaces.sheets import authenticate_sheets, update_single_item_in_sheet
except ImportError:
    # Fallback for direct execution or different project structure
    from interfaces.sheets import authenticate_sheets, update_single_item_in_sheet


# Logging setup
LOG_DIR_QM = "logs/queue_manager"
os.makedirs(LOG_DIR_QM, exist_ok=True)
logger.remove()
logger.add("logs/queue_manager/info.log", level="INFO", rotation="1 day")
logger.add("logs/queue_manager/debug.log", level="DEBUG", rotation="1 day")
logger.add(lambda msg: print(msg, end=""), level="INFO")  # Also log to stdout

CWD = os.getcwd()
# DB Folder Exists True
DB_FOLDER = os.path.join(CWD, 'db')
if not os.path.exists(DB_FOLDER):
    os.makedirs(DB_FOLDER)
DB_PATH = os.path.join(DB_FOLDER, 'queue.db')

QUEUE_SCHEMA = """
CREATE TABLE IF NOT EXISTS queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    drive_filename TEXT NOT NULL,
    local_filename TEXT NOT NULL,
    blf_link TEXT,
    converted_link TEXT,
    queue_number INTEGER,
    queue_status TEXT,
    downloaded INTEGER DEFAULT 0,
    subfolder TEXT,
    converted INTEGER DEFAULT 0,
    uploaded INTEGER DEFAULT 0,
    UNIQUE(drive_filename, subfolder)
);
"""

class QueueManager:
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or DB_PATH
        logger.info(f"Using queue DB at: {os.path.abspath(self.db_path)}")
        self._init_db()
        self.sheets_service = None
        try:
            self.sheets_service = authenticate_sheets()
            logger.info("Google Sheets service authenticated successfully for QueueManager.")
        except FileNotFoundError as fnf_error:
            logger.error(f"Failed to authenticate Google Sheets due to missing file: {fnf_error}. Sheet updates will be disabled.")
        except HttpError as http_err:
            logger.error(f"Failed to authenticate Google Sheets due to API error: {http_err}. Sheet updates will be disabled.")
        except Exception as e:
            logger.error(f"An unexpected error occurred during Google Sheets authentication: {e}. Sheet updates will be disabled.")
        logger.info(f"QueueManager initialized with DB: {self.db_path}")

    def _init_db(self):
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(QUEUE_SCHEMA)
            conn.commit()
        logger.info("Queue table ensured in database.")

    def _prepare_data_for_sheet(self, item_dict: Optional[Dict[str, Any]]) -> List[Any]:
        if not item_dict:
            return []
        return [
            item_dict.get('drive_filename', ''),
            item_dict.get('blf_link', ''),
            item_dict.get('converted_link', ''),
            str(item_dict.get('queue_number', '')),
            item_dict.get('queue_status', ''),
            datetime.now().isoformat()  # For "Last Updated (Sheet)"
        ]

    def _update_sheet_for_item(self, blf_link: Optional[str], item_details: Optional[Dict[str, Any]]):
        if not self.sheets_service:
            logger.warning("Sheets service not available. Skipping sheet update.")
            return
        if not blf_link:
            logger.warning("BLF link not provided. Skipping sheet update.")
            return
        if not item_details:
            logger.warning(f"Item details not found for blf_link {blf_link}. Skipping sheet update.")
            return

        sheet_row_data = self._prepare_data_for_sheet(item_details)
        if sheet_row_data:
            try:
                logger.debug(f"Attempting to update sheet for blf_link: {blf_link} with data: {sheet_row_data}")
                update_single_item_in_sheet(self.sheets_service, blf_link, sheet_row_data)
                logger.info(f"Successfully triggered sheet update for blf_link: {blf_link}")
            except HttpError as e:
                logger.error(f"Google Sheets API error updating item {blf_link}: {e}")
            except Exception as e:
                logger.error(f"Unexpected error updating sheet for item {blf_link}: {e}")
        else:
            logger.warning(f"No data prepared for sheet update for blf_link: {blf_link}")
            
    def get_item(self, drive_filename: str, subfolder: str) -> Optional[Dict[str, Any]]:
        """Fetches a single item from the queue by drive_filename and subfolder."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row # To get results as dict-like objects
            cursor = conn.execute(
                "SELECT * FROM queue WHERE drive_filename = ? AND subfolder = ?",
                (drive_filename, subfolder)
            )
            row = cursor.fetchone()
            if row:
                return dict(row)
        return None

    def add_file(self, drive_filename: str, blf_link: str, queue_number: int, subfolder: str, local_filename: str, queue_status: str = 'pending'):
        item_added = False
        with sqlite3.connect(self.db_path) as conn:
            try:
                conn.execute(
                    "INSERT INTO queue (drive_filename, local_filename, blf_link, queue_number, queue_status, downloaded, subfolder) VALUES (?, ?, ?, ?, ?, 0, ?)",
                    (drive_filename, local_filename, blf_link, queue_number, queue_status, subfolder)
                )
                conn.commit()
                logger.info(f"Added file to queue: {drive_filename} ({subfolder})")
                item_added = True
            except sqlite3.IntegrityError:
                logger.warning(f"File already in queue or unique constraint failed: {drive_filename} ({subfolder})")
            except Exception as e:
                logger.error(f"Unexpected error adding file to queue: {e}")
                # Not raising here to allow potential sheet update if item already existed and we want to sync
        
        if item_added: # Only update sheet if item was newly added
            # Fetch the newly added item to get all its details for the sheet
            # The blf_link is passed in, which is the key for sheet updates.
            # We need all fields for _prepare_data_for_sheet
            newly_added_item_details = {
                "drive_filename": drive_filename,
                "local_filename": local_filename,
                "blf_link": blf_link,
                "queue_number": queue_number,
                "queue_status": queue_status,
                "downloaded": 0, # Default for new files
                "subfolder": subfolder,
                "converted": 0, # Default
                "uploaded": 0, # Default
                "converted_link": None # Default
            }
            self._update_sheet_for_item(blf_link, newly_added_item_details)


    def update_status(self, item_details: Dict[str, Any], status: str,
                      converted_link: Optional[str] = None,
                      uploaded_drive_link: Optional[str] = None):
        """
        Updates the status of an item in the database and then triggers a sheet update.
        Relies on the passed item_details for sheet update information, especially blf_link.
        If status is 'uploaded', uploaded_drive_link will be used to update 'converted_link'.
        """
        if not item_details or not item_details.get('drive_filename') or item_details.get('subfolder') is None: # subfolder can be empty string
            logger.error("Insufficient item details (drive_filename, subfolder) provided to update_status.")
            return

        drive_filename = item_details['drive_filename']
        subfolder = item_details['subfolder']
        db_update_successful = False
        
        item_details_for_sheet = item_details.copy() # Work on a copy
        item_details_for_sheet['queue_status'] = status

        updates_list = ["queue_status = ?"]
        params_list = [status]

        if status == 'converted':
            updates_list.append("converted = 1")
            item_details_for_sheet['converted'] = 1
            if converted_link is not None: # This is the local CSV path
                updates_list.append("converted_link = ?")
                params_list.append(converted_link)
                item_details_for_sheet['converted_link'] = converted_link
            else:
                item_details_for_sheet['converted_link'] = None # Ensure it's None if not provided
        
        elif status == 'uploaded':
            updates_list.append("uploaded = 1")
            item_details_for_sheet['uploaded'] = 1
            if uploaded_drive_link is not None: # This is the Google Drive link/ID
                updates_list.append("converted_link = ?") # Update converted_link to the Drive URL
                params_list.append(uploaded_drive_link)
                item_details_for_sheet['converted_link'] = uploaded_drive_link
            # If uploaded_drive_link is None when status is 'uploaded',
            # converted_link in DB remains as it was (local path), which might be okay
            # or you might want to explicitly set it to None or an error placeholder.
            # For now, it only updates if uploaded_drive_link is provided.

        # General case for local converted_link if status is not 'converted' or 'uploaded'
        # but a converted_link (local path) is still being provided.
        if converted_link is not None and status not in ['converted', 'uploaded']:
            if "converted_link = ?" not in updates_list: # Avoid adding if already set by 'converted' logic
                 updates_list.append("converted_link = ?")
                 params_list.append(converted_link)
            item_details_for_sheet['converted_link'] = converted_link


        sql_query = f"UPDATE queue SET {', '.join(updates_list)} WHERE drive_filename = ? AND subfolder = ?"
        params_list.extend([drive_filename, subfolder])
        
        logger.debug(f"Executing SQL for update_status: {sql_query} with params: {params_list}")

        with sqlite3.connect(self.db_path) as conn:
            try:
                conn.execute(sql_query, tuple(params_list))
                conn.commit()
                logger.info(f"DB status updated for {drive_filename} ({subfolder}) to {status}. ConvertedLink: {converted_link if converted_link else 'N/A'}")
                db_update_successful = True
            except sqlite3.Error as e:
                logger.error(f"SQLite error updating status for {drive_filename} ({subfolder}): {e}")

        if db_update_successful:
            blf_link_for_sheet = item_details_for_sheet.get('blf_link')
            if blf_link_for_sheet:
                self._update_sheet_for_item(blf_link_for_sheet, item_details_for_sheet)
            else:
                logger.warning(f"blf_link not found in provided item_details for {drive_filename} ({subfolder}). Cannot update sheet by blf_link.")
        else:
            logger.warning(f"DB update failed for {drive_filename} ({subfolder}). Sheet update skipped.")


    def set_downloaded(self, item_details: Dict[str, Any], downloaded: bool = True):
        """
        Sets the downloaded flag for an item in the database and triggers a sheet update.
        Relies on the passed item_details for sheet update information.
        """
        if not item_details or not item_details.get('drive_filename') or item_details.get('subfolder') is None:
            logger.error("Insufficient item details (drive_filename, subfolder) provided to set_downloaded.")
            return

        drive_filename = item_details['drive_filename']
        subfolder = item_details['subfolder']
        db_update_successful = False
        
        item_details_for_sheet = item_details.copy() # Work on a copy
        downloaded_int = 1 if downloaded else 0
        item_details_for_sheet['downloaded'] = downloaded_int
        # Note: set_downloaded typically doesn't change queue_status directly.
        # If a status change is implied (e.g., to 'downloaded_complete'),
        # it should be handled by calling update_status separately or by enhancing this method.
        # For now, we assume queue_status in item_details_for_sheet is the one to report.

        with sqlite3.connect(self.db_path) as conn:
            try:
                conn.execute(
                    "UPDATE queue SET downloaded = ? WHERE drive_filename = ? AND subfolder = ?",
                    (downloaded_int, drive_filename, subfolder)
                )
                conn.commit()
                logger.info(f"DB downloaded flag set to {downloaded_int} for {drive_filename} ({subfolder})")
                db_update_successful = True
            except sqlite3.Error as e:
                 logger.error(f"SQLite error setting downloaded for {drive_filename} ({subfolder}): {e}")

        if db_update_successful:
            blf_link_for_sheet = item_details_for_sheet.get('blf_link')
            if blf_link_for_sheet:
                self._update_sheet_for_item(blf_link_for_sheet, item_details_for_sheet)
            else:
                logger.warning(f"blf_link not found in provided item_details for {drive_filename} ({subfolder}) after set_downloaded. Cannot update sheet by blf_link.")
        else:
            logger.warning(f"DB update for downloaded flag failed for {drive_filename} ({subfolder}). Sheet update skipped.")


    def get_pending_files(self) -> List[Dict[str, Any]]:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM queue WHERE queue_status = 'pending' AND downloaded = 1 ORDER BY queue_number ASC"
            )
            files = [dict(zip([column[0] for column in cursor.description], row)) for row in cursor.fetchall()]
        logger.info(f"Fetched {len(files)} pending files from queue.")
        return files

    def get_files_to_download(self) -> List[Dict]:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM queue WHERE downloaded = 0 ORDER BY queue_number ASC"
            )
            files = [dict(zip([column[0] for column in cursor.description], row)) for row in cursor.fetchall()]
        logger.info(f"Fetched {len(files)} files to download from queue.")
        return files

    def get_files_to_convert(self) -> List[Dict]:
        """Return files that are pending conversion (downloaded=1, converted=0, status pending)."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM queue WHERE queue_status = 'pending' AND downloaded = 1 AND converted = 0 ORDER BY queue_number ASC"
            )
            files = [dict(zip([column[0] for column in cursor.description], row)) for row in cursor.fetchall()]
        logger.info(f"Fetched {len(files)} files to convert from queue.")
        return files

    def get_all(self) -> List[Dict]:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT * FROM queue ORDER BY queue_number ASC")
            files = [dict(zip([column[0] for column in cursor.description], row)) for row in cursor.fetchall()]
        return files

    def get_converted_not_uploaded(self) -> List[Dict]:
        """Return files that are converted but not yet uploaded (converted=1, uploaded=0)."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM queue WHERE converted = 1 AND uploaded = 0 ORDER BY queue_number ASC"
            )
            files = [dict(zip([column[0] for column in cursor.description], row)) for row in cursor.fetchall()]
        logger.info(f"Fetched {len(files)} converted but not uploaded files from queue.")
        return files
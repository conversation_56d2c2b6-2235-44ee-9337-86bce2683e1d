import logging
from typing import List, Dict

logging.basicConfig(level=logging.INFO)

def extract_blf_files(drive_files: List[Dict]) -> List[Dict]:
    """Extract BLF file info from Google Drive API results."""
    blf_files = [f for f in drive_files if f['name'].lower().endswith('.blf')]
    logging.info(f"Extracted {len(blf_files)} BLF files from Drive results.")
    return blf_files

def is_duplicate(file_name: str, existing_files: List[str]) -> bool:
    """Check if a file is already in the queue or local list."""
    duplicate = file_name in existing_files
    if duplicate:
        logging.info(f"Duplicate detected: {file_name}")
    return duplicate 
' convert_single.vbs
' Usage: cscript convert_single.vbs --config-path <path> --blf-path <path> --output <path>

WScript.Echo "Starting conversion script..."

Dim configPath, blfPath, outputPath
Dim i

' Add this function at the top after variable declarations
Function StripQuotes(s)
    If Left(s, 1) = Chr(34) And Right(s, 1) = Chr(34) Then
        StripQuotes = Mid(s, 2, Len(s) - 2)
    Else
        StripQuotes = s
    End If
End Function

For i = 0 To WScript.Arguments.Count - 1
    If LCase(WScript.Arguments(i)) = "--config-path" Then
        configPath = WScript.Arguments(i + 1)
    ElseIf LCase(WScript.Arguments(i)) = "--blf-path" Then
        blfPath = WScript.Arguments(i + 1)
    ElseIf LCase(WScript.Arguments(i)) = "--output" Then
        outputPath = WScript.Arguments(i + 1)
    End If
Next
' Print Before Normalization
WScript.Echo "Paths: Config=" & configPath & ", BLF=" & blfPath & ", Output=" & outputPath

' After parsing arguments
configPath = StripQuotes(configPath)
blfPath = StripQuotes(blfPath)
outputPath = StripQuotes(outputPath)

If IsEmpty(configPath) Or IsEmpty(blfPath) Or IsEmpty(outputPath) Then
    WScript.Echo "Usage: cscript convert_single.vbs --config-path <path> --blf-path <path> --output <path>"
    WScript.Quit 1
End If

WScript.Echo "Arguments parsed: Config=" & configPath & ", BLF=" & blfPath & ", Output=" & outputPath

Set WshShell = CreateObject("WScript.Shell")
Set WMI = GetObject("winmgmts://./root/cimv2")
Set ProcessList = WMI.ExecQuery("SELECT * FROM Win32_Process WHERE Name='CANoe64.exe'")
Dim appFound
appFound = False

For Each Process In ProcessList
    On Error Resume Next
    Set App = GetObject(, "CANoe.Application." & Process.ProcessId)
    If Err.Number = 0 Then
        If Not App.Measurement.Running Then
            appFound = True
            Exit For
        End If
    End If
    On Error GoTo 0
Next

If Not appFound Then
    On Error Resume Next
    Set App = CreateObject("CANoe.Application")
    If Err.Number <> 0 Then
        WScript.Echo "No available CANoe instances found."
        WScript.Quit 2
    End If
    On Error GoTo 0
End If

WScript.Echo "CANoe instance attached or created."

App.Open configPath
WScript.Echo "Opened Config: " & configPath
WScript.Sleep 500

If App.Measurement.Running Then
    WScript.Echo "Stopping running measurement..."
    App.Measurement.Stop
End If

' Set up exporter for single BLF file
WScript.Echo "Setting up exporter for BLF to output conversion..."
' Use correct constant: 0 for ASCII, 1 for BLF, 2 for Signal
Set Logging = App.Configuration.OnlineSetup.LoggingCollection(1)
Set Exporter = Logging.Exporter

With Exporter.Sources
    .Clear
    .Add(blfPath)
End With

' Load file
Exporter.Load

With Exporter.Destinations
    .Clear
    .Add(outputPath)
End With

WScript.Echo "Starting export..."
Exporter.Save True

WScript.Echo "Converted: " & blfPath & " -> " & outputPath

' Signal completion
WScript.Echo "Conversion completed successfully."

' Cleanup
' Close the configuration to leave CANoe in a clean state for the next run
On Error Resume Next
App.Configuration.Close
On Error GoTo 0

' Release WshShell object
Set WshShell = Nothing

' Explicitly quit the script to ensure it terminates
WScript.Quit 0

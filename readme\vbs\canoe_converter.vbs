Set fso = CreateObject("Scripting.FileSystemObject")
currentFolder = fso.GetAbsolutePathName(".")
blfFolder = currentFolder & "\BLF"
csvFolder = currentFolder & "\CSV"
configFolder = currentFolder & "\CONFIG"
configFile = currentFolder & "\CONFIG\config.cfg"

' Delete all bat files in the current folder
Set fso = CreateObject("Scripting.FileSystemObject")
Set current_folder = fso.GetFolder(currentFolder)
For Each bat_file In current_folder.Files
    If LCase(fso.GetExtensionName(bat_file.Name)) = "bat" Then
        fso.DeleteFile currentFolder & "\" & bat_file.Name
    End If
Next
' Delete all instances in the BLF folder which have instance-* folders
Set fso = CreateObject("Scripting.FileSystemObject")
Set blf_folder = fso.GetFolder(blfFolder)
For Each instance_folder In blf_folder.SubFolders
    If Left(instance_folder.Name, 9) = "instance-" Then
        fso.DeleteFolder blfFolder & "\" & instance_folder.Name, True
    End If
Next

' Clear Env
instanceCount = InputBox("Enter the number of instances needed", "Instance Count", 1)
Set WMI = GetObject("winmgmts://./root/cimv2")
Set ProcessList = WMI.ExecQuery("SELECT * FROM Win32_Process WHERE Name='CANoe64.exe'")
' Store them in an array
Dim processArray()
ReDim processArray(0)
For Each Process In ProcessList
    ReDim Preserve processArray(UBound(processArray) + 1)
    processArray(UBound(processArray)) = Process.ProcessId
    ' WScript.Echo Process.ProcessId & " " & Process.CommandLine
Next
' Delete all config files in the config folder which have config-*.cfg
Set fso = CreateObject("Scripting.FileSystemObject")
Set config_folder = fso.GetFolder(configFolder)
For Each config_file In config_folder.Files
    If LCase(fso.GetExtensionName(config_file.Name)) = "cfg" Then
        If Left(config_file.Name, 7) = "config-" Then
            fso.DeleteFile configFolder & "\" & config_file.Name
        End If
    End If
Next
'delete all .stcfg in a loop from the config folder
' For Each config_file In config_folder.Files
'     If LCase(fso.GetExtensionName(config_file.Name)) = "stcfg" Then
'         fso.DeleteFile configFolder & "\" & config_file.Name
'     End If
' Next

' End Clear Env
' Create the instance folders in BLF and make instance-{i} directories and move .blf files to them equally
For i = 1 To instanceCount
    If Not fso.FolderExists(blfFolder & "\instance-" & i) Then
        fso.CreateFolder(blfFolder & "\instance-" & i)
    End If
    ' make copies of config.cfg for each instance in the configFolder if it does not exist
    If Not fso.FileExists(configFolder & "\config-" & i & ".cfg") Then
        fso.CopyFile configFile, configFolder & "\config-" & i & ".cfg"
    End If
Next

' Move the .blf files to the instance folders
Set blf_folder = fso.GetFolder(blfFolder)
fileCount = 0
For Each blf_file In blf_folder.Files
    If LCase(fso.GetExtensionName(blf_file.Name)) = "blf" Then
        fileCount = fileCount + 1
        instanceNumber = fileCount Mod instanceCount
        If instanceNumber = 0 Then
            instanceNumber = instanceCount
        End If
        fso.MoveFile blfFolder & "\" & blf_file.Name, blfFolder & "\instance-" & instanceNumber & "\" & blf_file.Name
    End If
Next

' Wait for the folders to be created
WScript.Sleep 500  ' Adjust the sleep time if needed

' Create the WshShell object for multi-threading
Set WshShell = CreateObject("WScript.Shell")

' Start the conversions in separate threads
For i = 1 To instanceCount
    instanceFolder = blfFolder & "\instance-" & i
    configPath = configFolder & "\config-" & i & ".cfg"
    ' configPath = configFolder & "\config.cfg"
    fullPathCSF = currentFolder & "\csf.vbs"
    ' MsgBox "Instance Folder: " & instanceFolder & vbCrLf & "Config Path: " & configPath & vbCrLf & "Full Path CSF: " & fullPathCSF
    ' Copy Command to Clipboard
    runCommand = "cscript.exe """ & fullPathCSF & """ """ & instanceFolder & """ """ & configPath & """ """ & csvFolder & """ """ & processArray(i) & """" & ",0,True"
    ' Place runCommand to input box to copy
    ' InputBox "Copy the command below and run it in command prompt", "Command", runCommand
    ' Run with full paths
    ' WshShell.Run runCommand, 0, True
    ' Create Run1.bat, Run2.bat... in the current folder with the command
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set runFile = fso.CreateTextFile(currentFolder & "\Run " & i & ".bat", True)
        runFile.WriteLine runCommand
    runFile.Close
Next
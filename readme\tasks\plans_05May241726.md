# Plan for convert_single.vbs and <PERSON> Script (05 May 24 17:26)

## Objective
- Implement a VBS script (convert_single.vbs) that takes command-line arguments:
  - --config-path <path>
  - --blf-path <path>
  - --csv-path <path>
- The script will be called from Python for future queuing system integration.
- Write a simple Python test script in tests/call_single_convert.py to invoke the VBS script.

## Steps
1. Analyze canoe_converter.vbs for relevant logic and conventions.
2. Design convert_single.vbs to parse command-line arguments and perform a single conversion using the provided paths.
3. Implement convert_single.vbs.
4. Write tests/call_single_convert.py to call convert_single.vbs with sample arguments and check for expected output.
5. Document the usage and update plans/tasks as progress is made. 
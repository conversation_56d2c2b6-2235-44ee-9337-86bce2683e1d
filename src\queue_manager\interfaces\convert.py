import subprocess
import logging
import os
from pathlib import Path

logging.basicConfig(level=logging.INFO)

CWD = os.getcwd()
VBS_PATH = os.path.join(CWD, 'convert_single.vbs')

def escape_path(path: str) -> str:
    abs_path = Path(path).resolve()
    return str(abs_path)

def run_conversion(config_path: str, blf_path: str, output_path: str) -> bool:
    vbs_path = escape_path(VBS_PATH)
    config_path = escape_path(config_path)
    blf_path = escape_path(blf_path)
    output_path = escape_path(output_path)
    cmd = [
        'cscript.exe',
        vbs_path,
        '--config-path', config_path,
        '--blf-path', blf_path,
        '--output', output_path
    ]
    logging.info(f"Running conversion: {' '.join(cmd)}")
    try:
        # Ensure the output directory exists before running the conversion
        # The VBScript might not create it.
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        logging.info(f"Conversion STDOUT: {result.stdout}")
        if result.stderr:
            logging.warning(f"Conversion STDERR: {result.stderr}")
        
        # Explicitly check if the output file was created
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0: # Check if file exists and is not empty
            logging.info(f"Output file {output_path} successfully created.")
            return True
        else:
            logging.error(f"Output file {output_path} not found or is empty after conversion script execution.")
            return False
            
    except subprocess.CalledProcessError as e:
        logging.error(f"Error running convert_single.vbs: {e}")
        logging.error(f"STDOUT: {e.stdout}")
        logging.error(f"STDERR: {e.stderr}")
        return False
    except Exception as e:
        logging.error(f"An unexpected error occurred during conversion: {e}")
        return False
import os
import pickle
import logging
from typing import List, Dict
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.http import MediaIoBaseDownload

SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/drive'
]
CWD = os.getcwd()
TOKEN_PATH = os.path.join(CWD, 'src/drive/token.pickle')
AUTH_PATH = os.path.join(CWD, 'src/drive/auth.json')

logging.basicConfig(level=logging.INFO)

def authenticate_drive():
    creds = None
    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH, 'rb') as token:
            creds = pickle.load(token)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(AUTH_PATH, SCOPES)
            creds = flow.run_local_server(port=0)
        with open(TOKEN_PATH, 'wb') as token:
            pickle.dump(creds, token)
    logging.info("Authenticated with Google Drive.")
    return build('drive', 'v3', credentials=creds)

def list_files_in_folder(service, folder_id: str) -> List[Dict]:
    files = []
    page_token = None
    while True:
        response = service.files().list(
            q=f"'{folder_id}' in parents and mimeType != 'application/vnd.google-apps.folder'",
            spaces='drive',
            fields='nextPageToken, files(id, name)',
            pageToken=page_token
        ).execute()
        files.extend(response.get('files', []))
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    logging.info(f"Listed {len(files)} files in folder {folder_id}.")
    return files

def download_file(service, file_id: str, destination_path: str):
    request = service.files().get_media(fileId=file_id)
    with open(destination_path, 'wb') as f:
        downloader = MediaIoBaseDownload(f, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
            logging.info(f"Download {int(status.progress() * 100)}% for {destination_path}")
    logging.info(f"Downloaded file to {destination_path}") 
# Plan for Queue Management and Conversion System (14 May 25 13:04)

## Objective
- Implement a Python-based queue management system to monitor Google Drive's BLF_FILES folder, update a local SQLite queue, and manage file conversion and status tracking.
- Integrate with Google Drive using scripts in @drive/ for authentication and file operations.
- Use convert_single.vbs (see tests/call_single_convert.py) for file conversion.
- Update a Google Sheet with the status of files every 15 minutes.

## Steps
1. Analyze @drive/download_csv.py and @drive/download_csv_date.py for Google Drive and Sheets integration patterns.
2. Design the local queue database schema (SQLite) to track file_name, blf_link, converted_link, queue_number, queue_status.
3. Implement a watcher script in src/queue_manager/ to:
   - Poll BLF_FILES on Drive every minute.
   - Compare with local hash/db and update queue for new files.
   - Assign queue numbers and update statuses.
4. Integrate conversion logic:
   - For each queued file, call convert_single.vbs using the pattern in tests/call_single_convert.py.
   - Move converted files to CSV_FILES and update queue/db.
5. Implement periodic (every 15 min) update of the Google Sheet (MAIN) with the current queue/db status.
6. Write tests for queue management, conversion, and Drive/Sheet integration.
7. Document all new modules, public APIs, and update project documentation.
8. Run ruff and pytest, fix issues, and ensure code quality before submission. 
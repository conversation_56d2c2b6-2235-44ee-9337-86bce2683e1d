# Tasks for Queue Management and Conversion System (14 May 25 13:04)

- [ ] Analyze @drive/download_csv.py and @drive/download_csv_date.py for Drive/Sheets integration
    - [ ] Previous logic might have to be tweeked for the blf download instead of csv
    - [ ] The logic must avoid duplication of the downloads
- [ ] Design SQLite schema for queue (file_name, blf_link, converted_link, queue_number, queue_status)
- [ ] Implement watcher script in src/queue_manager/ to poll BLF_FILES every minute
- [ ] Compare Drive BLF_FILES with local hash/db and update queue for new files
- [ ] Assign queue numbers and update statuses in db
- [ ] Integrate convert_single.vbs invocation (see tests/call_single_convert.py)
- [ ] Move converted files to CSV_FILES and update db
- [ ] Implement periodic (15 min) update of Google Sheet (MAIN) with queue/db status
- [ ] Write tests for queue management, conversion, and Drive/Sheet integration
- [ ] Document all new modules, public APIs, and update project documentation
- [ ] Run ruff and pytest, fix issues, and ensure code quality before submission 
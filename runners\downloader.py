from loguru import logger
import os
from src.queue_manager.core import QueueManager
from src.queue_manager.interfaces.drive import authenticate_drive, download_file

# Logging setup
LOG_DIR = os.path.join(os.getcwd(), 'logs', 'downloader')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

LOCAL_BLF_DIR = os.path.join(os.getcwd(), 'blf')

def get_unique_local_path(directory: str, original_filename: str) -> tuple[str, str]:
    """
    Generates a unique local path by appending a counter to the filename if needed.
    Returns the full unique local_path and the actual_filename used.
    """
    base, ext = os.path.splitext(original_filename)
    counter = 1
    actual_filename = original_filename
    local_path = os.path.join(directory, actual_filename)

    while os.path.exists(local_path):
        actual_filename = f"{base} ({counter}){ext}"
        local_path = os.path.join(directory, actual_filename)
        counter += 1
    return local_path, actual_filename

def download_pending_files():
    queue = QueueManager()
    drive_service = authenticate_drive()
    if not drive_service:
        logger.error("Failed to authenticate with Google Drive. Cannot proceed with downloads.")
        return

    files_to_download = queue.get_files_to_download()

    for item in files_to_download:
        drive_filename = item['drive_filename']
        original_local_filename = item['local_filename']  # Original name from DB
        subfolder = item['subfolder'] or ''
        blf_id = item['blf_link']

        local_dir = os.path.join(LOCAL_BLF_DIR, subfolder)
        os.makedirs(local_dir, exist_ok=True)

        actual_local_path, actual_local_filename = get_unique_local_path(local_dir, original_local_filename)

        # Case 1: File with the determined unique name (which might be the original name) already exists.
        # This implies it was likely downloaded correctly before, or it's a collision we've now resolved.
        if os.path.exists(actual_local_path) and actual_local_filename == original_local_filename:
            logger.info(f"File '{original_local_filename}' already exists at '{actual_local_path}'. Ensuring DB is updated.")
            # Call new method to ensure local_filename in DB is correct and status is set.
            queue.set_downloaded_and_update_local_name(item, True, actual_local_filename)
            continue
        
        # If actual_local_filename is different from original_local_filename,
        # it means a new name was generated due to a collision. We will download to this new name.
        # If actual_local_filename is the same as original_local_filename and it didn't exist, we download.

        logger.info(f"Attempting to download {drive_filename} (Drive ID: {blf_id}) to {actual_local_path} (original: {original_local_filename})")
        try:
            download_file(drive_service, blf_id, actual_local_path)
            logger.info(f"Successfully downloaded {drive_filename} to {actual_local_path}")
            # Update DB with downloaded status and the actual local filename used
            queue.set_downloaded_and_update_local_name(item, True, actual_local_filename)
        except Exception as e:
            logger.error(f"Failed to download {drive_filename} to {actual_local_path}: {e}")
            # If download fails, the DB state for this item is not changed by this function call.
            # It will be picked up in the next run.

if __name__ == '__main__':
    download_pending_files()
from loguru import logger
import os
from src.queue_manager.core import QueueManager
from src.queue_manager.interfaces.drive import authenticate_drive, download_file

# Logging setup
LOG_DIR = os.path.join(os.getcwd(), 'logs', 'downloader')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

LOCAL_BLF_DIR = os.path.join(os.getcwd(), 'blf')

def download_pending_files():
    queue = QueueManager()
    drive_service = authenticate_drive()
    files_to_download = queue.get_files_to_download()
    for item in files_to_download:
        drive_filename = item['drive_filename']
        local_filename = item['local_filename']
        subfolder = item['subfolder'] or ''
        blf_id = item['blf_link']
        # Build local path
        local_dir = os.path.join(LOCAL_BLF_DIR, subfolder)
        os.makedirs(local_dir, exist_ok=True)
        local_path = os.path.join(local_dir, local_filename)
        if os.path.exists(local_path):
            logger.info(f"File already exists locally: {local_path}")
            queue.set_downloaded(drive_filename, subfolder, True)
            continue
        logger.info(f"Downloading {drive_filename} (Drive ID: {blf_id}) to {local_path}")
        try:
            download_file(drive_service, blf_id, local_path)
            logger.info(f"Downloaded {drive_filename} to {local_path}")
            queue.set_downloaded(drive_filename, subfolder, True)
        except Exception as e:
            logger.error(f"Failed to download {drive_filename}: {e}")

if __name__ == '__main__':
    download_pending_files() 
;CANoe Version |4|15|0|51941 nikhil_config_trying_logging 
Version: 15.2.41 Build 41
32 PRO
5
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1.0.1
VGlobalConfiguration 1 Begin_Of_Object
18
VGlobalParameters 2 Begin_Of_Object
36
2
3,100,200,500
1000000 1.000000 0 1000 1 1 0 0 1 1 1 0 0 0 1 0 0 0
1
0
1 1
ResetSignalsOnMeasurementStart=1
VDatabaseContainerStreamer 3 Begin_Of_Object
9
2
<VFileName V9 QL> 1 "S1_CAN1_Move4.0_Rev4.0.28_Untruncated.dbc" 
CAN1_V01_Move4_0_Rev4_0_28

1
0
1
000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001

<VFileName V9 QL> 1 "..\..\..\DBC Files\S1_CAN2_Move4.0_Rev4.0.28_Untruncated.dbc" 
S1_CAN2_Move3_0_Rev3_0_27

1
1
1
000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010

1
0
0
0
0
0
0
End_Of_Object VDatabaseContainerStreamer 3
0
0
1
<VFileName V9 QL> 1 "nikhil_config_trying_logging.cfg" 
0
0
0
1
VPersistentEnvarSelectionData 3 Begin_Of_Object
1
1 1 0 0
~
~
End_Of_Object VPersistentEnvarSelectionData 3
VPersistentExtensionData 3 Begin_Of_Object
3
VPersistentRelation 4 Begin_Of_Object
1
HookDLLActivations
1
1
End_Of_Object VPersistentRelation 4
End_Of_Object VPersistentExtensionData 3
VPersistentTreeStateInfo 3 Begin_Of_Object
1
Version
5
DialogBegin
1
75 75 605 580
SymbolExplorerDialogBegin
1
HistoryBegin
1 0
HistoryEnd
FiltersBegin
Begin
3 0 -1
0
SymbSelHeaderMgrBegin
1 6
0 1 200 0 0
1 1 100 0 0
2 0 100 0 0
3 0 75 1 1
5 1 80 0 0
6 1 200 0 0
15 1 80 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 -1
-1
SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
10 1 75 0 0
11 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 -1
-1
SymbSelHeaderMgrBegin
1 3
0 1 200 0 0
7 0 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End

FiltersEnd
0 0
SymbolExplorerDialogEnd

DialogEnd
End_Of_Object VPersistentTreeStateInfo 3
VPrintSettings 3 Begin_Of_Object
1
0 0 0 0 0
0 0 0 0 0 0 0 0 0 1
<VFileName V9 QL> 1 "" 
@@@@
Ausdruck Seite: {PAGE}    {DATE}  {TIME}
Lizenznehmer: {LICENSENAME}
Seriennummer: {LICENSENO}
@@@@
0

End_Of_Object VPrintSettings 3
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\Public\Documents\Vector\CANwin\8.0.71\CANwin Demos\templates\portlink1.pre" 
1
VPortlinkConfigurationStreamer 3 Begin_Of_Object
1
1
0
0
0
END_OF_DRIVER
END_OF_PORT_CONFIGURATION_STREAM
End_Of_Object VPortlinkConfigurationStreamer 3
0
0
0
0
<VMultibusFilterDialogSettings> V2 275
<VWidthInfoContainer> 6
<VWidthInfo> V2 20 1 -1 36 
<VWidthInfo> V2 1 1 -1 47 
<VWidthInfo> V2 0 1 -1 40 
<VWidthInfo> V2 2 1 -1 47 
<VWidthInfo> V2 4 1 -1 33 
<VWidthInfo> V2 5 3 0 24 1 24 2 24 
<VBusWidthInfoSet> 6
1 0 <VWidthInfoContainer> 1
<VWidthInfo> V2 6 2 0 36 1 36 
1 1 <VWidthInfoContainer> 4
<VWidthInfo> V2 7 1 -1 45 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 9 1 -1 30 
<VWidthInfo> V2 6 2 0 24 1 32 
1 2 <VWidthInfoContainer> 4
<VWidthInfo> V2 19 1 -1 45 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 22 1 -1 30 
<VWidthInfo> V2 6 2 0 24 1 32 
7 0 <VWidthInfoContainer> 2
<VWidthInfo> V2 23 1 -1 47 
<VWidthInfo> V2 29 1 -1 47 
8 0 <VWidthInfoContainer> 5
<VWidthInfo> V2 7 1 -1 47 
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 11 3 0 30 1 30 3 30 
<VWidthInfo> V2 17 3 0 20 1 20 2 20 
<VWidthInfo> V2 18 6 0 26 1 33 2 26 3 26 4 20 5 40 
9 0 <VWidthInfoContainer> 1
<VWidthInfo> V2 41 1 -1 47 
EndOf <VMultibusFilterDialogSettings>
VGlobalActionsStreamer 3 Begin_Of_Object
3
3
0
End_Of_Object VGlobalActionsStreamer 3
VEventSortingConfigStreamer 3 Begin_Of_Object
1
0
0
0
1
1
1
0
End_Of_Object VEventSortingConfigStreamer 3
FlexRayOptionParameters: 2 0 1 0 1 1 :0 :
FlexRayOptionParametersEnd
VCaplOptionsStreamer 3 Begin_Of_Object
2
17
1448
0
2001
1
2002
0
2005
0
2008
1
2013
1
2020
1
2032
1
2038
1
2039
0
2040
1
2041
1
2054
0
2055
1
2065
0
2135
1
2201
0
1
512
End_Of_Object VCaplOptionsStreamer 3
VSVConfigurationStreamer 3 Begin_Of_Object
1
73
﻿<?xml version="1.0" encoding="utf-8"?>
<systemvariables version="4" />
2
0
End_Of_Object VSVConfigurationStreamer 3
VOfflineBusStatisticSettings 3 Begin_Of_Object
1
1
1
1 1
1 500000
1 2
1 500000
0 3
1 0
0 4
1 0
0 5
1 0
0 6
1 0
0 7
1 0
0 8
1 0
0 9
1 0
0 10
1 0
0 11
1 0
0 12
1 0
0 13
1 0
0 14
1 0
0 15
1 0
0 16
1 0
0 17
1 0
0 18
1 0
0 19
1 0
0 20
1 0
0 21
1 0
0 22
1 0
0 23
1 0
0 24
1 0
0 25
1 0
0 26
1 0
0 27
1 0
0 28
1 0
0 29
1 0
0 30
1 0
0 31
1 0
0 32
1 0
0 33
1 0
0 34
1 0
0 35
1 0
0 36
1 0
0 37
1 0
0 38
1 0
0 39
1 0
0 40
1 0
0 41
1 0
0 42
1 0
0 43
1 0
0 44
1 0
0 45
1 0
0 46
1 0
0 47
1 0
0 48
1 0
0 49
1 0
0 50
1 0
0 51
1 0
0 52
1 0
0 53
1 0
0 54
1 0
0 55
1 0
0 56
1 0
0 57
1 0
0 58
1 0
0 59
1 0
0 60
1 0
0 61
1 0
0 62
1 0
0 63
1 0
0 64
1 0
0 65
1 0
0 66
1 0
0 67
1 0
0 68
1 0
0 69
1 0
0 70
1 0
0 71
1 0
0 72
1 0
0 73
1 0
0 74
1 0
0 75
1 0
0 76
1 0
0 77
1 0
0 78
1 0
0 79
1 0
0 80
1 0
0 81
1 0
0 82
1 0
0 83
1 0
0 84
1 0
0 85
1 0
0 86
1 0
0 87
1 0
0 88
1 0
0 89
1 0
0 90
1 0
0 91
1 0
0 92
1 0
0 93
1 0
0 94
1 0
0 95
1 0
0 96
1 0
0 97
1 0
0 98
1 0
0 99
1 0
0 100
1 0
0 101
1 0
0 102
1 0
0 103
1 0
0 104
1 0
0 105
1 0
0 106
1 0
0 107
1 0
0 108
1 0
0 109
1 0
0 110
1 0
0 111
1 0
0 112
1 0
0 113
1 0
0 114
1 0
0 115
1 0
0 116
1 0
0 117
1 0
0 118
1 0
0 119
1 0
0 120
1 0
0 121
1 0
0 122
1 0
0 123
1 0
0 124
1 0
0 125
1 0
0 126
1 0
0 127
1 0
0 128
1 0
0 129
1 0
0 130
1 0
0 131
1 0
0 132
1 0
0 133
1 0
0 134
1 0
0 135
1 0
0 136
1 0
0 137
1 0
0 138
1 0
0 139
1 0
0 140
1 0
0 141
1 0
0 142
1 0
0 143
1 0
0 144
1 0
0 145
1 0
0 146
1 0
0 147
1 0
0 148
1 0
0 149
1 0
0 150
1 0
0 151
1 0
0 152
1 0
0 153
1 0
0 154
1 0
0 155
1 0
0 156
1 0
0 157
1 0
0 158
1 0
0 159
1 0
0 160
1 0
0 161
1 0
0 162
1 0
0 163
1 0
0 164
1 0
0 165
1 0
0 166
1 0
0 167
1 0
0 168
1 0
0 169
1 0
0 170
1 0
0 171
1 0
0 172
1 0
0 173
1 0
0 174
1 0
0 175
1 0
0 176
1 0
0 177
1 0
0 178
1 0
0 179
1 0
0 180
1 0
0 181
1 0
0 182
1 0
0 183
1 0
0 184
1 0
0 185
1 0
0 186
1 0
0 187
1 0
0 188
1 0
0 189
1 0
0 190
1 0
0 191
1 0
0 192
1 0
0 193
1 0
0 194
1 0
0 195
1 0
0 196
1 0
0 197
1 0
0 198
1 0
0 199
1 0
0 200
1 0
0 201
1 0
0 202
1 0
0 203
1 0
0 204
1 0
0 205
1 0
0 206
1 0
0 207
1 0
0 208
1 0
0 209
1 0
0 210
1 0
0 211
1 0
0 212
1 0
0 213
1 0
0 214
1 0
0 215
1 0
0 216
1 0
0 217
1 0
0 218
1 0
0 219
1 0
0 220
1 0
0 221
1 0
0 222
1 0
0 223
1 0
0 224
1 0
0 225
1 0
0 226
1 0
0 227
1 0
0 228
1 0
0 229
1 0
0 230
1 0
0 231
1 0
0 232
1 0
0 233
1 0
0 234
1 0
0 235
1 0
0 236
1 0
0 237
1 0
0 238
1 0
0 239
1 0
0 240
1 0
0 241
1 0
0 242
1 0
0 243
1 0
0 244
1 0
0 245
1 0
0 246
1 0
0 247
1 0
0 248
1 0
0 249
1 0
0 250
1 0
0 251
1 0
0 252
1 0
0 253
1 0
0 254
1 0
0 255
1 0
End_Of_Object VOfflineBusStatisticSettings 3
VNETOptionsStreamer 3 Begin_Of_Object
4
0
<VFileName V9 QL> 0 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe" 
1

0
End_Of_Object VNETOptionsStreamer 3
0
1
VUserFileMgrAnlyz 3 Begin_Of_Object
2
0
0
End_Of_Object VUserFileMgrAnlyz 3
VCLibraryOptions 3 Begin_Of_Object
1
0
End_Of_Object VCLibraryOptions 3
NValueObjectDisplay::VNameDisplaySettings 3 Begin_Of_Object
3
12
0
4
1
4
2
4
3
4
4
1
5
1
6
4
7
4
8
4
9
6
10
4
11
4
12
0
1
1
1
2
1
3
1
4
1
5
1
6
1
7
1
8
1
9
1
10
1
11
1
12
0
0
1
0
2
0
3
0
4
0
5
0
6
0
7
0
8
0
9
0
10
0
11
0
0
1
9
128
0
End_Of_Object NValueObjectDisplay::VNameDisplaySettings 3
ConfigurationSavedByCANwBeginner 0
VGlobalExportAndLoggingSettings 3 Begin_Of_Object
9
2
1
0
0
6
1
0.10000000000000001
2
0
0.10000000000000001
2
19
0
1
3
1
0
::
,
.
<VFileName V9 QL> 1 "" 
0
0
0
1
6
0
6
0
0
0.10000000000000001
1
0
0
6
730
0
0.10000000000000001
0
0
-1
VLoggingComment 4 Begin_Of_Object
1
1
VLoggingCommentAttribute 5 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 5
End_Of_Object VLoggingComment 4
End_Of_Object VGlobalExportAndLoggingSettings 3
0
VRTFilterOptions 3 Begin_Of_Object
3
0
0
0
0
0
End_Of_Object VRTFilterOptions 3
VRTTxBufferOptions 3 Begin_Of_Object
2
1
1
500
End_Of_Object VRTTxBufferOptions 3
VRTIRQReductionOptions 3 Begin_Of_Object
2
500
End_Of_Object VRTIRQReductionOptions 3
VPersistentDebuggerOptions 3 Begin_Of_Object
2
64
10000
End_Of_Object VPersistentDebuggerOptions 3
7
0
0
0
0
0
1
0
0
1
VAFDXGlobalSettings 3 Begin_Of_Object
4
1000
15
1
0
0
0
0
End_Of_Object VAFDXGlobalSettings 3
VRTCANErrorFrameOptions 3 Begin_Of_Object
1
1
0
End_Of_Object VRTCANErrorFrameOptions 3
1
ILConfiguration::VProxyManager 3 Begin_Of_Object
1
0
0
End_Of_Object ILConfiguration::VProxyManager 3
4
VGlobalVariableSettings 3 Begin_Of_Object
1
65001
End_Of_Object VGlobalVariableSettings 3
VGeneralLoggingBlockSettings 3 Begin_Of_Object
2
2000
4
0
3
End_Of_Object VGeneralLoggingBlockSettings 3
0
0
4
0
1
SecurityManager::VSecurityManager 3 Begin_Of_Object
3
0
0
NULL
End_Of_Object SecurityManager::VSecurityManager 3
VRTAutosarPDULayerMode 3 Begin_Of_Object
1
2
End_Of_Object VRTAutosarPDULayerMode 3
VErtOptions 3 Begin_Of_Object
2
0
0
End_Of_Object VErtOptions 3
0
1
4
VDistributedDebuggingSettings 3 Begin_Of_Object
1
0
2828
End_Of_Object VDistributedDebuggingSettings 3
VAdditionalLicenseOptions 3 Begin_Of_Object
1
0
End_Of_Object VAdditionalLicenseOptions 3
End_Of_Object VGlobalParameters 2
VDesktopManager 2 Begin_Of_Object
1
1
3
VDesktop 3 Begin_Of_Object
1
Trace
{09E7E12D-A97D-4875-B88E-FAC00DBC5477}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Configuration
{8575239B-BFC5-47CB-AE1E-46E7627E5DCB}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{2C007CF7-DF38-4EC1-B451-76B84AF681CC}" Guid="784c4fde-b4fc-42d5-a4fa-0af7cba87c83" State="DockableInsideHost" DockedSize="201, 201" FloatingSize="423, 189" HasOptions="False" ImageIndex="-1" Text="Write" TitleBarText="Write"><AutoHideStateInfo RootDock="Bottom" /><DockedInsideHostStateInfo RootDock="Bottom" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden>
kPersistNoLineBreak
</LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Analysis
{7950D4A7-0237-4745-9CD5-1A492D1180BC}
Begin_Of_Multi_Line_String
3
<?xml version="1.0"?><!--



  Actipro Docking/MDI for WinForms

  Copyright (c) 2001-2014 Actipro Software LLC.  All rights reserved.

  http://www.actiprosoftware.com



--><ToolWindowLayout Version="1.0"><LayoutData><HostContainerControl /><AutoHide Dock="Left" /><AutoHide Dock="Right" /><AutoHide Dock="Top" /><AutoHide Dock="Bottom" /><TabbedDocuments Orientation="Horizontal" /><FloatingContainers /><Hidden><ToolWindow Key="{F5E09530-AAE7-48d9-B925-CEF5027AA97D}" Guid="32e4b564-02ec-46fd-9296-f6315c198b9d" State="DockableInsideHost" DockedSize="325, 325" FloatingSize="325, 380"><AutoHideStateInfo RootDock="Left" /><DockedInsideHostStateInfo RootDock="Left" IsAttached="False" /><DockedOutsideHostStateInfo IsAttached="False" /></ToolWindow></Hidden></LayoutData><CustomData /></ToolWindowLayout>
End_Of_Serialized_Data 3
End_Of_Object VDesktop 3
4294967295
4294967295
4294967295
End_Of_Object VDesktopManager 2
0
VGBAnlyzBox 2 Begin_Of_Object
3
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
0 1 0 1 -1 -1 -8 -31 772 0 1272 588

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 22 22 953 535
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1001 592
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 772 0 1272 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 22 22 953 535
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 0 0 0 0 0 0 0 0 0
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 512 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{8C2E149F-8DB0-4F6E-931E-B0C8CACD85C5}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
4
1
75
VDAOGBFunctionBlock 4 Begin_Of_Object
1
1
0
TABPredecessor:
0
TABSuccessor:
2
VPlugConf 5 Begin_Of_Object
1
End_Of_Object VPlugConf 5
VDAOGBFunctionBlock 5 Begin_Of_Object
1
2
0
TABPredecessor:
1
TABSuccessor:
38
VChannelFilterConfiguration 6 Begin_Of_Object
3
VMigratedGenericConfiguration<class_VChannelFilterData> 7 Begin_Of_Object
1
VChannelFilterData 8 Begin_Of_Object
2
1
VBusChannelData 9 Begin_Of_Object
1
2
2
1
2
1
1
End_Of_Object VBusChannelData 9
End_Of_Channel_Data
1
End_Of_Object VChannelFilterData 8
End_Of_Object VMigratedGenericConfiguration<class_VChannelFilterData> 7
0
0
End_Of_Object VChannelFilterConfiguration 6
VDOCrossing 6 Begin_Of_Object
2
3
0
VDAOGBHSStd 7 Begin_Of_Object
1
4
0
0 0
TABPredecessor:
43
TABSuccessor:
45
VDODynamicLine 8 Begin_Of_Object
1
5
0
0
VDAOSwitch 9 Begin_Of_Object
1
45
0
TABPredecessor:
4
TABSuccessor:
46
VDAOGBHSStd 10 Begin_Of_Object
1
46
0
0 0
TABPredecessor:
45
TABSuccessor:
48
VDODynamicLine 11 Begin_Of_Object
1
47
0
0
VDOFRamification 12 Begin_Of_Object
1
48
0
TABPredecessor:
46
TABSuccessor:
50
5
VDORefinement 13 Begin_Of_Object
1
49
0
13
VDAOGBHSStd 14 Begin_Of_Object
1
50
0
0 0
TABPredecessor:
48
TABSuccessor:
52
VDODynamicLine 15 Begin_Of_Object
1
51
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
52
0
TABPredecessor:
50
TABSuccessor:
55
VNetMSControlConfiguration 17 Begin_Of_Object
1
VNETControlConfiguration 18 Begin_Of_Object
1
VConfigurationRoot 19 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 19
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 1 0 1 -1 -1 -1 -1 0 0 521 400
CAN Statistics
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1016 510
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 521 400
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1272 829
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 98 705 498
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{61AD3E74-2E74-428E-A5EF-47D53688E895}
0
End_Of_Object VBoxRoot 21
1 2 0 1 -1 -1 -1 -1 0 0 482 266
End_Of_Object VUniqueBox 20
1
1 2 0 1 -1 -1 -1 -1 0 0 482 266
0
End_Of_Object VNETControlBox 19
135
APPDIR Vector.CANalyzer.CAN.StatisticsMonitor.DLL
Vector.CANalyzer.CAN.StatisticsMonitor, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.CAN.StatisticsMonitor.StatisticsMonitor
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
App
2
UInt16
Channel
1
Array
States
3

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.UInt64
3
1
0
19
UInt64
ArrayElement
65536
UInt64
ArrayElement
65537
UInt64
ArrayElement
65539
UInt64
ArrayElement
65539
UInt64
ArrayElement
65540
UInt64
ArrayElement
65541
UInt64
ArrayElement
65542
UInt64
ArrayElement
65543
UInt64
ArrayElement
65544
UInt64
ArrayElement
65545
UInt64
ArrayElement
65546
UInt64
ArrayElement
65547
UInt64
ArrayElement
18
UInt64
ArrayElement
17
UInt64
ArrayElement
65548
UInt64
ArrayElement
65549
UInt64
ArrayElement
65550
UInt64
ArrayElement
196623
UInt64
ArrayElement
65552
UInt64
ArrayElement
131071
Array
ColumnWidths
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Int32
4
1
0
4
Int32
ArrayElement
160
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
Int32
ArrayElement
75
UInt64
TimerInterval
10
UInt64
TimerIntervalMs
1050
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
4
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETControlConfiguration 18
End_Of_Object VNetMSControlConfiguration 17
VDOLine 17 Begin_Of_Object
1
53
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
54
0
7
VDAOGBHSStd 14 Begin_Of_Object
1
55
0
0 0
TABPredecessor:
52
TABSuccessor:
57
VDODynamicLine 15 Begin_Of_Object
1
56
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
57
0
TABPredecessor:
55
TABSuccessor:
60
VTraceConfiguration 17 Begin_Of_Object
1
VTraceControlCfg 18 Begin_Of_Object
9
VTraceSearchCfg 19 Begin_Of_Object
1
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
0
End_Of_Object VTraceSearchCfg 19
VTraceFilterCfg 19 Begin_Of_Object
1
0
1
VTraceAnalysisFilterGroup 20 Begin_Of_Object
2
1
Filter Group 0
2
VTraceAnalysisSingleFilter 21 Begin_Of_Object
4
1
1
0
End_Of_Object VTraceAnalysisSingleFilter 21
VTraceAnalysisSingleFilter 21 Begin_Of_Object
4
0
0
0
End_Of_Object VTraceAnalysisSingleFilter 21
1
End_Of_Object VTraceAnalysisFilterGroup 20
End_Of_Object VTraceFilterCfg 19
1
0
0
0
43
0
0
1
0
2
0
3
0
4
0
5
0
6
1
18
GFver=5;ver=5: FT TF TF FT FT FF FF FF FF TF;T F _Statistics;F T CANoe;F T VTS
End_Of_Serialized_Data 18
7
0
8
0
9
0
10
0
11
1
18
ver=5: FT FT FT FT
End_Of_Serialized_Data 18
12
0
13
0
14
0
15
0
16
0
17
0
18
0
19
0
20
0
21
0
22
0
23
0
24
0
25
0
26
0
27
0
28
0
29
0
30
0
31
0
32
0
33
0
34
0
35
0
36
0
37
0
38
0
39
0
40
0
41
0
42
0
0
1
VTraceColumnConfiguration 19 Begin_Of_Object
4
1
Initial
142
VTNColumnData 20 Begin_Of_Object
4
0
104
0
Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
1
37
1
Chn
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
2
45
2
ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
3
120
3
Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
4
170
-1
ID / Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
5
43
5
Dir
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
6
45
6
DLC
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
7
270
7
Data
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
8
37
-1
Attr
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
9
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
10
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
11
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
12
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
13
150
-1
Frame Duration
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
14
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
15
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
16
70
4
Event Type
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
17
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
18
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
19
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
20
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
21
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
22
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
23
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
24
85
-1
Bus Idle
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
25
85
-1
Bus Busy
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
26
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
27
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
28
110
-1
HH:MM:SS
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
29
85
-1
Diff time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
30
50
-1
Bustype
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
31
120
-1
Send node
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
32
50
-1
Bus
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
33
80
-1
Database
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
34
50
-1
Counter
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
35
90
-1
Start of Frame
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
36
50
-1
PGN
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
37
50
-1
DP
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
38
50
-1
Prio
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
39
50
-1
Specific 10
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
40
50
-1
Specific 11
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
41
50
-1
Specific 12
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
42
50
-1
Specific 13
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
43
50
-1
Specific 14
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
44
50
-1
Specific 15
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
45
50
-1
Specific 16
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
46
50
-1
Specific 17
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
47
50
-1
Specific 18
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
48
50
-1
Specific 19
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
49
50
-1
Specific 20
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
50
150
-1
Data ASCII
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
51
150
-1
Comment
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
52
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
53
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
54
50
-1
Namespace
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
55
120
-1
Diff time (ref. event)
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
56
50
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
57
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
58
100
-1
---
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
59
120
-1
Date / Time
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
60
50
-1
Receive Node
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
61
50
-1
VL No.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
62
200
-1
Sub VL ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
63
50
-1
Transmitter
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
64
200
-1
Receiver
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
65
50
-1
Tx Port ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
66
50
-1
Rx Port ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
67
65
-1
Port Char.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
68
50
-1
IP Frag.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
69
150
-1
Master Port Name
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
70
65
-1
BAG
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
71
65
-1
Seq. No.
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
72
50
-1
Line
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
73
50
-1
Interface ID
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
74
50
-1
Equipment
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
75
50
-1
Partition
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
76
50
-1
BRS
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
77
50
-1
ESI
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
78
45
-1
Data length
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
79
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
80
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
81
10
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
82
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
83
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
84
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
85
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
86
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
87
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
88
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
89
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
90
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
91
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
92
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
93
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
94
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
95
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
96
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
97
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
98
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
99
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
100
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
101
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
102
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
103
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
104
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
105
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
106
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
107
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
108
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
109
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
110
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
111
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
112
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
113
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
114
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
115
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
116
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
117
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
118
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
119
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
120
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
121
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
122
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
123
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
124
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
125
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
126
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
127
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
128
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
129
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
130
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
131
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
132
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
133
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
134
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
135
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
136
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
137
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
138
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
139
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
140
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
VTNColumnData 20 Begin_Of_Object
4
141
50
-1
 
1
0
1
0
0
End_Of_Object VTNColumnData 20
End_Of_Object VTraceColumnConfiguration 19
0
0
VTraceControlFixedModeExpansionItems 19 Begin_Of_Object
3
0
End_Of_Object VTraceControlFixedModeExpansionItems 19
18
C:\Users\<USER>\Documents\Vector\CANoe\15 (x64)\Templates\CANoe
End_Of_Serialized_Data 18
18
Trace-Fenster
End_Of_Serialized_Data 18
18

End_Of_Serialized_Data 18
0
776880451
1
1
18
VLogExportPersister 19 Begin_Of_Object
7
1416
44616685
Trace-Fenster
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19

End_Of_Serialized_Data 18
0
0
0
290
0
150
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe" 
1
End_Of_Object VTraceControlCfg 18
VNETTraceControlBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 2 2 3 -1 -1 -8 -31 431 301 1013 595
Trace
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 1272 534
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1272 829
END_OF_DESKTOP_DATA
6
2 3 -1 -1 -8 -31 431 301 1013 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 431 301 1013 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{AFB1C724-5CE6-46B5-9D95-1CAB21180494}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETTraceControlBox 18
End_Of_Object VTraceConfiguration 17
VDOLine 17 Begin_Of_Object
1
58
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
59
0
4
VDAOGBHSStd 14 Begin_Of_Object
1
60
0
0 0
TABPredecessor:
57
TABSuccessor:
62
VDODynamicLine 15 Begin_Of_Object
1
61
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
62
0
TABPredecessor:
60
TABSuccessor:
65
VNETDataListControlHost 17 Begin_Of_Object
1
VConfigurationRoot 18 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 18
VNETDataBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 2 0 1 -1 -1 -1 -1 522 0 1039 265
Data
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 423 193
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1013 595
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 423 193
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1001 591
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 522 0 1039 265
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1272 829
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{806B9B7C-0935-4206-A253-D2EFE270B0B4}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETDataBox 18
1
9
0
20 150 16 100 40 40 50 100 60 100 1
35 35
30
70 70 70 100
100
1 1 0 1 0 0 1 1 1 0 1
0 0
0
0 0 0 0
0
1 0
5000 0 10000 0 10000
1 0
VLogCfgData 18 Begin_Of_Object
12
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
0
4
1
1
0
2
0
0
18
VLogExportPersister 19 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19

End_Of_Serialized_Data 18
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Data.mdf" 
0
1
0
10
80
0
1
1
0


<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Data.mdf" 
1
0
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
0
edc352bb-9cc1-420a-9e83-0e1052ca12c7
1
VLoggingComment 19 Begin_Of_Object
1
1
VLoggingCommentAttribute 20 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 20
End_Of_Object VLoggingComment 19
End_Of_Object VLogCfgData 18
1
[End_of_Control]
End_Of_Object VNETDataListControlHost 17
VDOLine 17 Begin_Of_Object
1
63
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
64
0
5
VDAOGBHSStd 14 Begin_Of_Object
1
65
0
0 0
TABPredecessor:
62
TABSuccessor:
67
VDODynamicLine 15 Begin_Of_Object
1
66
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
67
0
TABPredecessor:
65
TABSuccessor:
70
VGraphBoxConf 17 Begin_Of_Object
1
VNETGraphBox 18 Begin_Of_Object
1
VNETControlBox 19 Begin_Of_Object
2
VUniqueBox 20 Begin_Of_Object
1
VBoxRoot 21 Begin_Of_Object
1
1
1 0 0 1 -1 -1 -1 -1 0 401 1272 829
Graphics
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1001 592
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1001 591
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 401 1272 829
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1272 829
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{D15DB9FD-7F05-415C-94AC-708B1CEB44CC}
0
End_Of_Object VBoxRoot 21
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 20
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 19
End_Of_Object VNETGraphBox 18
81
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
Expanded
True
Int32
SplitterWidth
184
Int32
SplitterHeight
80
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.Position
3
LegendPosition
3
Int32
value__
0
--TextFormatter: End of Object--
APPDIR Vector.CANalyzer.Graphic.DLL
Vector.CANalyzer.Graphic, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Graphic.GraphicCommandID
4
ScrollSignalsButton1Command
4
Int32
value__
29
--TextFormatter: End of Object--
TypeRef:4
ScrollSignalsButton2Command
5
Int32
value__
30
--TextFormatter: End of Object--
TypeRef:4
ScrollButton1Command
6
Int32
value__
35
--TextFormatter: End of Object--
TypeRef:4
ScrollButton2Command
7
Int32
value__
36
--TextFormatter: End of Object--
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
VSignalObjectStreamer 18 Begin_Of_Object
1
0
[GraphWindow:x_x_x_x_x_x_WindowBk_Grid_AxisBk_XAxisFr_YAxisFr_x_x_x_x_x_x]
0 100000 100000 200000 576000000 1 ffffff b2b2b2 ffffff 0 0 0 0 1 1 1 0
0 30 5000
0
0 100
0
16777215
0
2
0
1
41943040
-1
VLogExportPersister 19 Begin_Of_Object
7
1416
25200253
Graphics Window
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 19
12
20 25 110 20 18 55 55 55 55 55 57 13 
184
0
0
1
0
0
0
-11
0
0
0
0
0
0
0
400
0
Tahoma
0
1
0
0
0
-11
0
0
0
34
0
0
0
400
0
Tahoma
0
1
1
0
0
11711154
32768
0
0
0
0
0
0
0
0
0
300
0 10
1
0
0
VLogCfgData 19 Begin_Of_Object
12
0
0
0
0
0
0
0
0
1024
60
0
0
0
3
0
4
1
1
0
2
0
0
19
VLogExportPersister 20 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 20

End_Of_Serialized_Data 19
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Graphics.mdf" 
0
1
0
10
80
0
1
1
0


<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Graphics.mdf" 
1
0
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\{LoggingBlock}.mdf" 
0
0597a29e-c09f-43eb-ac3f-a50b9c9eea57
1
VLoggingComment 20 Begin_Of_Object
1
1
VLoggingCommentAttribute 21 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 21
End_Of_Object VLoggingComment 20
End_Of_Object VLogCfgData 19
0 128
0 0 0 0
1 40 20 15
0 -1
1
1
0
0
0
1 12
1 1 1 0 0 0 0 0 0 0 0 0 
0 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 
20 25 130 55 55 55 55 55 57 57 82 30 
184 80
0
0
30000000000
1
0
0
End_Of_Object VSignalObjectStreamer 18
End_Of_Object VGraphBoxConf 17
VDOLine 17 Begin_Of_Object
1
68
0
130 0
NULL
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
VDORefinement 13 Begin_Of_Object
1
69
0
6
VDAOGBHSStd 14 Begin_Of_Object
1
70
0
0 0
TABPredecessor:
67
TABSuccessor:
72
VDODynamicLine 15 Begin_Of_Object
1
71
0
0
VDAOGBFunctionBlock 16 Begin_Of_Object
1
72
0
TABPredecessor:
70
TABSuccessor:
74
VTriggerConfiguration 17 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 18 Begin_Of_Object
1
VTriggerCfgData 19 Begin_Of_Object
5
2
0
0
0
0
0
0
0
0
0
0
0
200000
1
1
1
1
50000
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
VEvCondBlock 20 Begin_Of_Object
1
VEvCondGroup 21 Begin_Of_Object
2
VEvCondPrimitive 22 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 22
1
0
0
End_Of_Object VEvCondGroup 21
End_Of_Object VEvCondBlock 20
0
0
0
116
0
0
0
2
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 19
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 18
VTriggerBox 18 Begin_Of_Object
1
VBoxRoot 19 Begin_Of_Object
1
1
1 -1 0 1 0 0 0 0 208 222 817 579
Logging
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 208 222 817 579
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{28AF63C3-C9F2-47F0-9A0D-BADA436F6A04}
0
End_Of_Object VBoxRoot 19
End_Of_Object VTriggerBox 18
0
End_Of_Object VTriggerConfiguration 17
VDOLine 17 Begin_Of_Object
1
73
0
10 0
VDAOGBFunctionBlock 18 Begin_Of_Object
1
74
0
TABPredecessor:
72
TABSuccessor:
0
VLoggingConfiguration 19 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 20 Begin_Of_Object
1
VLogCfgData 21 Begin_Of_Object
12
0
1
1
1
0
0
0
0
1024
60
1
0
0
3
0
4
1
1
0
2
0
0
21
VLogExportPersister 22 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 22

End_Of_Serialized_Data 21
<VFileName V9 QL> 1 "..\..\..\..\Desktop\Can Logs\S1AIR_Diff_Charger.blf" 
0
1
0
10
80
0
1
1
1


<VFileName V9 QL> 1 "..\..\..\..\Desktop\Can Logs\S1AIR_Diff_Charger{LocalTime}.blf" 
<VFileName V9 QL> 1 "..\..\..\..\Desktop\Can Logs\S1AIR_Diff_Charger2023-07-25_20-47-49.blf" 
1
1
<VFileName V9 QL> 1 "..\..\..\..\Desktop\Can Logs\S1AIR_Diff_Charger{LocalTime}.blf" 
0
e1d5399d-bb9d-43f4-ab37-8c54ba8b31e7
1
VLoggingComment 22 Begin_Of_Object
1
1
VLoggingCommentAttribute 23 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 23
End_Of_Object VLoggingComment 22
End_Of_Object VLogCfgData 21
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 20
1
End_Of_Object VLoggingConfiguration 19
VDOLine 19 Begin_Of_Object
1
75
0
60 0
NULL
End_Of_Object VDOLine 19

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 18
End_Of_Object VDOLine 17

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 16
End_Of_Object VDODynamicLine 15
End_Of_Object VDAOGBHSStd 14
NULL
End_Of_Object VDORefinement 13
End_Of_Object VDOFRamification 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
End_Of_Object VDAOSwitch 9
End_Of_Object VDODynamicLine 8
End_Of_Object VDAOGBHSStd 7
NULL
VDODynamicLine 7 Begin_Of_Object
1
37
1
20
VDAOGBHSStd 8 Begin_Of_Object
1
38
1
1 0
TABPredecessor:
2
TABSuccessor:
43
VDODynamicLine 9 Begin_Of_Object
1
39
1
0
VDODynamicLine 10 Begin_Of_Object
1
40
1
10
VDODynamicLine 11 Begin_Of_Object
1
41
0
20
VDOLine 12 Begin_Of_Object
1
42
0
10 0
VDAOGBFunctionBlock 13 Begin_Of_Object
1
43
0
TABPredecessor:
38
TABSuccessor:
4
VLoggingConfiguration 14 Begin_Of_Object
4
VMigratedGenericConfiguration<class_VLogCfgData> 15 Begin_Of_Object
1
VLogCfgData 16 Begin_Of_Object
12
0
1
1
1
0
0
0
0
1024
60
0
0
0
3
1
4
1
1
0
2
1
0
16
VLogExportPersister 17 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 17

End_Of_Serialized_Data 16
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Logging_Prefilter.blf" 
0
1
0
30
80
0
1
1
1


<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Logging_Prefilter.blf" 
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe" 
1
1
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Logging_Prefilter.blf" 
0
e212f1ef-f5c0-4cb4-b59d-2f759616fb63
1
VLoggingComment 17 Begin_Of_Object
1
1
VLoggingCommentAttribute 18 Begin_Of_Object
1
Comment

1
End_Of_Object VLoggingCommentAttribute 18
End_Of_Object VLoggingComment 17
End_Of_Object VLogCfgData 16
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 15
5
End_Of_Object VLoggingConfiguration 14
NULL

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 13
End_Of_Object VDOLine 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDODynamicLine 9
End_Of_Object VDAOGBHSStd 8
End_Of_Object VDODynamicLine 7
4
End_Of_Object VDOCrossing 6

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
VDAOGBFunctionBlock 4 Begin_Of_Object
1
44
0
TABPredecessor:
0
TABSuccessor:
45
VOfflineSrcConfiguration 5 Begin_Of_Object
3
VMigratedGenericConfiguration<struct_VOfflineCfgData> 6 Begin_Of_Object
1
VOfflineCfgData 7 Begin_Of_Object
2
VReplayCfgBase 8 Begin_Of_Object
1
0
1
End_Of_Object VReplayCfgBase 8
VCfgBreakCondition 8 Begin_Of_Object
1
VDataBreakCondition 9 Begin_Of_Object
1
0
VEvCondBlock 10 Begin_Of_Object
1
VEvCondGroup 11 Begin_Of_Object
2
VEvCondPrimitive 12 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 12
1
0
0
End_Of_Object VEvCondGroup 11
End_Of_Object VEvCondBlock 10
End_Of_Object VDataBreakCondition 9
End_Of_Object VCfgBreakCondition 8
0
0
0
0
0
0
End_Of_Object VOfflineCfgData 7
End_Of_Object VMigratedGenericConfiguration<struct_VOfflineCfgData> 6
VChannelMapping 6 Begin_Of_Object
1
0
End_Of_Object VChannelMapping 6
End_Of_Object VOfflineSrcConfiguration 5
NULL

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
0
End_Of_Object VDOLocalInfoStruct 3
67305472
0
0
2
VDOLocalInfoStruct 3 Begin_Of_Object
End_Of_Object VDOLocalInfoStruct 3
End_Of_Serialized_Data 2
0.000000
0 0
End_Of_Object VGBAnlyzBox 2
VGBRealTimeBox 2 Begin_Of_Object
1
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
0 0 0 1 -1 -1 -8 -31 0 0 771 588

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 44 44 975 557
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1013 595
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -8 -31 0 0 771 588
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1366 528
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 44 44 975 557
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 0 0 0 0 0 0 0 0 0
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 512 0 1024 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 512 0 1024 577
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{E2D2C0D5-9337-48B4-8DC6-E6B0C4CB6FE0}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
4
1
58
VDAOBus 4 Begin_Of_Object
1
1
0
0
TABPredecessor:
0
TABSuccessor:
2
VDAOGBFunctionBlock 5 Begin_Of_Object
1
2
0
TABPredecessor:
1
TABSuccessor:
0
VCardConf 6 Begin_Of_Object
1
End_Of_Object VCardConf 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
End_Of_Object VDAOBus 4
NULL
0
End_Of_Object VDOLocalInfoStruct 3
0.000000
0 0
1 0 2 59421 1 233 1 2882400001 527 760 176 727 2882400002  0 0 0 59421 1 280 1 2882400001 522 522 178 178 2882400002  1 0 0 0 0 0   3 
SS_BEGIN_COMMON_INFO
1
0
SS_END_COMMON_INFO

EOF_MBSSDATA
2
CAN
1
1
1
2131827361040 1 1 1 0 0 0 0 0 2000 0 1 0 
SS_BEGIN_COMMON_INFO
1
3
Channels
1
Databases
1
Misc
1
SS_END_COMMON_INFO

EOF_BUSDATA
1
_Start_VPRBSManager 1 
0 0x32 0x1 
_End_VPRBSManager
NodeSignalPanelBustypeCount 0
EOF_BUS
Network
1
1
2
2131827368000 1 1 1 0 0 0 0 0 2000 0 1 0 
SS_BEGIN_COMMON_INFO
1
3
Channels
1
Databases
1
Misc
1
SS_END_COMMON_INFO

EOF_BUSDATA
1
_Start_VPRBSManager 1 
0 0x32 0x1 
_End_VPRBSManager
NodeSignalPanelBustypeCount 0
EOF_BUS

EOF_MBSSDATA
End_Of_Object VGBRealTimeBox 2
VWriteBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 0 0 1 -1 -1 -1 -1 0 535 1272 829
Write
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 535 1272 829
6 0 1027 169 0 0 300 180 300 180 0 61440 1 36756 1904 0 0 0 0 260 0 0 0 1 5 32767 1 59422 1 5 1017 174 4 6 518 1 0 1 0 1 169 0 59422 1 
END_OF_DOCK_INFO
1
0
0
0
1
1272 829
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 402 423 595
6 1 0 0 0 0 0 0 423 189 0 0 1 36756 1904 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 1 0 0 0 1 201 0 59422 1 
END_OF_DOCK_INFO
0
0
0
0
1
1272 829
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 402 423 595
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 0 0 0 0 0 0 0 0 0
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 363 1024 576
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 363 1024 576
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{2C007CF7-DF38-4EC1-B451-76B84AF681CC}
0
End_Of_Object VBoxRoot 4
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 3
2
VWriteControlAdapter 3 Begin_Of_Object
2
VControlAdapter 4 Begin_Of_Object
1
End_Of_Object VControlAdapter 4
1
3
WListVer 2
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\Public\Documents\Vector\CANwin\8.0.71\CANwin Demos\templates" 
 0 1 1 1 1 0
 False 147 90 0 280 280 280 280
End_Of_Serialized_Data 3
End_Of_Object VWriteControlAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VWriteBox 2
VWinStore 2 Begin_Of_Object
1
22 2 3 -1 -1 -1 -1 -10000 -10000 -8980 -9233
End_Of_Child_List
End_Of_Object VWinStore 2
VWinStore 2 Begin_Of_Object
1
22 0 1 -1 -1 -1 -1 0 21 1021 739
End_Of_Child_List
End_Of_Object VWinStore 2
VChipMultibusConfig 2 Begin_Of_Object
1
Version 8 10
5 64
0
9 0
11 0
1
14 0
1
12 1
3
0 127 0 0 1 2900 10 0 0 0
1 1
4
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
13 0
2
15 0
7 0
16 0
1
End_Of_Object VChipMultibusConfig 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 0
0
End_Of_Object VChipConfigC200 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 1
0
End_Of_Object VChipConfigC200 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC005 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC005 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC527 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC527 2
VChipConfigC1000 2 Begin_Of_Object
1
1
1000 16000 0 0
1 35 1 0 2 0 0 0 0 0 0
1 1000 0
1
55 24 0
2 2 27 12 2 2 0
0
0 0
End_Of_Object VChipConfigC1000 2
VChipConfigC1000 2 Begin_Of_Object
1
2
1000 16000 0 0
1 35 1 0 2 0 0 0 0 0 0
1 1000 1
1
55 24 0
2 2 27 12 2 2 0
0
0 0
End_Of_Object VChipConfigC1000 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 0
0
End_Of_Object VChipConfigC462 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 1
0
End_Of_Object VChipConfigC462 2
0
13
3 0
5 0
6 0
7 0
8 0
9 0
11 0
13 0
14 0
15 0
16 0
17 0
18 0
VScanBaudrateConfiguration 2 Begin_Of_Object
1
2
256
1000
5
1000
1
0
1
256
1000
5
1000
1
0
1
End_Of_Object VScanBaudrateConfiguration 2
4
0
VPersistentPath 2 Begin_Of_Object
1
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\CAN_500kBaud_1ch.cpd" 
End_Of_Object VPersistentPath 2
0
3
0
0
VPlugInsPersistentWrapper 2 Begin_Of_Object
1
<PlugIns>
</PlugIns>
End_Of_Object VPlugInsPersistentWrapper 2
0
0
VMacroStreamer 2 Begin_Of_Object
2
VMacroManager 3 Begin_Of_Object
3
0
0
0
0
End_Of_Object VMacroManager 3
End_Of_Object VMacroStreamer 2
VSignalGeneratorStreamer 2 Begin_Of_Object
1
VAnlyzSigGeneratorManager 3 Begin_Of_Object
5
0
0
0
0
0
End_Of_Object VAnlyzSigGeneratorManager 3
End_Of_Object VSignalGeneratorStreamer 2
SignalGeneratorsReplay 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 -1 -1 211 228 1173 656
Signal Generators and Signal Replay
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 211 228 1173 656
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 211 228 827 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 211 228 827 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{13DE5351-04E5-439B-9987-A543976B9ACA}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
0
0
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
2
1
2
1
<?xml version="1.0" encoding="utf-8"?>
<SymbolSelectionProperties xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<AreDiagQualifiersShown>false</AreDiagQualifiersShown>
<CurrentViewId>NetworkSymbols</CurrentViewId>
<SearchProperties>
<SearchTermHistory />
</SearchProperties>
<ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>5</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>6</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>9</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>7</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>8</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>10</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>11</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>12</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>13</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>14</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>15</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>/</Key>
<NodeTreeProperties />
</ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>4</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>Embedded/NetworkSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
<ViewProperties>
<ColumnsProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>0</Position>
<Sorting>Ascending</Sorting>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>2</Position>
<Width>200</Width>
</ColumnProperties>
<ColumnProperties>
<IsVisible>true</IsVisible>
<Position>3</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
<ColumnProperties>
<Position>-1</Position>
<Width>100</Width>
</ColumnProperties>
</ColumnsProperties>
<Key>Embedded/VariableSymbols</Key>
<NodeTreeProperties />
</ViewProperties>
</ViewProperties>
</SymbolSelectionProperties>
END_OF_WORKSPACE_MEMBER_DATA
END_OF_WORKSPACE_MEMBER
1
0
0

END_OF_WORKSPACE_DATA

END_OF_WORKSPACE_CONFIGURATION
LinNMWindow 0
LinScopeWindow 0
VCanGlOpConf 2 Begin_Of_Object
1
1
2
End_Of_Object VCanGlOpConf 2
0
1
0
<End_of_SimulinkController>
StartOfComment
EndOfComment
15.2 SP2
VHILInterfaceMgrAnlyz 2 Begin_Of_Object
5
0
0
2809
0
3030
1
End_Of_Object VHILInterfaceMgrAnlyz 2
0
BasicDiagnosticsEditor 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 -1 -1 204 115 1063 889
Basic Diagnostics
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 204 115 1063 889
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 204 115 820 461
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 204 115 820 461
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{71D16D25-0275-4116-B7AA-104E7328DF81}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
0
0
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANalyzer.BasicDiagnosticsEditor.DLL
Vector.CANalyzer.BasicDiagnosticsEditor, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.BasicDiagnosticsEditor.VBasicDiagnosticsEditorWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
0
CalculateExtendedStatistics 1
0
0
25
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.SymbolSelectionListBox.Data.SymbolMRUList
1
1
Int32
Count
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
J1939::VGlobalSettings 2 Begin_Of_Object
2
1
0
End_Of_Object J1939::VGlobalSettings 2
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 118 801 474
Start Values
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 816 556
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{38D06A6E-39BC-4EB3-97F7-EFCC051F454C}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 3
424
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.StartValuesController
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValuesModel
3
StartValuesModel
3
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.GUISettings
4
GUISettings
4
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Boolean
SetValuesOnMeasurementStart
True

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValue, Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
6
StartValues
6

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValueGroup, Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
7
StartValuesGroups
7
TypeRef:5
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:4
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.GUI.ColumnSettings, Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
8
ColumnSettings
9
TypeRef:5
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Array
_items
11
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValue
9
1
0
-1
Int32
_size
0
Int32
_version
0
--TextFormatter: End of Object--
TypeRef:7
7
Array
_items
12
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValueGroup
10
1
0
3
TypeRef:10
ArrayElement
13

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Object
11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
Int32
_size
1
Int32
_version
17
--TextFormatter: End of Object--
TypeRef:8
9
Array
_items
14
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.ColumnSettings
12
1
0
15
TypeRef:12
ArrayElement
15
TypeRef:12
ArrayElement
16
TypeRef:12
ArrayElement
17
TypeRef:12
ArrayElement
18
TypeRef:12
ArrayElement
19
TypeRef:12
ArrayElement
20
TypeRef:12
ArrayElement
21
TypeRef:12
ArrayElement
22
TypeRef:12
ArrayElement
23
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
TypeRef:11
ArrayElement
0
Int32
_size
9
Int32
_version
18
--TextFormatter: End of Object--
TypeRef:10
13
Int32
StartValueGroup_ID
0
String
StartValueGroup_Name
1
Start values group1
String
StartValueGroup_Comment
1

Boolean
StartValueGroup_Active
True
Boolean
StartValueGroup_AutoPersist
False
TypeRef:5
SerializationVersion
24
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
15
Int32
mHandle
0
Int32
mDefaultWidth
25
Int32
mWidth
25
Int32
SortOrderInt
0
Int32
mVisibleIndex
1
TypeRef:5
SerializationVersion
25
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
3
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:12
16
Int32
mHandle
1
Int32
mDefaultWidth
142
Int32
mWidth
142
Int32
SortOrderInt
0
Int32
mVisibleIndex
2
--TextFormatter: End of Object--
TypeRef:12
17
Int32
mHandle
2
Int32
mDefaultWidth
130
Int32
mWidth
130
Int32
SortOrderInt
0
Int32
mVisibleIndex
3
--TextFormatter: End of Object--
TypeRef:12
18
Int32
mHandle
3
Int32
mDefaultWidth
84
Int32
mWidth
84
Int32
SortOrderInt
0
Int32
mVisibleIndex
5
--TextFormatter: End of Object--
TypeRef:12
19
Int32
mHandle
4
Int32
mDefaultWidth
120
Int32
mWidth
120
Int32
SortOrderInt
0
Int32
mVisibleIndex
6
--TextFormatter: End of Object--
TypeRef:12
20
Int32
mHandle
5
Int32
mDefaultWidth
70
Int32
mWidth
70
Int32
SortOrderInt
0
Int32
mVisibleIndex
4
--TextFormatter: End of Object--
TypeRef:12
21
Int32
mHandle
6
Int32
mDefaultWidth
55
Int32
mWidth
55
Int32
SortOrderInt
0
Int32
mVisibleIndex
0
--TextFormatter: End of Object--
TypeRef:12
22
Int32
mHandle
7
Int32
mDefaultWidth
198
Int32
mWidth
198
Int32
SortOrderInt
0
Int32
mVisibleIndex
8
--TextFormatter: End of Object--
TypeRef:12
23
Int32
mHandle
8
Int32
mDefaultWidth
40
Int32
mWidth
40
Int32
SortOrderInt
0
Int32
mVisibleIndex
7
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
VStandaloneLoggingUserConfig 2 Begin_Of_Object
4
0
VLogCfgData 3 Begin_Of_Object
12
1
1
0
1
1
0
0
0
1024
60
1
0
0
3
0
4
1
1
0
2
0
0
3
VLogExportPersister 4 Begin_Of_Object
7
1416
11062249
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 4

End_Of_Serialized_Data 3
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Logging_.blf" 
0
0
1
30
80
0
1
1
1


<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Logging_{MeasurementIndex}.blf" 
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe" 
0
1
<VFileName V9 QL> 1 "..\..\..\..\..\Public\Documents\Vector\CANoe\15 (x64)\Templates\CANoe\Logging_{MeasurementIndex}.blf" 
0
71952ca4-ded1-4205-a3a3-008017f015f0
1
VLoggingComment 4 Begin_Of_Object
1
0
End_Of_Object VLoggingComment 4
End_Of_Object VLogCfgData 3
0
VAutoRunPreLoggingCaplBox 3 Begin_Of_Object
1
<VFileName V9 QL> 0 "" 
0
End_Of_Object VAutoRunPreLoggingCaplBox 3
0
VTriggerCfgData 3 Begin_Of_Object
5
1
0
0
1
0
0
0
0
1
0
0
0
200000
0
0
1
1
5000
VEvCondBlock 4 Begin_Of_Object
1
VEvCondGroup 5 Begin_Of_Object
2
VEvCondPrimitive 6 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 6
1
0
0
End_Of_Object VEvCondGroup 5
End_Of_Object VEvCondBlock 4
VEvCondBlock 4 Begin_Of_Object
1
VEvCondGroup 5 Begin_Of_Object
2
VEvCondPrimitive 6 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 6
1
0
0
End_Of_Object VEvCondGroup 5
End_Of_Object VEvCondBlock 4
0
0
0
116
1
0
0
1
0
0
0
0
0
0
0
End_Of_Object VTriggerCfgData 3
End_Of_Object VStandaloneLoggingUserConfig 2
Mapping::VMappingManager 2 Begin_Of_Object
4
1
Mapping::VMappingGroup 3 Begin_Of_Object
2
1
1
0
Static Mapping
1
Mapping::VMappingGroup 4 Begin_Of_Object
2
1
0
1
Group 1
0
0
0
End_Of_Object Mapping::VMappingGroup 4
0
0
End_Of_Object Mapping::VMappingGroup 3
0

End_Of_Object Mapping::VMappingManager 2
VTSystemControl 0
TestConfigurationSetup
VTestConfigurationSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 207 122 828 491
Test Setup for Test Units
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 207 122 828 491
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 207 122 828 491
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 207 122 828 491
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 207 122 828 491
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 207 122 828 491
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 207 122 828 491
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{EB390A16-0D8D-40A9-96E6-13FAD7C9B34F}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
35
APPDIR Vector.CANoe.TestConfigurationSetup.DLL
Vector.CANoe.TestConfigurationSetup, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.TestConfigurationSetup.TestConfigurationSetup
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
TestConfigurationSetupPersistence
1
0;-1;-1;
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VTestConfigurationSetupWrapper 2
AFDXVLStatisticSysVars
NAFDX::NStatisticsMonitor::VSVClient 2 Begin_Of_Object
1
Begin_Of_Multi_Line_String
2
﻿<?xml version="1.0" encoding="utf-8"?>
<systemvariables version="4" />
End_Of_Serialized_Data 2
End_Of_Object NAFDX::NStatisticsMonitor::VSVClient 2
DocumentViewer
VDocumentViewerWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 199 118 799 474
Documents
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{DDD8C00A-D258-4975-A7E4-BFE6E49C5F09}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
37
APPDIR Vector.CANalyzer.DocumentViewer.DLL
Vector.CANalyzer.DocumentViewer, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DocumentViewer.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
SplitterExpanded
True
Int32
DocumentListHeight
150
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
0
End_Of_Object VDocumentViewerWrapper 2
SVDialogSettings
VSVDialogSettings 2 Begin_Of_Object
1
-1
-1
930
600
1
1
0
320
440
365
0
0
0
0
0
End_Of_Object VSVDialogSettings 2
FunctionBusDialogSettings
VFunctionBusDialogSettings 2 Begin_Of_Object
2
-1
-1
1140
550
300
300
-1
0
End_Of_Object VFunctionBusDialogSettings 2
AutomationSequences
VAutomationSequencesWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 199 118 799 474
Automation Sequences
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 199 118 799 474
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{E4803F48-B1E4-4229-B264-EA14A6657F33}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
34
APPDIR Vector.CANalyzer.AutomationSequences.DLL
Vector.CANalyzer.AutomationSequences, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.AutomationSequences.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Int32
SelectedTabPage
0
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VAutomationSequencesWrapper 2
LogFileConverter
VLogFileConverter 2 Begin_Of_Object
1
2
VLogExportPersister 3 Begin_Of_Object
7
1416
78171113
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
<VFileName V9 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
1
3
2
19
0.10000000000000001
1
0
0
0
0

0
0
0
0
730
0
0.10000000000000001
0
0
1
End_Of_Object VLogExportPersister 3

End_Of_Serialized_Data 2
End_Of_Object VLogFileConverter 2
ThreadingSettings
VPersistentThreadingSettings 2 Begin_Of_Object
1
3
7
End_Of_Object VPersistentThreadingSettings 2
GlSignalSamplingSettings
GlLoggerConfig::VGlSignalSamplingSettings 2 Begin_Of_Object
1
0
End_Of_Object GlLoggerConfig::VGlSignalSamplingSettings 2
C2xSecurity
NC2xSec::VCar2xSecuritySettings 2 Begin_Of_Object
3
0
0
258
514
End_Of_Object NC2xSec::VCar2xSecuritySettings 2
NodeLayerConfiguration
32
APPDIR Vector.CANoe.NodeLayer.Configuration.Persistency.DLL
Vector.CANoe.NodeLayer.Configuration.Persistency, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeLayer.Configuration.Persistency.Persistor
1
1
String
NodeLayers
7
<ProjectVariantPropertyModel>
  <FileFormatVersion>3</FileFormatVersion>
  <ProjectVariantProperties>
    <VariantProperties />
    <ModifiableParameters />
  </ProjectVariantProperties>
</ProjectVariantPropertyModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
ILConfigurationComponent
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 118 801 473

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 118 801 473
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{74E65F1F-C0C2-4A79-B548-D71A99F411B6}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
47
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.ILConfigurationComponent
1
1
APPDIR Vector.CANoe.ILConfiguration.DLL
Vector.CANoe.ILConfiguration, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.ILConfiguration.GUI.GUISettings
2
GUISettings
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
Boolean
DbcSettingsAvailable
False
TypeRef:3
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
C2xCertificateExplorer
NC2xSec::VCertificateExplorer 2 Begin_Of_Object
0
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 273 105 1093 423
Car2x Certificate Explorer
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{1A696806-CECB-4766-B9E0-E6748795C940}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
31
APPDIR Vector.CANalyzer.Wlan.CertificateExplorer.DLL
Vector.CANalyzer.Wlan.CertificateExplorer, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Wlan.CertificateExplorer.ApplicationProxy
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object NC2xSec::VCertificateExplorer 2
ScenarioManager
NWlan::VCar2xScenarioManagerWnd 2 Begin_Of_Object
3
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 273 105 1093 423
Car2x Scenario Manager
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{D36D103F-638C-423A-8F1F-5DFB75F33C59}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
31
APPDIR Vector.CANalyzer.Wlan.ScenarioManager.DLL
Vector.CANalyzer.Wlan.ScenarioManager, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Wlan.ScenarioManagerProxy
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3

1

End_Of_Object NWlan::VCar2xScenarioManagerWnd 2
Multimedia
VMultimediaWrapper 2 Begin_Of_Object
1
0
End_Of_Object VMultimediaWrapper 2
FrameworkData
70
APPDIR Vector.CANalyzer.Framework.DLL
Vector.CANalyzer.Framework, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Framework.SerializationStore
1
1

System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.HashSet`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]
2
WindowFavorites
2

System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.HashSet`1[[Vector.CANalyzer.Framework.FavoritesManager+DialogCommandInfo, Vector.CANalyzer.Framework, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
3
DialogFavorites
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
Int32
Version
6

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.GenericEqualityComparer`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]
5
Comparer
5
Int32
Capacity
0
--TextFormatter: End of Object--
TypeRef:3
3
Int32
Version
6

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.ObjectEqualityComparer`1[[Vector.CANalyzer.Framework.FavoritesManager+DialogCommandInfo, Vector.CANalyzer.Framework, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null]]
6
Comparer
6
Int32
Capacity
0
--TextFormatter: End of Object--
TypeRef:5
5
--TextFormatter: End of Object--
TypeRef:6
6
--TextFormatter: End of Object--
VCommonMacroSettings
VCommonMacroSettings 2 Begin_Of_Object
1
3
YYYY-MM-DD_hh-mm-ss
End_Of_Object VCommonMacroSettings 2
EthernetSettings
NEthernet::VGlobalSettings 2 Begin_Of_Object
3
1
-1
1
End_Of_Object NEthernet::VGlobalSettings 2
VSymbolSelectionDialogSettings
VSymbolSelectionDialogSettings 2 Begin_Of_Object
1

End_Of_Object VSymbolSelectionDialogSettings 2
FunctionBusData
NFunctionBus::NDataModel::VFunctionBusData 2 Begin_Of_Object
8
0
NFunctionBus::NDataModel::VBindingConfiguration 3 Begin_Of_Object
1
4
Abstract
CAPL
C#
Mapping
End_Of_Object NFunctionBus::NDataModel::VBindingConfiguration 3
End_Of_Object NFunctionBus::NDataModel::VFunctionBusData 2
ITSObjectManager
NWlan::VITSStationManager 2 Begin_Of_Object
0
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 273 105 1093 423
ITS Object Manager
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 273 105 1093 423
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{931193EC-F2DB-4E8E-80A1-DB1DD8AE6963}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
31
APPDIR Vector.CANalyzer.Wlan.ITSObjectManager.DLL
Vector.CANalyzer.Wlan.ITSObjectManager, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Wlan.ObjectManagerProxy
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object NWlan::VITSStationManager 2
C2xGlobalOptions
NWlan::VCar2xGlobalOptions 2 Begin_Of_Object
1
3
0
1
0
0
0
End_Of_Object NWlan::VCar2xGlobalOptions 2
OfflineConfig
VOfflineConfigWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 255 167 1020 668
Offline Mode
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 255 167 1020 668
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 167 1020 668
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 167 1020 668
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 255 167 1020 668
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 167 1020 668
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 167 1020 668
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{C24A5A96-7AD2-459D-B97A-0B1747C76DB3}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
157
APPDIR Vector.CANalyzer.OfflineMode.GUI.DLL
Vector.CANalyzer.OfflineMode.GUI, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.OfflineMode.GUI.ComponentWrapper
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
OverallModel
123
<?xml version="1.0" encoding="utf-8"?>
<OfflineConfigOverallModel xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<DataModel xmlns="" i:type="OfflineConfigDataModel">
<LogFileMergeMode>ByMeasurementTime</LogFileMergeMode>
<RootGroup xmlns="" i:type="RootGroup">
<OfflineSources xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
<Id>9ca34fe5-42d7-4864-9126-3305ff4c42d5</Id>
<UserOffset>0</UserOffset>
</RootGroup>
<ChannelMappingSets xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
<DefaultChannelMappingSetId i:nil="true" />
<BreakOnCondition>false</BreakOnCondition>
<BreakpointOffset>PT0S</BreakpointOffset>
<BreakpointOffsetMode>NoBreak</BreakpointOffsetMode>
<TimeRangeMode>EntireRange</TimeRangeMode>
<TimeRangeStart i:nil="true" />
<TimeRangeEnd i:nil="true" />
</DataModel>
<GuiModel xmlns="" i:type="OfflineConfigGuiModel">
<ConfigDialogState xmlns="" i:type="ConfigDialogState">
<Left i:nil="true" />
<Top i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
</ConfigDialogState>
<FrontEndViewState xmlns="" i:type="FrontEndViewState">
<ColumnStates xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>IsActive</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>0</VisiblePosition>
<Width>56</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileTitle</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>1</VisiblePosition>
<Width>190</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>MeasurementStart</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>2</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>MeasurementEnd</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>3</VisiblePosition>
<Width>120</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>UserOffset</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>4</VisiblePosition>
<Width>100</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileFormat</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>5</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FormatVersion</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>6</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>DisplayTargetId</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>7</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileSize</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>8</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>UncompressedSize</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>9</VisiblePosition>
<Width>75</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileCreator</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>10</VisiblePosition>
<Width>140</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>ChannelMappingSet</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>11</VisiblePosition>
<Width>100</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>NumberOfObjects</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>12</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>IndexField</FieldName>
<IsVisible>false</IsVisible>
<VisiblePosition>13</VisiblePosition>
<Width>80</Width>
</d4p1:anyType>
<d4p1:anyType xmlns="" i:type="ColumnState">
<FieldName>FileName</FieldName>
<IsVisible>true</IsVisible>
<VisiblePosition>14</VisiblePosition>
<Width>227</Width>
</d4p1:anyType>
</ColumnStates>
<ExtensibleMetadataHeight>150</ExtensibleMetadataHeight>
<IsExtensibleMetadataExpanded>false</IsExtensibleMetadataExpanded>
</FrontEndViewState>
</GuiModel>
</OfflineConfigOverallModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VOfflineConfigWrapper 2
CanDbSettings
VCanDbSettings 2 Begin_Of_Object
1
255































































































































































































































































End_Of_Object VCanDbSettings 2
CANstressNGSettings
CANstressNG::VCANstressNGMgr 2 Begin_Of_Object
1
0
34
255
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
End_Of_Object CANstressNG::VCANstressNGMgr 2
FunctionBusSetup
VFunctionBusSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 255 165 1020 663
Function Bus Setup
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{2DD9F69E-3A97-4487-B148-00AA4CC8C591}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
106
APPDIR Vector.CANoe.FunctionBus.GUI.DLL
Vector.CANoe.FunctionBus.GUI, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANoe.FunctionBusSetup.FunctionBusSetupComponent
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
DataModel
72
<?xml version="1.0" encoding="utf-8"?>
<DataModel xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
<ViewDataModel xmlns="" i:type="ViewDataModel">
<ApplicationModelsViewState xmlns="" i:type="ApplicationModelsViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
</ApplicationModelsViewState>
<BindingViewState xmlns="" i:type="BindingViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SplitterPositionLeft i:nil="true" />
<SplitterPositionRight i:nil="true" />
<SplitterState i:nil="true" />
<ExpandedBindingBlockRawValue i:nil="true" />
<IsMaximized i:nil="true" />
</BindingViewState>
<CommunicationTimingsViewState xmlns="" i:type="CommunicationTimingsViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SelectedTabPageIndex i:nil="true" />
</CommunicationTimingsViewState>
<CommunicationViewState xmlns="" i:type="CommunicationViewState">
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
<SelectedViewIndex i:nil="true" />
</CommunicationViewState>
<TimingsViewState xmlns="" i:type="TimingsViewState">
<SplitterPosition i:nil="true" />
</TimingsViewState>
<DataSourcesViewState xmlns="" i:type="DataSourcesViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SplitterPosition i:nil="true" />
<SymbolSelectionControlState i:nil="true" />
</DataSourcesViewState>
<InterfacesViewState xmlns="" i:type="InterfacesViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</InterfacesViewState>
<ParticipantsViewState xmlns="" i:type="ParticipantsViewState">
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
</ParticipantsViewState>
<MainViewState xmlns="" i:type="MainViewState">
<SplitterPosition i:nil="true" />
</MainViewState>
<ParticipantSendModelViewState xmlns="" i:type="ParticipantSendModelViewState">
<SymbolSelectionControlState i:nil="true" />
<SplitterPosition i:nil="true" />
<X i:nil="true" />
<Y i:nil="true" />
<Width i:nil="true" />
<Height i:nil="true" />
<IsMaximized i:nil="true" />
</ParticipantSendModelViewState>
</ViewDataModel>
</DataModel>
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
5
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VFunctionBusSetupWrapper 2
ApplicationModelSetup
VApplicationModelSetup 2 Begin_Of_Object
1
0
End_Of_Object VApplicationModelSetup 2
EthernetPortBasedVLANSettings
NEthernet::VPortBasedVLANSettings 2 Begin_Of_Object
1
0
End_Of_Object NEthernet::VPortBasedVLANSettings 2
DiagnosticParameterWindow
VDiagnosticParameterWindowWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 255 165 1020 663
Diagnostic Parameters
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 255 165 1020 663
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{0208086D-D582-4DCD-BECE-ABB6BE4ED909}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
50
APPDIR Vector.CANalyzer.DiagnosticParameterWindow.DLL
Vector.CANalyzer.DiagnosticParameterWindow, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DiagnosticParameterWindow.DiagnosticParameterWindow
1
1
APPDIR CANw_Net.DLL
CANw_Net, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
String
ParameterWindowCtrlPersistence
1
-1;False;0,200,True;1,60,True;2,60,True;3,75,True;4,21,True;5,50,True;6,75,True;
APPDIR Vector.CANalyzer.DiagnosticParameterWindow.DLL
Vector.CANalyzer.DiagnosticParameterWindow, Version=15.2.41.0, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DiagnosticParameterWindow.Definitions.LastRecentSearchPopupItemList
3
RecentSearchItems
3
APPDIR Components\Vector.CANalyzer.Serialization\*******\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Int32
Version
1
Int32
Count
0
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VDiagnosticParameterWindowWrapper 2
ParticipantModelSetup
VParticipantModelSetup 2 Begin_Of_Object
2
0
0
End_Of_Object VParticipantModelSetup 2
SimSetupPorts
VSSGlobalPortList 2 Begin_Of_Object
5
0
1
0
0
0
0
End_Of_Object VSSGlobalPortList 2
VttTapClient
VttTapClientPersist 2 Begin_Of_Object
1
0
End_Of_Object VttTapClientPersist 2
VarCodeView
VVarCodeViewWrapperManager 2 Begin_Of_Object
1
0
End_Of_Object VVarCodeViewWrapperManager 2
ConnectivitySettings
Connectivity::VConnectivitySettings 2 Begin_Of_Object
7



0

0



0


1
0


0

0
4
End_Of_Object Connectivity::VConnectivitySettings 2
ErtSysVars
VErtSystemVariableManagerAnlyz 2 Begin_Of_Object
1
0
End_Of_Object VErtSystemVariableManagerAnlyz 2
PDUIGComponentManager
0
MGWSettings
VMGWSettings 2 Begin_Of_Object
1
0
End_Of_Object VMGWSettings 2
FunctionBusInteractiveStimulation
NFunctionBusInteractiveStimulation::VStimulationManager 2 Begin_Of_Object
1
0
End_Of_Object NFunctionBusInteractiveStimulation::VStimulationManager 2
End_Of_Object VGlobalConfiguration 1

VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: SOM Tester_Tool ESCL HU MCU VCU BMS00 Cluster TCU
VAL_TABLE_ Vtsig_Wifi_Connection_Status 3 "WIFI_CONNECTION_FAILURE" 2 "WIFI_CONNECTING" 1 "WIFI_CONNECTION_SUCCESSFUL" 0 "SNA" ;
VAL_TABLE_ Vtsig_Cruise_Control_Availabilit 3 "SNA" 2 "NOT_AVAILABLE_SPD_OOR" 1 "ACTIV" 0 "AVAILABLE_NOT_ACTV" ;
VAL_TABLE_ Vtsig_Driver_Mode_S1X 4 "REVERSE" 3 "HYPER" 2 "SPORTS" 1 "NORMAL" 0 "ECO" ;
VAL_TABLE_ Vtsig_Display_Screen_S1X 7 "Passcode & Charge Screen" 6 "BootUp Screen" 5 "Blank Screen" 4 "OTA_Screen" 3 "Drive_Screen" 2 "Charge_Screen" 1 "Parked_Screen" 0 "Passcode_Screen" ;
VAL_TABLE_ Vtsig_Passcode_Authin_Progress 2 "Completed" 1 "In_Progress" 0 "SNA" ;
VAL_TABLE_ Vtsig_BCM_SW_Version_Variant_Mis 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_MCU_SW_Variant_Mismach 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_SOM_BMS_Mismatch 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_BCM_BMS_Mismatch 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_Variant_Dipswitch_Mismatch 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_VIN_Variant_mismatch 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_Dipswitch_not_Sent_Error 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_Cell_Chemistry_SW_Mismatch 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_BMS_Board_Variant_SW_Misma 1 "True" 0 "False" ;
VAL_TABLE_ Vtsig_False_True 1 "True" 0 "False" ;
VAL_TABLE_ VtSIg_Enbl_Disbl 1 "Enable" 0 "Disable" ;


BO_ ********** VECTOR__INDEPENDENT_SIG_MSG: 0 Vector__XXX
 SG_ Scooter_Deliver_Flg : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1776 Acclrn_Time_for_Cycle: 8 VCU
 SG_ Acclrn_Time_for_Cycle2 : 8|8@1+ (1,0) [0|255] ""  HU
 SG_ Acclrn_Time_for_Cycle1 : 0|8@1+ (1,0) [0|255] ""  HU
 SG_ Range_Full_Charge_SOC : 40|16@1+ (1,0) [0|65535] ""  HU
 SG_ Range_Full_Charge_Dist : 24|16@1+ (1,0) [0|65535] ""  HU

BO_ 1049 BMS_Voltage_Out_Sense: 8 BMS00
 SG_ Rat_Bite_Counter : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ AI_PB13_12V_OUT_SENSE : 0|16@1+ (0.001,0) [0|65.535] "" Vector__XXX
 SG_ AI_PB12_3V3_SENSE : 16|16@1+ (0.001,0) [0|65.535] "" Vector__XXX

BO_ 1783 Move_OS_Tec_Pack_Status: 8 VCU
 SG_ Move_OS_Pack_Status : 0|8@1+ (1,0) [0|255] ""  BMS00,ESCL,HU,MCU,Tester_Tool

BO_ 1751 Current_Profile: 8 VCU
 SG_ Scooter_Deliver_Flg : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Current_Profile : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1750 TP_TCU_Mesage: 8 TCU
 SG_ BBW_setting_from_TCU : 16|2@1+ (1,0) [0|3] ""  BMS00
 SG_ Regen_setting_from_som : 4|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HyperModeAvailable : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SportModeAvailable : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TimeFenceBreached : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GeoFenceBreached : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 561 HU_Display_Message: 8 HU
 SG_ FC_Amount_Trigger : 16|1@1+ (1,0) [0|1] ""  VCU
 SG_ FC_Charging_Payment_Amount : 0|16@1+ (1,0) [0|65535] ""  VCU

BO_ 1056 DIY_Mode_Name: 8 TCU
 SG_ DIY_Mode : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  HU,Cluster

BO_ 1673 Vehicle_VIN_Info: 8 VCU
 SG_ Multiplexer_Sig_1 M : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VIN_Number_1to7 m1 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ VIN_Number_8to14 m2 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ VIN_Number_15to17 m3 : 8|24@1+ (1,0) [0|16777215] "" Vector__XXX

BO_ 553 MCU_Supply: 8 VCU
 SG_ MCU_12V_Supply : 0|1@1+ (1,0) [0|1] ""  Tester_Tool

BO_ 1089 Charger_Payment_Notification_State: 8 HU
 SG_ Charger_Payment : 0|5@1+ (1,0) [0|31] ""  VCU

BO_ 1081 Hyper_Charger_Due: 8 HU
 SG_ Due_Status : 0|4@1+ (1,0) [0|15] ""  VCU
 SG_ Veh_Connectivity : 4|1@1+ (1,0) [0|1] ""  VCU

BO_ 1044 Spike_Status: 8 BMS00
 SG_ VOOR_cutoff_flg_cspike : 1|1@1+ (1,0) [0|1] ""  ESCL,HU,MCU,Tester_Tool,VCU
 SG_ cspike_fault : 0|1@1+ (1,0) [0|1] ""  ESCL,HU,MCU,Tester_Tool,VCU

BO_ 1552 Vehicle_Hardware_Variant: 8 VCU
 SG_ Motor_Hardware_variant : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Charger_Hardware_variant : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ MCU_Hardware_variant : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BMS_DipSwitch_Variant : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BMS_Hardware_variant : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BCM_Hardware_variant : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1047 BMS_info_1: 8 BMS00
 SG_ Reserve : 32|32@1+ (1,0) [0|4294967295] ""  VCU
 SG_ BMS_Unique_Part_Number : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 793 MCU_Unique_ID: 8 MCU
 SG_ Reserve : 32|32@1+ (1,0) [0|4294967295] ""  VCU
 SG_ MCU_unique_part_number : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 1656 SOM_Hardware: 8 VCU
 SG_ SOM_Vin_Mismatch : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Display_Hardware_variant : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SOM_Hardware_variant : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1775 WIFI_BT_SW_version_info: 8 VCU
 SG_ WIFI_BT_SW_version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1774 TCU_SW_Version_info: 8 VCU
 SG_ TCU_SW_version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1746 Gps_Satellite_Data: 8 VCU
 SG_ WiFi_State : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ LTE_State : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Number_of_satellites_in_use : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Number_of_satellites : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ GPS_Fix : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1745 VCU_Features: 8 VCU
 SG_ ALL_WAKEUP : 6|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Reserved_Debug8 : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reserved_Debug7 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reserved_Debug6 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reserved_Debug5 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reserved_Debug4 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reserved_Debug3 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reserved_Debug2 : 10|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ Reserved_Debug1 : 0|6@1+ (1,0) [0|63] "" Vector__XXX

BO_ 1080 TIME: 8 VCU
 SG_ Minutes : 56|8@1+ (1,0) [0|60] "mins" Vector__XXX
 SG_ Hours : 48|8@1+ (1,0) [0|24] "h" Vector__XXX

BO_ 1687 BLE_Version: 8 VCU
 SG_ BLE_Version_Data2 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_Version_Data1 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_Version_Data0 : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1623 Custom_mode_feature_BLE: 8 VCU
 SG_ ParkAssist_Enable : 24|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Max_speed : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Regen_setting : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Throttle_sensitivity_setting : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Torque_Value : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1686 BLE_MAC_Address: 8 VCU
 SG_ BLE_MAC_Address_Data7 : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data6 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data5 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data4 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data3 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data2 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data1 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BLE_MAC_Address_Data0 : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1624 Custom_mode_Enable_BLE: 8 VCU
 SG_ Custom_mode_Status : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1743 State_S1X: 8 VCU
 SG_ Error_Code : 4|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Diag_Server_State : 1|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ Error_State : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1684 Hotspot_Passcode: 8 VCU
 SG_ ECOS_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1643 Music_S1X: 8 VCU
 SG_ Track_name : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ Music_operation : 6|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ Total_packet_number_music : 3|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ Current_packet_number_music : 0|3@1+ (1,0) [0|7] "" Vector__XXX

BO_ 1741 MCU_Regen_Info: 8 VCU
 SG_ REGEN_info_sharing : 0|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1683 TEXT: 8 VCU
 SG_ Control_Byte : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1742 Display_Sw_Version_Info: 8 VCU
 SG_ Display_Sw_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1644 Calling_S1X: 8 VCU
 SG_ Current_Packet_Number : 0|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ Contact_Details : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ Contact : 6|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ Total_Packet_Number : 3|3@1+ (1,0) [0|7] "" Vector__XXX

BO_ 1706 ACK_Passcode_Cmd: 8 VCU
 SG_ Passcode_Change_Request : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1705 Passcode_Cmd_From_BLE: 8 VCU
 SG_ Passcode_Profile_ID : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_data6 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_data5 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_data4 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_data3 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_data2 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_data1 : 8|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1739 Vehicle_Mode_S1X: 8 VCU
 SG_ BLE_Connection_Status_S1X : 56|2@1+ (1,0) [0|3] ""  HU
 SG_ Current_Distance_Covered : 40|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ Cruise_Control_Availability_S1X : 36|4@1+ (1,0) [0|15] ""  HU
 SG_ HMI_StatusBar_Stt_S1X : 32|4@1+ (1,0) [0|15] ""  HU
 SG_ Odometer_S1X : 8|24@1+ (1,0) [0|16777215] ""  HU
 SG_ Driver_Mode_S1X : 4|4@1+ (1,0) [0|15] ""  HU
 SG_ Display_Screen_S1X : 0|4@1+ (1,0) [0|15] ""  HU

BO_ 1738 S1X_Passcode_Authentication: 8 VCU
 SG_ Current_Position : 48|3@1+ (1,0) [0|7] ""  HU
 SG_ Passcode_Authin_Progress : 51|2@1+ (1,0) [0|3] ""  HU
 SG_ bcm_To_Som_PasscodeAuth_5 : 40|8@1+ (1,0) [0|255] ""  HU
 SG_ bcm_To_Som_PasscodeAuth_4 : 32|8@1+ (1,0) [0|255] ""  HU
 SG_ bcm_To_Som_PasscodeAuth_3 : 24|8@1+ (1,0) [0|255] ""  HU
 SG_ bcm_To_Som_PasscodeAuth_2 : 16|8@1+ (1,0) [0|255] ""  HU
 SG_ bcm_To_Som_PasscodeAuth_1 : 8|8@1+ (1,0) [0|255] ""  HU
 SG_ bcm_To_Som_PasscodeAuth_0 : 0|8@1+ (1,0) [0|255] ""  HU

BO_ 1711 S1X_Passcode_Ack: 8 SOM
 SG_ Reattempt_Timer_Seconds : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reattempt_Timer_Minutes : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Passcode_count_Remaining : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Som_To_Bcm_PasscodeAck : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1707 Navigation_Direction_Info: 8 VCU
 SG_ Navigation_Direction_Info_distance_units : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Navigation_Direction_Info_distance : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ Navigation_Direction_Info_direction_type : 32|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1642 Temperature_Sensing_Max: 8 VCU
 SG_ AI_LED_NTC_PLUS_MAX : 30|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_HB_NTC_PLUS_MAX : 24|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_TEMP_SENSOR_MEASURE_MAX : 8|8@1+ (1,-50) [-50|205] "" Vector__XXX
 SG_ AI_TEMP_SENSOR_MEASURE_HBLB_MAX : 16|8@1+ (1,-50) [-50|205] "" Vector__XXX
 SG_ AI_DC_DC_TEMP_MEASURE_MAX : 0|8@1+ (1,-50) [-50|205] "" Vector__XXX

BO_ 1563 Temperature_Sensing_Min: 8 VCU
 SG_ AI_LED_NTC_PLUS_MIN : 32|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_HB_NTC_PLUS_MIN : 24|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_TEMP_SENSOR_MEASURE_MIN : 16|8@1+ (1,-50) [-50|205] "" Vector__XXX
 SG_ AI_TEMP_SENSOR_MEASURE_HBLB_MIN : 8|8@1+ (1,-50) [-50|205] "" Vector__XXX
 SG_ AI_DC_DC_TEMP_MEASURE_MIN : 0|8@1+ (1,-50) [-50|205] "" Vector__XXX

BO_ 1631 Voltage_Sensing_Max: 8 VCU
 SG_ DISPLAY_SUPPLY_21V_MAX : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_SUPPLY_48V_MAX : 35|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ AI_BMS_DCDC_SENSE_MAX : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_5V_SENSE_MAX : 32|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ AI_3V8_HIGH_SENSE_MAX : 24|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ AI_1V8_HIGH_SENSE_MAX : 16|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ AI_12V_MEASURE_MAX : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_HIGH_MEASURE_MAX : 4|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_FC_SENSE_MAX : 0|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 1630 Voltage_Sensing_Min: 8 VCU
 SG_ AI_BMS_DCDC_SENSE_MIN : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_MEASURE_MIN : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_HIGH_MEASURE_MIN : 4|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_FC_SENSE_MIN : 0|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ DISPLAY_SUPPLY_21V_MIN : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_SUPPLY_48V_MIN : 35|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ AI_5V_SENSE_MIN : 32|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ AI_3V8_HIGH_SENSE_MIN : 24|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ AI_1V8_HIGH_SENSE_MIN : 16|8@1+ (0.1,0) [0|25.5] "" Vector__XXX

BO_ 1567 Motor_Sense: 8 VCU
 SG_ SEAT_SENSOR : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESCL_Sensor_2 : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESCL_Sensor_1 : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AI_ESCL_CUR_SEN_HBRIDGE : 6|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_SEATLOCK_CUR_SEN_HBRIDGE : 0|6@1+ (0.1,0) [0|6.3] "" Vector__XXX

BO_ 1566 Temperature_Sensing_Avg: 8 VCU
 SG_ AI_LED_NTC_PLUS_AVG : 32|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_TEMP_SENSOR_MEASURE_HBLB_AVG : 16|8@1+ (1,-50) [-50|205] "" Vector__XXX
 SG_ AI_TEMP_SENSOR_MEASURE_AVG : 8|8@1+ (1,-50) [-50|205] "" Vector__XXX
 SG_ AI_HB_NTC_PLUS_AVG : 24|6@1+ (0.1,0) [0|6.3] "" Vector__XXX
 SG_ AI_DC_DC_TEMP_MEASURE_AVG : 0|8@1+ (1,-50) [-50|205] "" Vector__XXX

BO_ 1564 Voltage_Sensing_Avg: 8 VCU
 SG_ DISPLAY_SUPPLY_21V_AVG : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_SUPPLY_48V_AVG : 35|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ AI_BMS_DCDC_SENSE_AVG : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_5V_SENSE_AVG : 32|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ AI_3V8_HIGH_SENSE_AVG : 24|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ AI_1V8_HIGH_SENSE_AVG : 16|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ AI_12V_MEASURE_AVG : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_HIGH_MEASURE_AVG : 4|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AI_12V_FC_SENSE_AVG : 0|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 1646 VCU_Faults: 8 VCU
 SG_ VIN_Variant_mismatch : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Variant_Dipswitch_Mismatch : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_BMS_Mismatch : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MCU_SW_Variant_Mismatch : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Dipswitch_not_Sent_Error : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Cell_Chemistry_SW_Mismatch : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Board_Variant_SW_Mismatch : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_SW_Version_Variant_Mismatch : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_BMS_Mismatch : 5|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1645 magnetometer_2: 8 VCU
 SG_ Magnetometer_Zaxis : 0|32@1- (0.01,-360) [-360|360] "deg" Vector__XXX

BO_ 1629 Magnetometer_1: 8 VCU
 SG_ Magnetometer_Yaxis : 32|32@1- (0.01,-360) [-360|360] "deg" Vector__XXX
 SG_ Magnetometer_Xaxis : 0|32@1- (0.01,-360) [-360|360] "deg" Vector__XXX

BO_ 1611 VCU_Data1: 8 VCU
 SG_ VCU_SOM_Temperature : 16|16@1+ (0.1,-50) [-50|6503.5] "" Vector__XXX
 SG_ VCU_Temperature : 0|16@1+ (0.1,-50) [-50|6503.5] "" Vector__XXX

BO_ 550 MCU_Data_6: 8 MCU
 SG_ Synchronous_motor_angle : 16|16@1+ (1,0) [0|360] ""  VCU
 SG_ MCU_HH_Activation_Counter : 0|16@1+ (1,0) [0|65535] "Count"  VCU

BO_ 1218 ESCL_Details: 8 ESCL
 SG_ ESCL_Power_Status : 48|1@1+ (1,0) [0|1] ""  VCU
 SG_ ESCL_Version : 0|48@1+ (1,0) [0|281474976710655] ""  VCU

BO_ 1937 ESCL_Diagnostic_Response: 8 ESCL
 SG_ ESCL_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1936 ESCL_Diagnostic_Request: 8 VCU
 SG_ ESCL_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  ESCL

BO_ 1766 Data_Analytics_Over_Cloud: 8 VCU
 SG_ SOH_RNG_MUL_FCTR : 1|8@1+ (1,0) [0|100] "%" Vector__XXX
 SG_ Cruise_Deactivation_Speed : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1956 UARTCAN_Receive: 8 VCU
 SG_ UARTCAN_Rx_Queue_Overflow : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ UARTCAN_Rx_Fail : 32|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 1955 Queue_Overflow: 8 VCU
 SG_ UARTCAN_Tx_Queue_Overflow : 32|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ VehCAN_receiver_Queue_Overflow : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 1954 Veh_CanOverflow: 8 VCU
 SG_ Queue_transmission_Overflow : 32|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ CAN_Queue_Transmission_Fail_Count : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 1953 Fail_Count_CAN_Transmission: 8 VCU
 SG_ CAN_Transmission_FailCount : 32|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ Diag_CAN_Transmission_Fail_Count : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 276 Throttle_Calibration_Message: 8 MCU
 SG_ Drive_Start_Throttle_Count : 16|16@1+ (1,0) [0|16384] "Count"  VCU
 SG_ IDLE_Throttle_Count : 0|16@1+ (1,0) [0|16384] "Count"  VCU

BO_ 277 Regen_Set_Percentage: 8 MCU
 SG_ Coast_Regen_Percentage : 16|16@1+ (0.1,0) [0|100] "%"  VCU
 SG_ Brake_Regen_Percentage : 32|16@1+ (0.1,0) [0|100] "%"  VCU
 SG_ Forced_Regen_Percentage : 0|16@1+ (0.1,0) [0|100] "%"  VCU

BO_ 1669 Gyroscope_Reading: 8 VCU
 SG_ Gyro_X_Theta : 0|32@1+ (0.1,-45) [-45|65490] "deg" Vector__XXX

BO_ 1668 Deration_Message: 2 VCU
 SG_ Deration_Motor : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Deration_MCU : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 430 DCIR_Cell_4: 8 BMS00
 SG_ Cell_DCIR_14 : 16|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_13 : 0|16@1+ (1,0) [0|65535] "uohm"  VCU

BO_ 429 DCIR_Cell_3: 8 BMS00
 SG_ Cell_DCIR_12 : 48|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_11 : 32|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_10 : 16|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_9 : 0|16@1+ (1,0) [0|65535] "uohm"  VCU

BO_ 428 DCIR_Cell_2: 8 BMS00
 SG_ Cell_DCIR_8 : 48|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_7 : 32|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_6 : 16|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_5 : 0|16@1+ (1,0) [0|65535] "uohm"  VCU

BO_ 427 DCIR_Cell_1: 8 BMS00
 SG_ Cell_DCIR_4 : 48|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_3 : 32|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_2 : 16|16@1+ (1,0) [0|65535] "uohm"  VCU
 SG_ Cell_DCIR_1 : 0|16@1+ (1,0) [0|65535] "uohm"  VCU

BO_ 1654 VCU_Debug_Msg3: 8 VCU
 SG_ VCU_Signal_Debug3_3 : 32|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ VCU_Signal_Debug3_2 : 16|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ VCU_Signal_Debug3_1 : 0|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1653 VCU_Debug_Msg2: 8 VCU
 SG_ VCU_Feature_Debug2_3 : 48|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ VCU_Feature_Debug2_2 : 32|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ VCU_Feature_Debug2_1 : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 1665 User_Feature_Selection: 8 VCU
 SG_ Regeneration_Command : 1|1@1+ (1,0) [0|1] ""  MCU
 SG_ Hill_Hold_Command : 0|1@1+ (1,0) [0|1] ""  MCU

BO_ 1703 Ack_From_HMI_for_BLE: 8 HU
 SG_ Ack_From_HMI_for_BLE : 0|8@1+ (1,0) [0|255] ""  VCU

BO_ 1702 OLA_Maps_POI_Name: 8 VCU
 SG_ POI_Name_23to24 m3 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ POI_Name_15to22 m2 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ POI_Name_8to14 m1 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ POI_Name_1to7 m0 : 8|56@1+ (1,0) [0|7.20575940379279E+016] "" Vector__XXX
 SG_ POI_Multiplexer M : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1622 Vehicle_Monitor_Mode: 8 VCU
 SG_ Parts_mismatch_speed_limit : 56|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Parts_mismatch_odo_limit : 48|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ service_speed_limit : 40|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ service_odo_limit : 32|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ in_purchase_speed_limit : 24|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ in_purchase_odo_limit : 16|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Monitor_Mode_Part_Mismatch : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Monitor_Mode_Job_Open : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Monitor_Mode_In_Purchase : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ MODE_Restriction : 0|8@1+ (1,0) [0|255] ""  Tester_Tool

BO_ 1701 Ola_Maps_Longitude_command: 8 VCU
 SG_ Long_data7 : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data6 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data5 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data4 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data3 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data2 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data1 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Long_data0 : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1700 Ola_Maps_Latitude_command: 8 VCU
 SG_ Lat_data7 : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data6 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data5 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data4 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data3 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data2 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data1 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Lat_data0 : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1681 BCM_BLEto_SOM: 8 VCU
 SG_ Data5_BCM_BLEto_SOM : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data4_BCM_BLEto_SOM : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data3_BCM_BLEto_SOM : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data2_BCM_BLEto_SOM : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data1_BCM_BLEto_SOM : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data0_BCM_BLEto_SOM : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Acknowledgement_BCM_BLEto_SOM : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ CommandType_BCM_BLEto_SOM : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1680 SOM_BLEto_BCM: 8 HU
 SG_ Data5_SOM_BLEto_BCM : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data4_SOM_BLEto_BCM : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data3_SOM_BLEto_BCM : 40|8@1+ (1,0) [0|255] ""  VCU
 SG_ Data2_SOM_BLEto_BCM : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data1_SOM_BLEto_BCM : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Data0_SOM_BLEto_BCM : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Acknowledgement_SOM_BLEto_BCM : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ CommandType_SOM_BLEto_BCM : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1667 Vehicle_Safety_State: 8 VCU
 SG_ Hill_Hold_Availability : 12|4@1+ (1,0) [0|15] ""  HU
 SG_ Regen_Availability : 8|4@1+ (1,0) [0|15] ""  HU
 SG_ Tow_Theft_Detection : 4|4@1+ (1,0) [0|15] ""  HU
 SG_ Fall_Detection_State : 0|4@1+ (1,0) [0|15] ""  HU

BO_ 1666 Vehicle_Dynamics: 8 VCU
 SG_ Cruise_Delta_Spd : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Delta_Throttle_Speed : 40|16@1+ (1,0) [0|65535] ""  Tester_Tool
 SG_ Accident_Severity_Sts : 34|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Adaptive_Power_Boost_Request : 33|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Right_Turn_Completion_Status : 32|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Left_Turn_Completion_Status : 31|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Hard_Reboot_Disable : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Fall_Detected_L2_Phase : 29|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Fall_Detected_L1_Phase : 28|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Motor_Cutoff_Status_of_Left_Brake : 25|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Fall_Set_Reset_TimerFlag : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Motor_Cutoff_Status_of_Right_Brake : 26|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Hill_Hold_Request : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Vehicle_Acceleration : 16|8@1- (0.1,0) [-12.8|12.7] "" Vector__XXX
 SG_ Vehicle_Resultant_Inclination : 0|16@1- (0.1,-180) [-180|180] "deg" Vector__XXX

BO_ 1625 SOH_Signals: 8 BMS00
 SG_ SOH_update_index : 48|4@1+ (1,0) [0|10] ""  VCU
 SG_ SOH_Array5 : 40|8@1+ (1,0) [0|100] "%"  VCU
 SG_ SOH_Array4 : 32|8@1+ (1,0) [0|100] "%"  VCU
 SG_ SOH_Array3 : 24|8@1+ (1,0) [0|100] "%"  VCU
 SG_ SOH_Array2 : 16|8@1+ (1,0) [0|100] "%"  VCU
 SG_ SOH_Array1 : 8|8@1+ (1,0) [0|100] "%"  VCU
 SG_ Cycle_SOH : 0|8@1+ (1,0) [0|100] "%" Vector__XXX

BO_ 1652 VCU_Debug_Msg1: 8 VCU
 SG_ Gyroscope_X_Prctle : 0|16@1- (1,0) [-32768|32767] "" Vector__XXX
 SG_ VCU_Debug6 : 42|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VCU_Debug5 : 34|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VCU_Bool_Debug4 : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ VCU_Bool_Debug3 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ VCU_Debug2 : 16|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1651 BMS_Debug_Msg: 8 BMS00
 SG_ BMS_Debug6 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BMS_Debug5 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ BMS_Bool_Debug4 : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Bool_Debug3 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Debug2 : 16|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ BMS_Debug1 : 0|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1632 Vehicle_Config_Data_8: 8 VCU
 SG_ Vacation_Mode : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Motor_Cutoff_Delay : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Auto_Indicator_Turnoff_Feature : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Hazard_Light_Feature_Enable : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Auto_Head_Light_Handling : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Smart_DRL_Light : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Smart_Head_Light : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Hill_Hold_Feature_Enable : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Proximity_Lock_Unlock : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Tow_Tamper_Detection : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Fall_Detection : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Boost_Mode : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Take_me_home_lights_API : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Tow_protection_API : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HIL_HOLD_FEATURE_ENABLE : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Early_Beta_Access : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ New_Upgrades : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Mode_Fencing : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Time_Fencing : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Geo_Fencing : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Proximity_L_U : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Energy_Insights : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TP_HyperCharge : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TP_FasterCharge : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Advanced_Regen : 12|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1591 VCU_BL_Info: 4 VCU
 SG_ VCU_Bootloader_Info : 0|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 1590 BMS00_BL_Info: 4 BMS00
 SG_ BMS00_Bootloader_Info : 0|4@1+ (1,0) [0|15] ""  VCU

BO_ 1699 BLE_Status_Message: 8 VCU
 SG_ BLE_Status : 0|16@1+ (1,0) [0|65535] ""  Tester_Tool

BO_ 853 Display_Info_3: 8 BMS00
 SG_ Regen_Availability_Flag : 0|1@1+ (1,0) [0|1] ""  VCU

BO_ 1621 BMS_Configuration_Info7: 8 BMS00
 SG_ SLC_CNT_CHRG_RESTART : 32|8@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SLC_MV_MINVG_STRTCELLBAL : 40|16@1+ (1,0) [0|5000] "V" Vector__XXX
 SG_ SOC_value_from_minimum_OCV : 0|16@1+ (0.01,0) [0|655.36] "" Vector__XXX
 SG_ SOC_debug_1 : 16|16@1+ (0.01,0) [0|655.36] "" Vector__XXX

BO_ 1610 Vehicle_miscellaneous_info: 8 VCU
 SG_ HH_RollBckDist : 8|8@1+ (1,0) [0|255] "cm"  Tester_Tool
 SG_ Scooter_Toppled : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Trunk_Unlock_Failure : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Veh_Low_Power_Management_State : 0|3@1+ (1,0) [0|4] ""  Tester_Tool

BO_ 1620 BMS_Configuration_Info6: 8 BMS00
 SG_ ACC_i_MAXDSCHCURRLIM_12P : 48|9@1+ (1,0) [0|300] "" Vector__XXX
 SG_ SAF_PCT_SOCREGENALLOWED : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_DEGC_REGENALLOWED : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SLC_A_OVERCURR_TOLCHG : 24|8@1+ (0.1,0) [0|25.5] "" Vector__XXX
 SG_ SAF_CNT_ERRORCNTR_SHRTCRCT : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_CNT_OVERVLTGCHRGPERM : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_CNT_ERRORCNTR_CELLDIP : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 100 Node_Absent_Flags: 8 VCU
 SG_ HBC_Node_Absent_Status : 4|1@1+ (1,0) [0|1] ""  BMS00
 SG_ BLE_Node_Absent_Status : 5|1@1+ (1,0) [0|1] ""  BMS00
 SG_ BCM_Node_Absent_Status : 6|1@1+ (1,0) [0|1] ""  BMS00
 SG_ ESCL_Node_Absent_Status : 2|1@1+ (1,0) [0|1] ""  BMS00
 SG_ MCU_Node_Absent_Status : 1|1@1+ (1,0) [0|1] ""  BMS00
 SG_ HU_Node_Absent_Status : 3|1@1+ (1,0) [0|1] ""  BMS00
 SG_ BMS_Node_Absent_Status : 0|1@1+ (1,0) [0|1] ""  BMS00

BO_ 544 BMS_Configuration_Info5: 8 BMS00
 SG_ SLC_MIN_CVEND_TIMER : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ACC_i_MAXDSCHCURRLIM_16P : 48|9@1+ (1,0) [0|300] "" Vector__XXX
 SG_ SAF_MV_COMPSPIKETHD : 37|11@1+ (1,0) [0|2000] "" Vector__XXX
 SG_ SAF_MV_CELLDIPTHDDELTA : 27|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISESLEEP : 23|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_SLEEPWARNG : 16|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ BCD_DEGC_THSDTEMP_PDU_CUTOFF : 0|8@1+ (1,0) [0|150] "" Vector__XXX

BO_ 537 BMS_Configuration_Info4: 8 BMS00
 SG_ BCD_DEGC_THSDTEMP_PDUWARNG : 36|8@1+ (1,0) [0|150] "" Vector__XXX
 SG_ BCD_DEGC_THSDTEMP_PDUERROR : 52|8@1+ (1,0) [0|150] "" Vector__XXX
 SG_ BCD_DEGC_DIFFTEMP_PDUWARNG : 44|8@1+ (1,0) [0|150] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_CHGERROR : 29|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISEDRV : 25|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_DRVERROR : 11|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_CHGWARNG : 18|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ SAF_DEGC_TEMPRISECHRG : 7|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_DRVWARNG : 0|7@1+ (1,0) [0|100] "" Vector__XXX

BO_ 536 Vehicle_Config_Data_7: 8 VCU
 SG_ MOTOR_ERR_PARK_SET_TEMP : 40|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MOTOR_WARN_NORMAL_SET_TEMP : 0|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MOTOR_WARN_NORMAL_RESET_TEMP : 10|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MOTOR_WARN_CHILL_SET_TEMP : 20|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MOTOR_WARN_CHILL_RESET_TEMP : 30|10@1+ (1,0) [0|600] "" Vector__XXX

BO_ 535 Vehicle_Config_Data_6: 8 VCU
 SG_ MCU_ERR_PARK_SET_TEMP : 40|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MCU_WARN_NORMAL_SET_TEMP : 0|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MCU_WARN_NORMAL_RESET_TEMP : 10|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MCU_WARN_CHILL_SET_TEMP : 20|10@1+ (1,0) [0|600] "" Vector__XXX
 SG_ MCU_WARN_CHILL_RESET_TEMP : 30|10@1+ (1,0) [0|600] "" Vector__XXX

BO_ 1992 HU_Diagnostic_Response: 8 HU
 SG_ HU_Diag_Response : 0|64@1+ (1,0) [0|0] ""  Tester_Tool

BO_ 1987 HU_Diagnostic_Request: 8 Tester_Tool
 SG_ HU_Diag_Request : 0|64@1+ (1,0) [0|0] ""  HU

BO_ 324 Vehicle_Lock_State_BCM: 8 VCU
 SG_ Vehicle_state_BCM_ULock : 0|3@1+ (1,0) [0|7] ""  HU

BO_ 549 MCU_Data_5: 8 MCU
 SG_ Regen_torque_Percent : 41|16@1+ (0.1,0) [0|3000] "" Vector__XXX
 SG_ Live_Z_pulse_counter : 21|20@1+ (1,0) [0|999999] "" Vector__XXX
 SG_ Live_PWM_rotor_angle : 12|9@1+ (1,0) [0|360] "" Vector__XXX
 SG_ Live_Position_counter : 0|12@1+ (1,0) [0|2048] "" Vector__XXX

BO_ 548 MCU_Data_4: 8 MCU
 SG_ Vehicle_Dir_flag : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AC_Current_MCU : 47|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ DC_Current_MCU : 37|10@1+ (1,0) [0|1000] "" Vector__XXX
 SG_ Bus_Voltage_MCU : 30|7@1+ (1,0) [0|100] "" Vector__XXX
 SG_ Rotor_Angle : 21|9@1+ (1,0) [0|360] "" Vector__XXX
 SG_ Position_Counter : 10|11@1+ (1,0) [0|2048] "" Vector__XXX
 SG_ Motor_Frequency : 0|10@1+ (1,0) [0|1000] "" Vector__XXX

BO_ 820 MCU_Fault_Data2: 8 MCU
 SG_ PWMMissingFault : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZIPulse_Missing_Fault : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Pulse_Missing_Num : 11|12@1+ (1,0) [0|2048] "" Vector__XXX
 SG_ AB_Pulse_Mism_ZIPulse_Fault : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Pos_Cnt_Mism_PWM_Val : 1|9@1+ (1,0) [0|360] "" Vector__XXX
 SG_ Pos_Cnt_Mism_PWM_Fault : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1619 BMS_Configuration_Info3: 8 BMS00
 SG_ SAF_CNT_ERRORCNTR_COMPSPIKE : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SAF_DEGC_OVERTEMP_CutOff : 48|8@1+ (1,0) [0|255] "degC" Vector__XXX
 SG_ SAF_DEGC_TEMPRISEPDU : 40|8@1+ (0.1,0) [0|25.5] "degC" Vector__XXX
 SG_ SOC_AH_USECAP_CELL : 26|12@1+ (0.01,0) [0|40.95] "" Vector__XXX
 SG_ SLC_MV_THDVTG_CHRGEND_CELL : 13|13@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SLC_MV_THDVTG_CCEND_CELL : 0|13@1+ (1,0) [0|4500] "" Vector__XXX

BO_ 1618 BMS_Configuration_Info2: 8 BMS00
 SG_ SAF_MV_UNDERVG_WARNG : 50|12@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_UNDERVG_RESM : 38|12@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_UNDERVG_ERROR : 26|12@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_CHG_OVERVG_HEAL : 13|13@1+ (1,0) [0|4500] "" Vector__XXX
 SG_ SAF_MV_CHG_OVERVG_ERROR : 0|13@1+ (1,0) [0|4500] "" Vector__XXX

BO_ 850 Display_Info_4: 8 BMS00
 SG_ SOH_Disp : 16|8@1+ (1,0) [0|100] "%"  BMS00,HU
 SG_ Display_SoC : 0|16@1+ (1,0) [0|100] "%"  HU,VCU

BO_ 401 BMS00_Parameter_8: 8 BMS00
 SG_ Aggregated_current_Ah : 32|32@1- (1E-006,0) [-2147.483648|2147.483647] "Ah"  VCU
 SG_ Calculated_current_based_on_coulomb : 0|32@1- (1E-006,0) [-2147.483648|2147.483647] "Amp"  VCU

BO_ 852 Display_Info_2: 8 VCU
 SG_ Display_Speedometer : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1682 Speaker_Sound_Control: 8 VCU
 SG_ Speaker_Lock_Failure_Sound : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Swipe_Lock_Sound : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Lock_Sound : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Alarm_Sts : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Find_My_Bike_Sound : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Unlock_Failure_Sound : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Unlock_Sound : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Seat_Unlock_Sound : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Reverse_Parking_Sound : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1697 CommandFromApp: 8 VCU
 SG_ Command_From_APP : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1698 CommandFromBLE: 8 VCU
 SG_ Command_From_BLE : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1780 Vehicle_Range: 8 VCU
 SG_ Vehicle_Range_Hyper : 48|16@1+ (1,0) [0|65535] "Km" Vector__XXX
 SG_ Vehicle_Range_Normal : 32|16@1+ (1,0) [0|65535] "Km" Vector__XXX
 SG_ Vehicle_Range_Sport : 16|16@1+ (1,0) [0|65535] "Km" Vector__XXX
 SG_ Vehicle_Range_Limp : 0|16@1+ (1,0) [0|65535] "Km" Vector__XXX

BO_ 1740 Vehicle_HMI_State: 8 VCU
 SG_ Navigation_Status : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_State : 8|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Passcode_Auth_Status : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1724 CommandFromTCU: 8 VCU
 SG_ Wifi_Connection_Status : 32|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ OTA_Installation_Progress : 16|8@1+ (1,0) [0|100] ""  Tester_Tool
 SG_ OTA_Installation_Message : 24|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Command_From_TCU : 0|16@1+ (1,0) [0|65535] ""  Tester_Tool

BO_ 1572 MCU_BL_Version_Info: 8 MCU
 SG_ MCU_Bootloader_Version_Gtake : 32|16@1+ (1,0) [0|65535] ""  VCU
 SG_ MCU_Bootloader_Version : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 1650 OTA_Status: 8 VCU
 SG_ OTA_Status_Information : 0|2@1+ (1,0) [0|3] ""  ESCL,HU,MCU,BMS00

BO_ 1649 VCU_ASW_Debug_Message2: 8 VCU
 SG_ Retry_Attempt_SSB_Passcode : 2|4@1+ (1,0) [0|5] "" Vector__XXX
 SG_ Charger_Retrigger : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Charger_Sleep_Request : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Signal8 : 48|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ Signal7 : 32|16@1+ (1,0) [0|65535] "" Vector__XXX
 SG_ Signal6 : 16|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1648 VCU_ASW_Debug_Message1: 8 VCU
 SG_ ASW_Log_Level_Set : 49|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ BMS_Over_temp_drive_error_BSW_signal : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_Over_temp_drive_warning_BSW_signal : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BMS_under_voltage_warning_BSW_signal : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AFE_Fault_Status_BSW_signal : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PDU_Warning_BSW_Signal : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PDU_Error_BSW_Signal : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ VCU_Limp_Home_Fault : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ VCU_Drive_Stop_Fault : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Signal4 : 41|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Signal3 : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Throttle_2_BSW_Value : 16|16@1+ (0.01,0) [0|20] "V" Vector__XXX
 SG_ Throttle_1_BSW_Value : 0|16@1+ (0.01,0) [0|20] "V" Vector__XXX

BO_ 1605 App_Setting_Data: 8 VCU
 SG_ Reserved_3 : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_2 : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CAPP_Activity_Status : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TMHL_Status : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ App_Auto_Indicator_Feature : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Proximity_Unlock_via_Passcode_Pin_Status : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Auto_Lock_Feature_Status : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Auto_Lock_Timer : 16|16@1+ (1,0) [0|65535] "seconds" Vector__XXX
 SG_ Take_Me_Home_Lights_Timer : 8|8@1+ (1,0) [0|255] "seconds" Vector__XXX
 SG_ Tow_Tamper_Detection_Status : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1603 HMI_Touch_Event: 8 VCU
 SG_ Touch_State : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1744 CommandFromVehicle: 8 VCU
 SG_ OTA_Install_Start : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Display_Icon : 40|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Hazard_Icon : 24|4@1+ (1,0) [0|15] ""  Tester_Tool
 SG_ Command_From_Vehicle : 0|8@1+ (1,0) [0|255] ""  Tester_Tool

BO_ 1664 VCU_ASW_Version_Info: 8 VCU
 SG_ VCU_ASW_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 2015 Functional_Diagnostic_Request: 8 VCU
 SG_ Functional_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  MCU,BMS00

BO_ 534 Vehicle_Config_Data_5: 8 VCU
 SG_ REGEN_BATT_TEMP_MIN_TH : 48|8@1+ (1,0) [0|255] "deg" Vector__XXX
 SG_ REGEN_SOC_UPPER_LIM : 40|8@1+ (1,0) [0|255] "%" Vector__XXX
 SG_ Reserved_Config_3 : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ TPMS : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Rain_Mode : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Child_Profile : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Tow_Protection : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Revive_Mode : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 533 Vehicle_Config_Data_4: 8 VCU
 SG_ Eco_Mode : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Auto_Indicator_Off : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Sound_Chimes : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Voice_Assistance : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Predictive_Maintenance : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Roadside_Assistance_on_CApp : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Track_Your_Vehicle : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Service_Book_Appointment_on_CApp : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 532 Vehicle_Config_Data_3: 8 VCU
 SG_ HMI_Mode_Vintage : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sounds_Mode_Power : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sounds_Mode_Hyper : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sounds_Mode_SciFi : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sounds_Mode_Normal : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HMI_Mode_Wonder : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HMI_Mode_Rush : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HMI_Mode_Care : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HMI_Mode_Boot : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HMI_Mode_Default : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Screen_Brightness_Adjuster_on_HMI : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Manual_SOS : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ OTA_Updates_Forced_Updates : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Welcome_Screen : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ESCL_Lock : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Find_My_Scooter : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1636 TP_FeatureV2_Available: 8 VCU
 SG_ Reserved_TP5 : 63|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CALLING : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CRUISE_CONTROL : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ADVANCED_REGEN_V2 : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FAST_CHARGING_V2 : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HYPER_CHARGING_V2 : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HILL_HOLD_V2 : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP4 : 62|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP3 : 61|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP2 : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reserved_TP1 : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FREE_ROAM_VIEW : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ INTER_CITY_TRIP_PLANNER : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ROAD_TRIP : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PUSH_LOCATION_TO_SCOOTER : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MAPS_ON_HMI : 54|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MOODS : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CONCERT_MODE : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PLAY_MUSIC : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PARTY_MODE : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CUSTOM_MOTOR_SOUNDS : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MEGAPHONE : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SCOOTER_WIDGETS : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NOTIFICATION_CENTER : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FALL_DETECTION : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LIVE_LOCATION_SHARING : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HAZARD_LIGHTS : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EMERGENCY_SOS : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HARDWARE_FAILURE_PREDICTION : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RIDE_JOURNAL : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RIDE_STATS : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ COMMUNITY : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FIND_MY_SCOOTER : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PROFILES : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DOCS_ON_SCOOTER : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FASTER_CHARGING : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SLOW_CHARGING : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BRAKE_BY_WIRE_SETTINGS : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BRAKE_BY_WIRE : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AUTO_INDICATOR : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TRACTION_CONTROL : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FORCED_REGEN : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ COAST_REGEN : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SPEED_LIMIT_ALERT : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RANGE_PREDICTION : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SMART_PARK : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DIY_MODE : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ REVERSE_MODE : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ECO_MODE : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NORMAL_MODE : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SPORT_MODE : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CAPP_LOCK_UNLOCK : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HYPER_MODE : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CAPP_VEHICLE_STATE : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PASSCODE_UNLOCK_V2 : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EARLY_ACCESS_BETA_V2 : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NEW_UPGRADES_V2 : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PROXIMITY_V2 : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ENERGY_INSIGHTS_V2 : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MODE_FENCING_V2 : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TIME_FENCING_V2 : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GEO_FENCING_V2 : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TOW_TAMPER_V2 : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TAKE_ME_HOME_LIGHTS_V2 : 4|1@1+ (1,0) [0|1] "" Vector__XXX


BO_ 530 Vehicle_Config_Data_2: 8 VCU
 SG_ Take_Me_Home_Lights : 56|1@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Cruise_Control : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Get_Home_Mode : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Hill_Hold : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Reverse_Mode : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Limp_Home_Mode : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Onboard_Navigation_2D : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Connectivity : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 529 Vehicle_Config_Data_1: 8 VCU
 SG_ Geofencing : 56|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Anti_Theft : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Side_Stand_Alert : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Infotainment_Control : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Bluetooth_Functions : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Boot_Unlock : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Vehicle_Lock_and_Unlock : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Vehicle_Variant : 0|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 1971 SOM_Diagnostic_Response: 8 VCU
 SG_ SOM_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1045 Tic_Status: 8 VCU
 SG_ BLE_Failure : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Tic_reset_Counter : 0|32@1+ (1,0) [0|4294967295] "" Vector__XXX

BO_ 1950 SOM_Diagnostic_Request: 8 Tester_Tool
 SG_ SOM_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1765 BMS00_Energy_Data: 8 BMS00
 SG_ PDU_TRNED_VAL : 32|16@1+ (0.01,0) [0|655.35] ""  ESCL,HU,MCU,SOM,Tester_Tool,VCU
 SG_ Instantaneous_Available_Energy : 16|16@1+ (0.001,-5) [-5|5] "Whr"  VCU
 SG_ Total_Available_Energy : 0|16@1+ (1,0) [0|5000] "Whr"  VCU

BO_ 1600 BMS00_Parameter_7: 8 BMS00
 SG_ Short_Circuit_Permanent_Precharge_Fault : 40|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Bus_Voltage_00_10ms : 24|16@1+ (0.001,0) [0|65] "V"  Tester_Tool,VCU
 SG_ Reserved_ : 7|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Reserved : 6|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Discharge_Switch_Demand_00 : 18|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Charge_Switch_Demand_00 : 17|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Precharge_Switch_Demand_00 : 16|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_MOSFETs_disconnection_failed_10ms : 5|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_MOSFETs_connection_failed_10ms : 4|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Precharge_failure_10ms : 3|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Precharge_too_slow_Info_10ms : 2|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Precharge_too_fast_Info_10ms : 1|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Short_circuit_detection_error : 0|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU

BO_ 1727 Digital_Output_Status: 8 VCU
 SG_ Charging_Contactor : 51|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MCU_12V_Control_Command : 50|1@1+ (1,0) [0|1] "V" Vector__XXX
 SG_ Spare2_Current_source_supply_control_command : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Status_LED_2 : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Status_LED_1 : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BLE_Output : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BLE_Module_Reset : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sig_3V3_Enable : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sig_3_8V_output_DC_DC_enable : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LIN_Driver_Enable : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Level_Shifter_Enable : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_Power_Boot : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_Power_Key : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_Volume_Up : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_Volume_down : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Drivers_Fault_Reset : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Driver_Diagnostic_Select_2 : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Driver_Diagnostic_Select_1 : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Driver_Diagnostic_Enable : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Control_Pilot_Wire_CPW : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SPARE2_Supply_12V_command : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Horn_12V_supply_control_command : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FC_contactor_Negative_supply_command : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FC_contactor_Positive_supply_command : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Spare_1_12V_supply_control_command : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Boot_Light_12V_supply_control_command : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Rear_Light_POS_12V_supply_control_command : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Rear_Brake_Light_POS_12V_supply_control_command : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Left_Indicators_12V_Supply_Control_Command : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Right_Indicators_12V_Supply_Control_Command : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Front_Light_High_Beam_12V_Supply_Control_Command : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Front_Light_Low_Beam_12V_Supply_Control_Command : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Front_DRL_12V_Supply_Control_Command : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Charger_USB_12V_Supply_Control_Command : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Head_Unit_12V_Supply_Control_Command : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Seat_Lock_12V_Supply_Control_Command : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Alarm_12V_Supply_Control_Command : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Steering_Lock_12V_Supply_Control_Command : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Speaker_Supply_Enable : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBC_12V_supply_Control_command : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Spare1_Current_source_supply_control_command : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Left_Brake_pad_wear_supply_control_Command : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Right_Brake_pad_wear_supply_control_command : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SSB_switch_supply_control_command : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Seat_Occupant_switch_supply_command : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Side_Stand_switch_supply_control_command : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BRAKE_PAD_Suppy_Enable : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HU_CAN_TRX_Standby_command : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FC_CAN_TRX_Standby_command : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Vehicle_CAN_TRX_Standby_command : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MCU_digital_Output_2_to_MCU : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MCU_digital_Output_1_to_MCU : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1726 Digital_Input_Status: 8 VCU
 SG_ RTC_Clock_Input : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RTC_INTERRUPT1 : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ IMU_INTERRUPT_2 : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ IMU_INTERRUPT_1 : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ CHARGER_PLUG_SENSE_STATUS : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BLE_OUTPUT_STATUS : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_INPUT2_MCU : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SOM_INPUT1_MCU : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ V3_LDO_PGOOD_STATUS : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AUX_BATTERY_CHARGING_STATUS : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sig_5V_Dc_Dc_PGOOD_STATUS : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Sig_3_8V_DC_DC_PGOOD_STATUS : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1329 Fast_Charger_Keep_Alive_Message: 8 VCU
 SG_ Fast_Charger_Alive : 0|1@1+ (1,0) [0|1] ""  BMS00

BO_ 1709 Vehicle_Right_HBC_Packet: 8 VCU
 SG_ Play_Pause_double_press : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Accept_Conf_double_press : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reject_Cancel_double_press : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Play_Pause_Long_Press_Voice_Assist : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Accept_Conf_Long_Press_Vol_Up : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reject_Cancel_Long_Press_Vol_Down : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Drive_Mode_Long_Press : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Play_Pause_Short_Press : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Accept_Conf_Short_Press : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Reject_Cancel_Short_Press : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Drive_Mode : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1708 Vehicle_Left_HBC_Packet: 3 VCU
 SG_ Cruise_Mode_Short_Press : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Long_press_of_Right_Indicator : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Long_press_of_Left_Indicator : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Cruise_Mode_Long_Press : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ High_Low_Beam_Long_Press_DRL : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Horn : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Right_Indicator : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Left_Indicator : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Cruise_Mode : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ High_Low_Beam_Short_Press : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 31 IGN_Status_by_VCU: 8 VCU
 SG_ VCU_IGN_Status : 0|3@1+ (1,0) [0|7] ""  MCU,BMS00

BO_ 151 MCU_Sleep_WakeUp_ACK: 8 MCU
 SG_ MCU_Sleep_WakeUp_ACK : 0|2@1+ (1,0) [0|3] ""  VCU,BMS00

BO_ 136 VCU_to_BMS_Wakeup: 1 VCU
 SG_ External_SOC_reset : 2|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Wake_up_Req_BMS : 0|2@1+ (1,0) [0|3] ""  BMS00

BO_ 3 VCU_NM_Message: 8 VCU
 SG_ WakeUp_Sleep_Request : 0|3@1+ (1,0) [0|7] ""  ESCL,HU,MCU,BMS00

BO_ 11 Slow_Charger_to_battery_wakeup: 8 VCU
 SG_ Battery_Wakeup_Request : 0|2@1+ (1,0) [0|3] ""  BMS00
 SG_ Serial_Number : 8|32@1+ (1,0) [0|4294967295] "" Vector__XXX
 SG_ Firmware_version : 40|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Hardware_version : 44|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Manufacturer_Code : 48|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ Bootloader_version : 52|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 64 MBMS_WakeUp_Reason: 8 BMS00
 SG_ MBMS_Wake_Up_Reason : 0|3@1+ (1,0) [0|7] ""  HU,MCU,VCU

BO_ 63 VCU_to_Battery_Authentication_Request: 8 VCU
 SG_ VCU_Seed : 0|32@1+ (1,0) [0|4294967295] ""  BMS00

BO_ 61 Battery_to_VCU_Authentication_Response: 8 BMS00
 SG_ BMS_Key : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 79 VCU_to_Battery_Authentication_Status: 8 VCU
 SG_ Auth_Status_BMS00 : 0|1@1+ (1,0) [0|1] ""  BMS00

BO_ 147 VCU_to_MCU_Authentication_Request: 8 VCU
 SG_ VCU_to_MCU_Seed : 0|32@1+ (1,0) [0|4294967295] ""  MCU

BO_ 155 MCU_to_VCU_Authentication_Response: 8 MCU
 SG_ MCU_Key : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 163 VCU_to_MCU_Authentication_Status: 8 VCU
 SG_ VCU_Auth_Response_to_MCU : 0|1@1+ (1,0) [0|1] ""  MCU,BMS00

BO_ 272 Battery_IDs: 8 BMS00
 SG_ Battery_Serial_No_1 m0 : 8|56@1+ (1,0) [0|7.20575940379279E+016] ""  VCU
 SG_ Multiplexer_Battery_Serial_No M : 0|8@1+ (1,0) [0|255] ""  VCU
 SG_ Battery_Serial_No_2 m1 : 8|56@1+ (1,0) [0|7.20575940379279E+016] ""  VCU

BO_ 284 BMS00_Parameter_1: 8 BMS00
 SG_ Contactor_state_00 : 0|4@1+ (1,0) [0|15] ""  VCU
 SG_ Battery_voltage_OCV_00 : 8|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Pack_SOC_00 : 24|16@1+ (0.01,0) [0|100] "%"  VCU
 SG_ BMS_Mode_00 : 40|4@1+ (1,0) [0|15] ""  VCU
 SG_ Battery_Precharge_failure_status_10ms : 48|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_charge_inhibit_10ms : 49|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_discharge_inhibit_10ms : 50|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Drive_Current_Flag_10ms : 51|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Charge_Current_Flag_10ms : 52|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Inhibit_Regen_Fault_10ms : 53|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Permanent_Fault_10ms : 54|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Short_circuit_detection_error_10ms : 55|1@1+ (1,0) [0|1] ""  VCU

BO_ 304 BMS00_Parameter_Limit_1: 8 BMS00
 SG_ Charge_current_limit_00 : 0|16@1+ (0.01,0) [0|300] "Amp"  Tester_Tool,VCU
 SG_ Discharge_current_limit_00 : 16|16@1+ (0.01,-400) [-400|0] "Amp"  Tester_Tool,VCU
 SG_ Charge_Voltage_limit_00 : 32|16@1+ (0.001,0) [0|65] "V"  Tester_Tool,VCU
 SG_ Charging_Mode_00 : 48|3@1+ (1,0) [0|7] ""  VCU

BO_ 320 BMS00_Parameter_Limit_2: 8 BMS00
 SG_ Regen_Power_Limit_00 : 0|16@1+ (1,0) [0|65535] "Watts"  MCU,Tester_Tool,VCU
 SG_ Discharge_Power_Limit_00 : 16|16@1+ (1,-50000) [-50000|0] "Watts"  MCU,Tester_Tool,VCU
 SG_ Battery_Current_00 : 32|16@1+ (0.01,-400) [-400|255] "Amp"  MCU,Tester_Tool,VCU

BO_ 336 BMS00_Parameter_3: 8 BMS00
 SG_ Bus_Voltage_00 : 0|16@1+ (0.001,0) [0|65] "V"  Tester_Tool,VCU
 SG_ Delta_Voltage_00 : 16|16@1+ (0.001,0) [0|65] "V"  Tester_Tool,VCU
 SG_ Available_Capacity_00 : 32|8@1+ (1,0) [0|100] "%"  VCU
 SG_ Pack_SoH : 40|8@1+ (1,0) [0|100] "%"  VCU

BO_ 288 Overall_Battery_Status_Info: 8 BMS00
 SG_ Overall_Battery_Current : 0|16@1+ (0.01,-300) [-300|300] "Amp"  MCU,VCU
 SG_ Overall_SOC : 16|16@1+ (0.01,0) [0|100] "%"  MCU,VCU
 SG_ Overall_Charge_Voltage_limit : 32|16@1+ (0.001,0) [0|65] "V"  MCU,VCU
 SG_ Charger_Mode_Request : 48|3@1+ (1,0) [0|7] ""  MCU,VCU

BO_ 528 Throttle_Status: 8 VCU
 SG_ Customer_Mode_Flg : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Throttle_Offset_Count : 3|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ Throttle_Offset : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Derate_Warning : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Throttle_Deviation_Status : 0|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 289 Overall_Battery_Current_Limit: 8 BMS00
 SG_ Overall_Charge_current_limit : 0|16@1+ (0.01,0) [0|600] "Amp"  MCU,VCU
 SG_ Overall_Discharge_current_limit : 16|16@1+ (0.01,-400) [-400|0] "Amp"  MCU,VCU
 SG_ Overall_Regen_Power_Limit : 32|16@1+ (1,0) [0|65535] "Watts"  MCU,VCU
 SG_ Overall_Discharge_Power_Limit : 48|16@1+ (1,-50000) [-50000|0] "Watts"  MCU,VCU

BO_ 290 Overall_Battery_Fault: 8 BMS00
 SG_ Battery_Voltage_Deviation_Error : 51|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Precharge_failure_status_00 : 0|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Precharge_failure_status_01 : 1|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Precharge_failure_status_02 : 2|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_charge_inhibit_00 : 8|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_charge_inhibit_01 : 9|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_charge_inhibit_02 : 10|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_discharge_inhibit_00 : 16|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_discharge_inhibit_01 : 17|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_discharge_inhibit_02 : 18|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Drive_Current_Flag_00 : 24|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Drive_Current_Flag_01 : 25|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Drive_Current_Flag_02 : 26|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Charge_Current_Flag_00 : 32|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Charge_Current_Flag_01 : 33|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Derate_Charge_Current_Flag_02 : 34|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Inhibit_Regen_Fault_00 : 40|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Inhibit_Regen_Fault_01 : 41|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Inhibit_Regen_Fault_02 : 42|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Permanent_Fault_00 : 48|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Permanent_Fault_01 : 49|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Permanent_Fault_02 : 50|1@1+ (1,0) [0|1] ""  VCU

BO_ 352 BMS00_Parameter_4: 8 BMS00
 SG_ Reset_SOC_from_OCV : 56|1@1+ (1,0) [0|1] ""  MCU,VCU
 SG_ Balancing_Status_00 : 0|1@1+ (1,0) [0|1] ""  MCU,VCU
 SG_ Effective_Battery_Temperature_00 : 8|16@1+ (0.1,-50) [-50|120] "degC"  MCU,VCU
 SG_ Battery_Temperature_Min_00 : 24|16@1+ (0.1,-50) [-50|120] "degC"  MCU,VCU
 SG_ Battery_Temperature_Max_00 : 40|16@1+ (0.1,-50) [-50|120] "degC"  MCU,VCU

BO_ 384 BMS00_Parameter_5: 8 BMS00
 SG_ Charging_time_Max : 0|16@1+ (1,0) [0|65535] "Min"  VCU
 SG_ DC_output_voltage_limit_parameter : 16|16@1+ (0.1,0) [0|120] "V"  VCU
 SG_ Charging_Rate : 32|8@1+ (1,0) [0|100] "%"  VCU

BO_ 400 BMS00_Parameter_6: 8 BMS00
 SG_ Time_to_chargeSC_80 : 0|16@1+ (1,0) [0|65535] "Min"  VCU
 SG_ Time_to_chargeFC_100 : 48|16@1+ (1,0) [0|65535] "Min"  VCU
 SG_ Time_to_Full_chargeSC_00 : 16|16@1+ (1,0) [0|65535] "Min"  VCU
 SG_ Time_to_chargeFC_80 : 32|16@1+ (1,0) [0|65535] "Min"  VCU

BO_ 1795 EOL_Timestamp: 8 Tester_Tool
 SG_ EOL_BMS_TimeStamp : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU,BMS00

BO_ 1793 TimeStamp_Sync_Message: 8 Tester_Tool
 SG_ VCU_TimeStamp : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  ESCL,HU,MCU,BMS00

BO_ 1040 BMS00_Fault_1: 8 BMS00
 SG_ BMS00_Voltage_sensor_failureCell1 : 0|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell2 : 1|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell3 : 2|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell4 : 3|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell5 : 4|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell6 : 5|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell7 : 6|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell8 : 7|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell9 : 8|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell10 : 9|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell11 : 10|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell12 : 11|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell13 : 12|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell1 : 13|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell2 : 14|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell3 : 15|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell4 : 16|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell5 : 17|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell6 : 18|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell7 : 19|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell8 : 20|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell9 : 21|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell10 : 22|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell11 : 23|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell12 : 24|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell13 : 25|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature1_sensor_failure : 26|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature2_sensor_failure : 27|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature3_sensor_failure : 28|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature4_sensor_failure : 29|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature1_out_of_range : 30|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature2_out_of_range : 31|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature3_out_of_range : 32|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Battery_pack_temperature4_out_of_range : 33|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Current_sensor_failure_Open_circuit : 34|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Current_sensor_failure_Short_circuit : 35|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_current_charge_Warning : 36|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_VOOR_CutOff_Error : 37|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Precharge_too_fast_Info : 38|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Precharge_too_slow_Info : 39|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Precharge_failure : 40|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_MOSFETs_connection_failed : 41|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_MOSFETs_disconnection_failed : 42|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_PDU_temperature_warning_info : 43|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_PDU_temperature_error : 44|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_voltage_charge_warning_info : 45|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_voltage_charge_Error : 46|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_voltage_charge_Permanent_Fault : 47|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_voltage_regen_warning_info : 48|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_voltage_regen_Error : 49|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Under_voltage_warning_info : 50|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Under_voltage_Error : 51|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Under_voltage_Permanent_Fault : 52|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_temperature_charge_warning_info : 53|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_temperature_charge_Error : 54|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_temperature_drive_warning_info : 55|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_temperature_drive_Error : 56|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_temperature_due_to_Cell_vent_error : 57|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_temperature_due_to_Cell_vent_Failure : 58|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Short_circuit_detection_error_info : 59|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Short_circuit_detection_permanent_fault : 60|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Cell_failure_permanent_fault : 61|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Over_current_charge_Error : 62|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Low_temperature_during_charging_warning_info : 63|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU

BO_ 1041 BMS00_Fault_2: 8 BMS00
 SG_ Batt_Derate_Eco : 41|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ PDU_Derate_Eco : 40|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ thermal_runaway_shadow_fault : 39|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ thermal_runaway_fault : 38|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Cell_Voltage_Rise_Deration_in_HC : 37|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ ChargerReAuthWarning : 35|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ ChargerReAuthError : 36|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Overcharge_Protection : 34|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Temperature_Blanket_Cut_off_Grade4 : 33|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Pack_Temperature_Error_Sleep : 32|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Pack_Temperature_Rise_Sleep : 31|1@1+ (1,0) [0|1] ""  VCU
 SG_ PDU_Temperature_Error_during_Sleep : 30|1@1+ (1,0) [0|1] ""  VCU
 SG_ PDU_Temperature_Rise_Detection : 29|1@1+ (1,0) [0|1] ""  VCU
 SG_ PDU_Temperature_Rise_Detection_Sleep : 28|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Temp_Increase_flt_sleep : 26|1@1+ (1,0) [0|1] ""  VCU
 SG_ Battery_Temp_Fault_during_Slp : 25|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS_3_3V_not_good_fault : 27|1@1+ (1,0) [0|1] ""  VCU
 SG_ External_Flash_COM_Lost : 24|1@1+ (1,0) [0|1] ""  VCU
 SG_ AFE_COM_LOST : 23|1@1+ (1,0) [0|1] ""  VCU
 SG_ Supply_9V_Not_Good : 22|1@1+ (1,0) [0|1] ""  VCU
 SG_ SUPPLY_12V_NOT_GOOD : 21|1@1+ (1,0) [0|1] ""  VCU
 SG_ SBC_FAULT_ACTIVE : 20|1@1+ (1,0) [0|1] ""  VCU
 SG_ PDUerrorCutOff_60 : 19|1@1+ (1,0) [0|1] ""  VCU
 SG_ TempRiseChrgDtctn_60 : 18|1@1+ (1,0) [0|1] ""  VCU
 SG_ TempRiseDriveDtctn_60 : 17|1@1+ (1,0) [0|1] ""  VCU
 SG_ SOCRegenWarn : 16|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ TempRegen_Warning : 15|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ CompSpikeDtctn_Fault : 13|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ CompSpike_Error : 14|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ CellDip_Fault : 12|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ CellDip_Error : 11|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BattTempAbNormal_Fault : 10|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Low_temperature_during_charging_Error : 0|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Low_temperature_during_driving_Warning : 1|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Low_temperature_during_driving_Error : 2|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Over_time_to_fast_charge_Error : 3|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_sensor_failureCell14 : 4|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_Voltage_out_of_range_Cell14 : 5|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Battery_pack_temperature5_sensor_failure : 6|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Battery_pack_temperature6_sensor_failure : 7|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Battery_pack_temperature5_out_of_range : 8|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Battery_pack_temperature6_out_of_range : 9|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU

BO_ 368 BMS00_Cell_Status: 8 BMS00
 SG_ BMS00_Balancing_Status_Battery_Cell_1 : 0|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_2 : 1|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_3 : 2|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_4 : 3|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_5 : 4|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_6 : 5|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_7 : 6|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_8 : 7|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_9 : 8|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_10 : 9|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_11 : 10|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_12 : 11|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_13 : 12|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Balancing_Status_Battery_Cell_14 : 13|1@1+ (1,0) [0|1] ""  VCU
 SG_ BMS00_Cell_Min_Voltage : 16|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ BMS00_Cell_Max_Voltage : 32|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 531 Charger_Status: 8 VCU
 SG_ Boost_Charge_Fault_Information : 51|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BoostCharger_billing_status : 50|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Charger_Status_Signal : 0|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Charger_Mode : 1|3@1+ (1,0) [0|7] ""  BMS00
 SG_ Charging_Voltage_Available_range : 4|16@1+ (0.1,0) [0|120] "V"  BMS00
 SG_ Charging_Current_Available_range : 20|16@1+ (0.1,0) [0|500] "Amp"  BMS00
 SG_ Slow_Charger_Fault_Information : 36|2@1+ (1,0) [0|3] ""  BMS00
 SG_ Charger_output_Short_circuit_error : 40|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Charger_under_temperature_error : 41|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Charger_over_temperature_error : 42|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Low_AC_voltage_error : 43|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Rectifier_hardware_error : 44|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Authentication_Error : 45|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Battery_Parameter_Time_out_error : 46|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Data_Corruption_error : 47|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Charge_control_message_timeout_error : 48|1@1+ (1,0) [0|1] ""  BMS00

BO_ 1568 BMS00_FW_Version_Info: 8 BMS00
 SG_ MBMS_Firmware_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1554 BMS00_BL_Version_Info: 8 BMS00
 SG_ BMS00_Bootloader_version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1657 VCU_FW_Version_Info: 8 VCU
 SG_ VCU_Firmware_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1616 BMS_ASW_Version_Info: 8 BMS00
 SG_ BMS_ASW_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1617 BMS_Configuration_Info: 8 BMS00
 SG_ BMS_Hardware_Variant : 56|2@1+ (1,0) [0|1] ""  VCU
 SG_ BMS_DipSwitch : 58|4@1+ (1,0) [0|3] ""  VCU
 SG_ Battery_Cell_Type : 0|4@1+ (1,0) [0|15] ""  VCU
 SG_ Battery_cell_in_Series : 8|8@1+ (1,0) [0|255] ""  VCU
 SG_ Battery_cell_in_Parallel : 16|8@1+ (1,0) [0|255] ""  VCU
 SG_ CAN_Database : 24|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 1586 VCU_BL_Version_Info: 8 VCU
 SG_ VCU_Bootloader_version : 0|64@1+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1587 HU_HW_Version_Info: 8 HU
 SG_ LCD_Unique_ID : 0|32@1+ (1,0) [0|4294967295] ""  VCU

BO_ 1588 HU_FW_Version_Info: 8 HU
 SG_ HU_Firmware_Version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1589 HU_BL_Version_Info: 8 HU
 SG_ HU_Bootloader_version : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 787 MCU_Data_1: 8 MCU
 SG_ Vehicle_speed_in_kmph : 0|16@1+ (0.1,0) [0|200] "kmph"  HU,VCU,BMS00
 SG_ MCU_Temperature : 16|16@1+ (0.01,0) [0|600] "degC"  VCU,BMS00
 SG_ Motor_Temprature : 32|16@1+ (0.01,0) [0|600] "degC"  VCU,BMS00
 SG_ Motor_RPM : 48|16@1+ (1,0) [0|65535] "rpm"  VCU,BMS00

BO_ 275 MCU_Data_2: 8 MCU
 SG_ MCU_Power_Status : 0|2@1+ (1,0) [0|3] ""  VCU
 SG_ Throttle_Value1 : 8|16@1+ (0.01,0) [0|20] "V"  VCU
 SG_ Throttle_Value2 : 24|16@1+ (0.01,0) [0|655.35] "V"  VCU
 SG_ Regen_Status : 2|3@1+ (1,0) [0|4] ""  VCU
 SG_ Cruise_Control_Status : 40|1@1+ (1,0) [0|1] ""  VCU
 SG_ Vehicle_Discharge_Mode : 41|4@1+ (1,0) [0|15] ""  VCU
 SG_ Hill_Hold_Response : 45|2@1+ (1,0) [0|3] ""  VCU
 SG_ MCU_Voltage : 48|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 1583 B2B_Message: 8 Tester_Tool
 SG_ CAN_Throttle_Speed_Mode : 0|8@1+ (1,0) [0|100] "%"  MCU
 SG_ CAN_Throttle_Torque_Mode : 8|8@1+ (1,0) [0|100] "%"  MCU

BO_ 547 MCU_Data_3: 8 MCU
 SG_ MCU_DC_Current : 40|16@1+ (0.01,-300) [-300|300] "Amp" Vector__XXX
 SG_ Motor_Current : 0|16@1+ (1,-1000) [-1000|1000] "A"  VCU,BMS00
 SG_ Motor_Torque : 16|16@1+ (0.001,0) [0|65.535] "Nm"  VCU,BMS00
 SG_ Distance_covered_for_current_trip : 32|8@1+ (1,0) [0|256] "Km"  VCU

BO_ 803 MCU_Disconnect_ACK: 8 MCU
 SG_ Disconnect_ACK : 0|1@1+ (1,0) [0|1] ""  VCU

BO_ 819 MCU_Fault_Data: 8 MCU
 SG_ Vehicle_Wrong_Direction_Flag : 50|1@1+ (1,0) [0|1] ""  VCU
 SG_ Motor_Temperature_Derating_Flag : 49|1@1+ (1,0) [0|1] ""  BMS00,VCU
 SG_ MCU_Temperature_Derating_Flag : 48|1@1+ (1,0) [0|1] ""  BMS00,VCU
 SG_ Attempted_Calibration_Counter : 40|8@1+ (1,0) [0|255] ""  BMS00,VCU
 SG_ Successful_Calibration_Counter : 32|8@1+ (1,0) [0|255] ""  BMS00,VCU
 SG_ MCU_Calibration_Status : 29|3@1+ (1,0) [0|7] ""  BMS00,VCU
 SG_ Software_overcurrent_protection_Grade_3 : 0|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Software_overvoltage_fault_Grade_3 : 1|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Drive_Protection : 2|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Failure_for_motor_parameter_tuning : 3|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Drive_Overload_Grade_3 : 4|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ U_phase_hall_fault : 5|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Drive_overtemperature_Grade_3 : 6|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Motor_overtemperature_Grade_3 : 7|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Encoder_disconnection_Grade_3 : 8|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Overvoltagebaseline_of_hardware_is_wrong_Grade_3 : 9|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Stalling_Fault_Grade_3 : 10|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ DC_Bus_undervoltage_fault_Grade_3 : 11|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ CAN_communication_abnormal_failure : 12|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Motor_over_speed_fault_Grade_3 : 13|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Motor_temperature_sensor_disconnection_fault_Grade_3 : 14|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Hardware_overcurrent_fault : 15|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Hardware_overvoltage_fault : 16|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Drive_power_under_voltage_fault : 17|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ The_resolver_connector_is_loose_and_abnormal : 18|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Controller_drive_abnormal_fault : 19|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Power_supplyof_drive_board_exceeds_the_upper_limit : 20|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Low_voltage_power_supply_under_voltage_fault : 21|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ U_phase_Hall_disconnection_fault : 22|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ V_phase_Hall_disconnection_fault : 23|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ W_phase_Hall_disconnection_fault : 24|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ V_phase_Hall_abnormal_fault : 25|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ W_phase_Hall_abnormal_fault : 26|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Throttle_Wiper1_Fail : 27|1@1+ (1,0) [0|1] ""  VCU,BMS00
 SG_ Throttle_Wiper2_Fail : 28|1@1+ (1,0) [0|1] ""  VCU,BMS00

BO_ 1565 IMU_Data_X: 8 VCU
 SG_ Accelerometer_Xaxis : 0|32@1- (1,-2000) [-2000|2000] "mg"  MCU
 SG_ Gyroscope_Xaxis : 32|32@1- (1,-2000) [-2000|2000] "dps"  MCU

BO_ 1597 IMU_Data_Y: 8 VCU
 SG_ Accelerometer_Yaxis : 0|32@1- (1,-2000) [-2000|2000] "mg" Vector__XXX
 SG_ Gyroscope_Yaxis : 32|32@1- (1,-2000) [-2000|2000] "dps" Vector__XXX

BO_ 1613 IMU_Data_Z: 8 VCU
 SG_ Accelerometer_Zaxis : 0|32@1- (1,-2000) [-2000|2000] "mg" Vector__XXX
 SG_ Gyroscope_Zaxis : 32|32@1- (1,-2000) [-2000|2000] "dps" Vector__XXX

BO_ 1571 MCU_Version_Info: 8 MCU
 SG_ MCU_HW_Version : 0|32@1+ (1,0) [0|4294967295] ""  VCU,BMS00
 SG_ MCU_Firmware_Version : 32|32@1+ (1,0) [0|4294967295] ""  VCU,BMS00

BO_ 323 Vehicle_Mode: 8 VCU
 SG_ About_Scooter_Event : 18|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HMI_StatusBar_Stt : 49|4@1+ (1,0) [0|15] ""  BMS00,HU,MCU
 SG_ Cruise_Control_Availability : 59|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ Available_Driving_Modes : 56|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ Vehicle_Mode_Level_1 : 0|4@1+ (1,0) [0|15] ""  HU,MCU,BMS00
 SG_ Vehicle_Mode_Level_2 : 4|4@1+ (1,0) [0|15] ""  HU,MCU,BMS00
 SG_ Vehicle_Mode_Level_3 : 8|4@1+ (1,0) [0|15] ""  HU,MCU,BMS00
 SG_ Vehicle_Mode_Level_4 : 12|4@1+ (1,0) [0|15] ""  HU,MCU,BMS00
 SG_ Drive_Request : 16|2@1+ (1,0) [0|3] ""  HU,MCU,BMS00
 SG_ Odometer : 24|24@1+ (1,0) [0|16777215] ""  HU,MCU,BMS00
 SG_ Charging_status_of_AUX_battery : 48|1@1+ (1,0) [0|1] ""  HU,MCU,BMS00

BO_ 339 DCDC_Voltage: 8 VCU
 SG_ DCDC_Output_Voltage : 0|8@1+ (0.1,0) [0|25.6] "" Vector__XXX
 SG_ SSB_Button_Status : 8|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 347 Vehicle_Discharge_Mode_Request: 8 VCU
 SG_ Front_Brake_Press : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Rear_Brake_Press : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Discharge_Mode : 0|4@1+ (1,0) [0|15] ""  MCU
 SG_ Front_Brake_Status : 8|1@1+ (1,0) [0|1] ""  MCU
 SG_ Rear_Brake_Status : 9|1@1+ (1,0) [0|1] ""  MCU
 SG_ Cruise_Control_Request : 10|3@1+ (1,0) [0|3] ""  MCU
 SG_ Gradient_Information_X_axis : 16|16@1+ (1,-360) [-360|360] "degree"  MCU
 SG_ Gradient_Information_Y_axis : 32|16@1+ (1,-360) [-360|360] "degree"  MCU
 SG_ Gradient_Information_Z_axis : 48|16@1+ (1,-360) [-360|360] "degree"  MCU

BO_ 851 Display_Info: 8 VCU
 SG_ Side_Stand_Status : 0|2@1+ (1,0) [0|1] ""  HU
 SG_ Seat_Lock_Status : 2|1@1+ (1,0) [0|1] ""  HU
 SG_ Cruise_Control_TTL : 3|1@1+ (1,0) [0|1] ""  HU
 SG_ Vehicle_Range : 24|16@1+ (1,0) [0|65535] ""  HU
 SG_ Right_Indicator_TTL : 40|1@1+ (1,0) [0|1] ""  HU
 SG_ Left_Indicator_TTL : 41|1@1+ (1,0) [0|1] ""  HU
 SG_ High_Beam_TTL : 42|1@1+ (1,0) [0|1] ""  HU
 SG_ Service_Indicator_TTL : 43|1@1+ (1,0) [0|1] ""  HU
 SG_ Vehicle_Error_Indicator_TTL : 44|2@1+ (1,0) [0|2] ""  HU
 SG_ Charge_TTL : 46|2@1+ (1,0) [0|3] ""  HU
 SG_ Position_Lamp_TTL : 48|1@1+ (1,0) [0|1] ""  HU
 SG_ Brake_Pad_TTL : 49|1@1+ (1,0) [0|1] ""  HU
 SG_ Low_Beam_TTL : 50|1@1+ (1,0) [0|1] ""  HU
 SG_ Low_Battery_Alert_TTL : 51|1@1+ (1,0) [0|1] ""  HU
 SG_ Indicator_On_Reason : 52|4@1+ (1,0) [0|15] ""  HU
 SG_ Ambient_Brightness_Level : 56|8@1+ (1,0) [0|100] "%"  HU

BO_ 1088 Steering_Lock_Request: 8 VCU
 SG_ StrLockUnlockCmd : 0|2@1+ (1,0) [0|2] ""  ESCL

BO_ 1216 Steering_Lock_Status: 8 ESCL
 SG_ ESCL_Status : 0|2@1+ (1,0) [0|3] ""  VCU

BO_ 1217 Steering_Lock_Error: 8 ESCL
 SG_ Motor_Stl_Crnt : 38|16@1+ (1,0) [0|65535] ""  VCU
 SG_ Motor_shrt_Flt : 37|1@1+ (1,0) [0|1] ""  VCU
 SG_ Motor_Opn_Flt : 36|1@1+ (1,0) [0|1] ""  VCU
 SG_ Motor_temp_Shut : 35|1@1+ (1,0) [0|1] ""  VCU
 SG_ plngr_stk_sts : 34|1@1+ (1,0) [0|1] ""  VCU
 SG_ Hall_2_sts : 33|1@1+ (1,0) [0|1] ""  VCU
 SG_ Hall_1_sts : 32|1@1+ (1,0) [0|1] ""  VCU
 SG_ Batt_Volt : 24|8@1+ (1,0) [0|255] "V"  VCU
 SG_ ESCL_Sleep_Ack : 16|8@1+ (1,0) [0|255] ""  VCU
 SG_ LockUnlockErrorIndication : 0|16@1+ (1,0) [0|65535] ""  VCU

BO_ 99 HU_NM_Message: 8 HU
 SG_ Wakeup_Reason : 0|5@1+ (1,0) [0|31] ""  VCU

BO_ 867 Ambient_Light_Sensor_Value: 8 HU
 SG_ Ambient_Light_Sensor_Value : 0|16@1+ (2,0) [0|84000] ""  VCU

BO_ 420 BMS00_Pack_Temp: 8 BMS00
 SG_ BMS00_Pack_Temperature_04 : 48|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,VCU
 SG_ BMS00_Pack_Temperature_01 : 0|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,VCU
 SG_ BMS00_Pack_Temperature_02 : 16|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,VCU
 SG_ BMS00_Pack_Temperature_03 : 32|16@1+ (0.1,-50) [-50|120] "DegC"  Tester_Tool,VCU

BO_ 424 BMS00_PDU_Temp: 8 BMS00
 SG_ BMS00_PDU_Temperature_01 : 0|11@1+ (0.1,-40) [-40|120] "DegC"  Tester_Tool,VCU
 SG_ BMS00_PDU_Temperature_02 : 16|11@1+ (0.1,-40) [-40|120] "DegC"  Tester_Tool,VCU
 SG_ BMS00_Battery_Pack_Voltage_Measured : 32|16@1+ (0.001,0) [0|65] "V"  Tester_Tool,VCU
 SG_ BMS00_Cell_Balancing_Temperature : 48|16@1+ (0.01,-40) [-40|615] "DegC"  Tester_Tool,VCU

BO_ 423 BMS00_Pack_Temp_2: 8 BMS00
 SG_ BMS00_Battery_Temperature_Display : 48|8@1+ (1,-50) [-50|200] ""  VCU
 SG_ BMS00_PDU_Delta_Temperature : 32|16@1+ (0.5,-50) [-50|32718] "DegC"  VCU
 SG_ BMS00_Pack_Temperature_05 : 0|16@1+ (0.1,-50) [-50|120] "DegC"  VCU
 SG_ BMS00_Pack_Temperature_06 : 16|16@1+ (0.1,-50) [-50|120] "DegC"  VCU

BO_ 672 EPOCH_Time: 8 BMS00
 SG_ BMS_EPOCH_Time : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 421 BMS_Cell_Voltage_1: 8 BMS00
 SG_ Cell_Voltage_Cell_1 : 0|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_2 : 16|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_3 : 32|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_4 : 48|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 422 BMS_Cell_Voltage_2: 8 BMS00
 SG_ Cell_Voltage_Cell_5 : 0|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_6 : 16|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_7 : 32|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_8 : 48|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 425 BMS_Cell_Voltage_3: 8 BMS00
 SG_ Cell_Voltage_Cell_9 : 0|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_10 : 16|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_11 : 32|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_12 : 48|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 426 BMS_Cell_Voltage_4: 8 BMS00
 SG_ Cell_Voltage_Avg_1_14 : 32|16@1+ (0.001,0) [0|65.535] "V"  VCU
 SG_ Cell_Voltage_Cell_13 : 0|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Cell_Voltage_Cell_14 : 16|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 1752 BMS00_Cloud_Data_1: 8 BMS00
 SG_ Cycle_Number : 0|16@1+ (1,0) [0|65535] "Count "  VCU
 SG_ Kwhr_Accumulated_Current_Cycle : 16|16@1+ (0.01,0) [0|65.53] "Kwhr"  VCU
 SG_ Factored_Charge_Amphr : 32|16@1+ (0.01,0) [0|100] "Amphr"  VCU
 SG_ Balancing_Temperature : 48|16@1+ (0.5,-40) [-40|90] "degC"  VCU

BO_ 1756 BMS00_Cloud_Data_3: 8 BMS00
 SG_ Total_Amphr_Discharged_in_the_Mode : 0|16@1+ (0.01,-100) [-100|0] "Amphr"  VCU
 SG_ Total_Kwhr_Dicharged_in_the_Mode : 16|16@1+ (0.01,-65) [-65|0] "Kwhr"  VCU
 SG_ Number_of_Stops_during_Drive : 32|16@1+ (1,0) [0|65535] "Count "  VCU
 SG_ Number_of_Charge_Stops_during_charge : 48|16@1+ (1,0) [0|65535] "Count"  VCU

BO_ 1758 BMS00_Cloud_Data_4: 8 BMS00
 SG_ Total_Amphr_Charged_in_the_Mode : 0|16@1+ (0.01,0) [0|100] "Amphr"  VCU
 SG_ Total_Kwhr_Charged_in_the_Mode : 16|16@1+ (0.01,0) [0|65] "Kwhr"  VCU
 SG_ Total_Active_Duration_of_the_Mode : 32|16@1+ (1,0) [0|65535] "Minutes"  VCU
 SG_ Total_Regenerative_Amphr : 48|16@1+ (0.01,0) [0|43] "Amphr"  VCU

BO_ 1760 BMS00_Cloud_Data_5: 8 BMS00
 SG_ Factored_Discharge_Amphr : 0|16@1+ (0.1,-6553) [-6553|0.5] "Amphr"  VCU
 SG_ Total_Kwhr_Consumption_in_30sec_Slot : 16|16@1+ (0.1,-65) [-65|0] "Kwhr"  VCU
 SG_ Total_Ah_Lost_in_charging_Aux_battery_in_Idle : 32|16@1+ (0.1,-43) [-43|0] "Amphr"  VCU
 SG_ Total_Charge_Time : 48|16@1+ (1,0) [0|65535] "Minutes"  VCU

BO_ 1762 BMS00_Cloud_Data_6: 8 BMS00
 SG_ Max_Cell_Deviation_Observed_during_Discharge : 0|16@1+ (0.001,0) [0|65] "V"  VCU
 SG_ Max_Cell_Deviation_Observed_during_Charge : 16|16@1+ (0.001,0) [0|65] "V"  VCU

BO_ 1764 BMS00_Cloud_Data_7: 8 BMS00
 SG_ Number_of_times_balancing_started_due_to_Deviation : 0|16@1+ (1,0) [0|65535] "Count"  VCU
 SG_ Total_Amphr_Lost_during_Balancing : 16|16@1+ (0.1,0) [0|43] "Amphr"  VCU
 SG_ Total_Balancing_Duration : 32|16@1+ (1,0) [0|65535] "Minutes"  VCU

BO_ 20 Fast_Charger_Connection_Status: 8 VCU
 SG_ Fast_Charger_Plug_Status : 0|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Fast_Charger_Contactor_Status : 1|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Fast_Charge_Terminate_Request : 2|1@1+ (1,0) [0|1] ""  BMS00

BO_ 1926 VCU_Diagnostic_request: 8 Tester_Tool
 SG_ VCU_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1968 VCU_Diagnostic_response: 8 VCU
 SG_ VCU_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1958 MCU_Diagnostic_request: 8 VCU
 SG_ MCU_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  MCU

BO_ 1972 MCU_Diagnostic_response: 8 MCU
 SG_ MCU_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1934 BMS0_Diagnostic_Request: 8 VCU
 SG_ BMS0_Diag_Request : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS00

BO_ 1969 BMS0_Diagnostic_Response: 8 BMS00
 SG_ BMS0_Diag_Response : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  VCU

BO_ 1288 Fast_Charger_Parameter_1: 8 VCU
 SG_ Charging_system_error : 0|1@1+ (1,0) [0|1] ""  BMS00
 SG_ EV_supply_equipment_malfunction : 1|1@1+ (1,0) [0|1] ""  BMS00
 SG_ EV_incompatibility : 2|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Reserved_6 : 3|5@1+ (1,0) [0|31] ""  BMS00
 SG_ EV_supply_equipment_stop_control : 8|1@1+ (1,0) [0|1] ""  BMS00
 SG_ EV_supply_equipment_status : 9|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Vehicle_connector_latched : 10|1@1+ (1,0) [0|1] ""  BMS00
 SG_ EV_supply_equipment_ready : 11|1@1+ (1,0) [0|1] ""  BMS00
 SG_ Waiting_state_before_charging_start : 12|1@1+ (1,0) [0|1] ""  BMS00
 SG_ EVSE_Emergency_Stop : 13|2@1+ (1,0) [0|1] ""  BMS00
 SG_ rated_DC_output_voltage : 16|16@1+ (0.1,0) [0|120] "V"  BMS00
 SG_ Available_DC_output_current : 32|16@1+ (0.1,0) [0|200] "A"  BMS00
 SG_ Confirmed_DC_output_voltage_limit : 48|16@1+ (0.1,0) [0|120] "V"  BMS00

BO_ 1289 Fast_Charger_Parameter_2: 8 VCU
 SG_ Control_protocol_number : 0|8@1+ (1,0) [0|254] ""  BMS00
 SG_ Available_DC_output_power : 8|8@1+ (50,0) [0|12700] "W"  BMS00
 SG_ Output_voltage : 16|16@1+ (0.1,0) [0|250] "V"  BMS00
 SG_ DC_output_current : 32|16@1+ (0.1,0) [0|200] "A"  BMS00
 SG_ Remaining_charging_time : 48|16@1+ (1,0) [0|65534] "min"  BMS00

BO_ 1296 FC_Voltage_Control: 8 VCU
 SG_ Voltage_Control_Option : 0|1@1+ (1,0) [0|1] ""  BMS00

BO_ 1412 EV_Identification_1: 8 VCU
 SG_ EVSE_identification_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS00

BO_ 1413 EV_Identification_2: 8 VCU
 SG_ EVSE_identification_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS00

BO_ 1414 Protocol_1: 8 VCU
 SG_ Protocol_identifier_low_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS00

BO_ 1415 Protocol_2: 8 VCU
 SG_ Protocol_identifier_high_byte : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  BMS00

BO_ 1607 AmpHr_Data_00: 8 BMS00
 SG_ Charge_AmpHr_00 : 0|16@1+ (0.01,0) [0|6553] "Amphr"  VCU
 SG_ Discharge_AmpHr_00 : 16|16@1+ (0.1,-6553) [-6553|0] "Amphr"  VCU

BO_ 1754 BMS00_Cloud_Data_2: 8 BMS00
 SG_ BMS_NVM_Failure_Status : 20|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_9V_Supply_Status : 0|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_12V_Supply_voltage_Status : 1|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ SBC_Limp_Input : 2|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ AFE_Fault_Input : 3|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Charge_Over_Current : 4|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Discharge_Over_current : 5|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ RTC_Interrupt : 6|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ RTC_Clock_IN : 7|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Charger_Plug_Sense : 8|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS_Status : 9|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_12V_Enable : 10|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_9V_Supply_Disable : 11|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ BMS00_5V_Peripheral_Enable : 12|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Gate_Drivers_Enable : 13|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Charge_MOSFET_Enable : 14|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Discharge_MOSFET_Enable : 15|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Pre_Charge_Enable : 16|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ AFE_Reset_Command : 17|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Overload_Clear : 18|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU
 SG_ Enable_3V3_Measure : 19|1@1+ (1,0) [0|1] ""  Tester_Tool,VCU

BO_ 2000 App_Cmd_Diagnostic_request: 8 VCU
 SG_ Vehicle_Type_Extended : 51|8@1+ (1,0) [0|8] ""  Tester_Tool
 SG_ Veh_Ctrl_Cmd : 0|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ App_Cmd : 8|8@1+ (1,0) [0|13] ""  Tester_Tool
 SG_ Proximity_Detect : 16|8@1+ (1,0) [0|3] ""  Tester_Tool
 SG_ Seat_Lock_Gpio : 24|8@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Som_Status : 32|8@1+ (1,0) [0|7] ""  Tester_Tool
 SG_ HMI_Ctrl_Cmd : 40|8@1+ (1,0) [0|124] ""  Tester_Tool
 SG_ Vehicle_type : 48|2@1+ (1,0) [0|3] ""  Tester_Tool
 SG_ ESCL_Timeout_Error : 50|1@1+ (1,0) [0|1] ""  Tester_Tool

BO_ 2001 HBC_Diagnostic_request: 8 VCU
 SG_ HBC_Left_Error_Count : 0|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ HBC_Right_Error_Count : 8|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ hbc_err_count_consecutive : 16|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ lin_mng_sts : 24|8@1+ (1,0) [0|5] ""  Tester_Tool
 SG_ lin_mng_proc_sts : 32|8@1+ (1,0) [0|2] ""  Tester_Tool
 SG_ lin_mng_state : 40|8@1+ (1,0) [0|2] ""  Tester_Tool
 SG_ Tx_Busy : 48|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Rx_Busy : 49|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ Bus_Busy : 50|1@1+ (1,0) [0|1] ""  Tester_Tool

BO_ 2002 VCU_Address_Register_Value1: 8 VCU
 SG_ Register_R0 : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_R1 : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2003 VCU_Address_Register_Value2: 8 VCU
 SG_ Register_R2 : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_R3 : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2004 VCU_Address_Register_Value3: 8 VCU
 SG_ Register_R12 : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_LR : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2005 VCU_Address_Register_Value4: 8 VCU
 SG_ Register_PC : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_PSR : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2006 VCU_Reset1: 8 VCU
 SG_ VCU_Low_Voltage_Detect_Reset : 1|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Loss_of_Clock_Reset : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Loss_of_Lock_Reset : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_CMU_Loss_of_Clock_Reset : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Watchdog : 5|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_External_Reset_Pin : 6|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Power_On_Reset : 7|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_JTAG_generated_reset : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Core_Lockup : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Software : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_MDM_AP_System_Reset_Request : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Stop_Acknowledge_Error : 13|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_Memory_Allocation_Failed : 16|8@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ VCU_System_Reset_Counter : 24|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ VCU_Memory_Leakage : 56|8@1+ (1,0) [0|255] "kb"  Tester_Tool

BO_ 2007 VCU_Reset2: 8 VCU
 SG_ Stack_Overflow_VCU : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 2018 BMS_AFE_Temperature_Debug: 8 BMS00
 SG_ AFE_fault_temperature_sensor : 32|4@1+ (1,0) [0|15] "DegC"  Tester_Tool,VCU
 SG_ AFE_Max_Temp : 16|16@1+ (0.1,-50) [-50|6503.5] "DegC"  Tester_Tool,VCU
 SG_ AFE_Min_Temp : 0|16@1+ (0.1,-50) [-50|6503.5] "DegC"  Tester_Tool,VCU

BO_ 2019 BMS_AFE_Fault_Debug: 8 BMS00
 SG_ AFE_Fault_Status3 : 32|16@1+ (1,0) [0|65535] ""  Tester_Tool,VCU
 SG_ AFE_Fault_Status2 : 16|16@1+ (1,0) [0|65535] ""  Tester_Tool,VCU
 SG_ AFE_Fault_Status1 : 0|16@1+ (1,0) [0|65535] ""  Tester_Tool,VCU

BO_ 2020 BMS_Address_Register_Value3: 8 BMS00
 SG_ Register_R12 : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_LR : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2021 BMS_Address_Register_Value4: 8 BMS00
 SG_ Register_PC : 0|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ Register_PSR : 32|32@1+ (1,0) [0|4294967295] ""  Tester_Tool

BO_ 2022 BMS_Reset1: 8 BMS00
 SG_ BMS_Low_Voltage_Detect_Reset : 1|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Loss_of_Clock_Reset : 2|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Loss_of_Lock_Reset : 3|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_CMU_Loss_of_Clock_Reset : 4|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Watchdog : 5|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_External_Reset_Pin : 6|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Power_On_Reset : 7|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_JTAG_generated_reset : 8|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Core_Lockup : 9|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Software : 10|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_MDM_AP_System_Reset_Request : 11|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Stop_Acknowledge_Error : 13|1@1+ (1,0) [0|1] ""  Tester_Tool
 SG_ BMS_Memory_Allocation_Failed : 16|8@1+ (1,0) [0|255] ""  Tester_Tool
 SG_ BMS_System_Reset_Counter : 24|32@1+ (1,0) [0|4294967295] ""  Tester_Tool
 SG_ BMS_Memory_Leakage : 56|8@1+ (1,0) [0|255] "kb"  Tester_Tool

BO_ 2023 BMS_Reset2: 8 BMS00
 SG_ Stack_Overflow : 0|64@1+ (1,0) [0|1.84467440737096E+019] ""  Tester_Tool

BO_ 1602 HU_DIGITAL_INPUT: 8 HU
 SG_ Backlight_Driver_Fault_Input : 0|1@1+ (1,0) [0|1] ""  VCU
 SG_ Ambient_Light_Sensor_Interrupt : 1|1@1+ (1,0) [0|1] ""  VCU
 SG_ Power_Good : 2|1@1+ (1,0) [0|1] ""  VCU
 SG_ WARNING_STATUS : 3|1@1+ (1,0) [0|1] ""  VCU
 SG_ ERROR_STATUS : 4|1@1+ (1,0) [0|1] ""  VCU
 SG_ WARNING_COUNTER : 8|8@1+ (1,0) [0|255] ""  VCU
 SG_ ERROR_COUNTER : 16|8@1+ (1,0) [0|255] ""  VCU
 SG_ RESET_COUNT : 24|8@1+ (1,0) [0|255] ""  VCU
 SG_ LAST_RESET_REASON : 32|8@1+ (1,0) [0|255] ""  VCU

BO_ 1601 HU_Data_1: 8 HU
 SG_ LED_NTC_ve_Input : 0|16@1+ (0.1,-40) [-40|200] "degC"  VCU
 SG_ Temperature_Sensor_Input : 16|16@1+ (0.1,-40) [-40|200] "degC"  VCU
 SG_ Main_Input_Supply : 32|16@1+ (1,0) [0|65535] "mV"  VCU

BO_ 1581 Charger_Connection_Status: 8 VCU
 SG_ Charger_Connected : 0|2@1+ (1,0) [0|3] ""  BMS00
 SG_ Charger_Plugged_In : 2|1@1+ (1,0) [0|1] ""  BMS00

BO_TX_BU_ 1711 : SOM,VCU;
BO_TX_BU_ 1680 : HU,VCU;
BO_TX_BU_ 1793 : Tester_Tool,VCU;


CM_ BO_ ********** "This is a message for not used signals, created by Vector CANdb++ DBC OLE DB Provider.";
CM_ SG_ 1603 Touch_State "Tells us the event when HMI was touched.";
CM_ SG_ 1041 PDUerrorCutOff_60 "PDU temperature - error FET cut off condition";
CM_ SG_ 1041 TempRiseChrgDtctn_60 "Charging will be stopped if Rise in battery temperature rise is > Z";
CM_ SG_ 1041 TempRiseDriveDtctn_60 "Temperature increase is > Y";
CM_ SG_ 1041 SOCRegenWarn "Deactivate regen and log a grade 1 fault while SOC > Threshold";
CM_ SG_ 1041 TempRegen_Warning "Deactivate regen and log a grade 1 fault while Battery Temp > Threshold";
CM_ SG_ 1041 CompSpikeDtctn_Fault "Cell Complementary Spike Detection";
CM_ SG_ 1041 CompSpike_Error "Cell Complementary Spike Detection Error";
CM_ SG_ 1041 CellDip_Fault "If Cell min dV/dt > xV, when Cell min < Y trigger a grade 1 fault
";
CM_ SG_ 1041 CellDip_Error "If >=3 Cell dip warnings, trigger a grade 2 fault";
CM_ SG_ 1041 BattTempAbNormal_Fault "Battery Temperature Fault Indication
If Avg (T3 and T5 sensor) < Avg of other 4  then Create Grade 1 fault";
CM_ SG_ 323 Cruise_Control_Availability "VCU will send the state of vehicle as locked, lock failed, unlocked or unlock failed based on ESCL status and head unit power status";
BA_DEF_ SG_  "NWM_WakeupAllowed" ENUM  "No","Yes";
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 100000;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","OnChangeAndIfActive","OnChangeAndIfActiveWithRepetition","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 100000000000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 50000;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 1000;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 999999;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","Cyclic and OnChange","Cyclic and OnWrite","Cyclic and IfActive","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed","NotUsed";
BA_DEF_ BO_  "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_  "NmMessage" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 50000;
BA_DEF_ BU_  "NmNode" ENUM  "No","Yes";
BA_DEF_ BU_  "NmStationAddress" INT 0 63;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_  "DBName" STRING ;
BA_DEF_  "NWMWakeupAllowed" ENUM  "No","Yes";
BA_DEF_  "BusType" STRING ;
BA_DEF_ SG_  "SystemSignalLongSymbol" STRING ;
BA_DEF_ BO_  "SystemMessageLongSymbol" STRING ;
BA_DEF_DEF_  "NWM_WakeupAllowed" "Yes";
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "NmMessage" "No";
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "NmNode" "No";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "NWMWakeupAllowed" "Yes";
BA_DEF_DEF_  "BusType" "CAN";
BA_DEF_DEF_  "SystemSignalLongSymbol" "";
BA_DEF_DEF_  "SystemMessageLongSymbol" "";
BA_ "NWMWakeupAllowed" 1;
BA_ "BusType" "CAN";
BA_ "DBName" "CAN1_S1x_Plus_V01_Rev3_0_36";
BA_ "GenMsgCycleTime" BO_ 1776 100;
BA_ "GenMsgCycleTime" BO_ 1049 100;
BA_ "GenMsgCycleTime" BO_ 1783 100;
BA_ "GenMsgCycleTime" BO_ 1751 100;
BA_ "GenMsgCycleTime" BO_ 1750 100;
BA_ "GenMsgCycleTime" BO_ 561 100;
BA_ "GenMsgCycleTime" BO_ 1056 5000;
BA_ "GenMsgSendType" BO_ 1673 8;
BA_ "GenMsgCycleTime" BO_ 553 100;
BA_ "GenMsgCycleTime" BO_ 1081 100;
BA_ "GenMsgCycleTime" BO_ 1044 100;
BA_ "GenMsgCycleTime" BO_ 1552 5000;
BA_ "GenMsgCycleTime" BO_ 1047 5000;
BA_ "GenMsgCycleTime" BO_ 793 5000;
BA_ "GenMsgCycleTime" BO_ 1656 5000;
BA_ "GenMsgCycleTime" BO_ 1775 5000;
BA_ "GenMsgCycleTime" BO_ 1774 5000;
BA_ "GenMsgCycleTime" BO_ 1745 100;
BA_ "GenMsgCycleTime" BO_ 1623 100;
BA_ "GenMsgCycleTime" BO_ 1742 5000;
BA_ "GenMsgCycleTime" BO_ 1739 100;
BA_ "GenMsgCycleTime" BO_ 1642 5000;
BA_ "GenMsgCycleTime" BO_ 1563 5000;
BA_ "GenMsgCycleTime" BO_ 1631 5000;
BA_ "GenMsgCycleTime" BO_ 1630 5000;
BA_ "GenMsgCycleTime" BO_ 1567 100;
BA_ "GenMsgCycleTime" BO_ 1566 5000;
BA_ "GenMsgCycleTime" BO_ 1564 5000;
BA_ "GenMsgCycleTime" BO_ 1645 100;
BA_ "GenMsgCycleTime" BO_ 1629 100;
BA_ "GenMsgCycleTime" BO_ 1611 100;
BA_ "GenMsgCycleTime" BO_ 550 100;
BA_ "GenMsgCycleTime" BO_ 1956 100;
BA_ "GenMsgCycleTime" BO_ 1955 100;
BA_ "GenMsgCycleTime" BO_ 1954 100;
BA_ "GenMsgCycleTime" BO_ 1953 100;
BA_ "GenMsgCycleTime" BO_ 276 100;
BA_ "GenMsgCycleTime" BO_ 277 100;
BA_ "GenMsgCycleTime" BO_ 1669 100;
BA_ "GenMsgCycleTime" BO_ 1668 100;
BA_ "GenMsgCycleTime" BO_ 430 100;
BA_ "GenMsgCycleTime" BO_ 429 100;
BA_ "GenMsgCycleTime" BO_ 428 100;
BA_ "GenMsgCycleTime" BO_ 427 100;
BA_ "GenMsgCycleTime" BO_ 1654 100;
BA_ "GenMsgCycleTime" BO_ 1653 100;
BA_ "GenMsgCycleTime" BO_ 1665 100;
BA_ "GenMsgSendType" BO_ 1702 8;
BA_ "GenMsgCycleTime" BO_ 1622 5000;
BA_ "GenMsgSendType" BO_ 1701 8;
BA_ "GenMsgSendType" BO_ 1700 8;
BA_ "GenMsgCycleTime" BO_ 1667 100;
BA_ "GenMsgCycleTime" BO_ 1666 100;
BA_ "GenMsgCycleTime" BO_ 1652 100;
BA_ "GenMsgCycleTime" BO_ 1651 100;
BA_ "GenMsgCycleTime" BO_ 1632 500;
BA_ "GenMsgCycleTime" BO_ 1591 5000;
BA_ "GenMsgCycleTime" BO_ 1590 5000;
BA_ "GenMsgCycleTime" BO_ 853 100;
BA_ "GenMsgCycleTime" BO_ 1621 2000;
BA_ "GenMsgCycleTime" BO_ 1610 100;
BA_ "GenMsgCycleTime" BO_ 1620 2000;
BA_ "GenMsgCycleTime" BO_ 100 20;
BA_ "GenMsgCycleTime" BO_ 544 2000;
BA_ "GenMsgCycleTime" BO_ 537 2000;
BA_ "GenMsgCycleTime" BO_ 536 2000;
BA_ "GenMsgCycleTime" BO_ 535 2000;
BA_ "GenMsgCycleTime" BO_ 324 100;
BA_ "GenMsgCycleTime" BO_ 549 100;
BA_ "GenMsgCycleTime" BO_ 820 100;
BA_ "GenMsgCycleTime" BO_ 850 100;
BA_ "GenMsgCycleTime" BO_ 401 100;
BA_ "GenMsgCycleTime" BO_ 852 100;
BA_ "GenMsgCycleTime" BO_ 1682 100;
BA_ "GenMsgSendType" BO_ 1697 8;
BA_ "GenMsgSendType" BO_ 1698 8;
BA_ "GenMsgCycleTime" BO_ 1780 1000;
BA_ "GenMsgCycleTime" BO_ 1740 100;
BA_ "GenMsgSendType" BO_ 1724 8;
BA_ "GenMsgCycleTime" BO_ 1572 5000;
BA_ "GenMsgCycleTime" BO_ 1650 100;
BA_ "GenMsgCycleTime" BO_ 1649 100;
BA_ "GenMsgCycleTime" BO_ 1648 100;
BA_ "GenMsgCycleTime" BO_ 1605 200;
BA_ "GenMsgCycleTime" BO_ 1603 1000;
BA_ "GenMsgSendType" BO_ 1744 8;
BA_ "GenMsgCycleTime" BO_ 1664 5000;
BA_ "GenMsgSendType" BO_ 2015 8;
BA_ "GenMsgCycleTime" BO_ 534 100;
BA_ "GenMsgCycleTime" BO_ 533 100;
BA_ "GenMsgCycleTime" BO_ 532 100;
BA_ "GenMsgCycleTime" BO_ 530 100;
BA_ "GenMsgCycleTime" BO_ 529 100;
BA_ "GenMsgSendType" BO_ 1971 8;
BA_ "GenMsgSendType" BO_ 1950 8;
BA_ "GenMsgCycleTime" BO_ 1765 100;
BA_ "GenMsgCycleTime" BO_ 1600 10;
BA_ "GenMsgCycleTime" BO_ 1727 100;
BA_ "GenMsgCycleTime" BO_ 1726 100;
BA_ "GenMsgCycleTime" BO_ 1329 100;
BA_ "GenMsgCycleTime" BO_ 1709 50;
BA_ "GenMsgCycleTime" BO_ 1708 50;
BA_ "GenMsgCycleTime" BO_ 31 100;
BA_ "GenMsgCycleTime" BO_ 151 100;
BA_ "GenMsgCycleTime" BO_ 136 100;
BA_ "GenMsgCycleTime" BO_ 3 100;
BA_ "GenMsgCycleTime" BO_ 11 100;
BA_ "GenMsgCycleTime" BO_ 64 100;
BA_ "GenMsgSendType" BO_ 63 8;
BA_ "GenMsgSendType" BO_ 61 8;
BA_ "GenMsgSendType" BO_ 79 8;
BA_ "GenMsgSendType" BO_ 147 8;
BA_ "GenMsgSendType" BO_ 155 8;
BA_ "GenMsgSendType" BO_ 163 8;
BA_ "GenMsgCycleTime" BO_ 272 5000;
BA_ "GenMsgCycleTime" BO_ 284 10;
BA_ "GenMsgCycleTime" BO_ 304 100;
BA_ "GenMsgCycleTime" BO_ 320 100;
BA_ "GenMsgCycleTime" BO_ 336 100;
BA_ "GenMsgCycleTime" BO_ 288 100;
BA_ "GenMsgCycleTime" BO_ 528 100;
BA_ "GenMsgCycleTime" BO_ 289 100;
BA_ "GenMsgCycleTime" BO_ 290 100;
BA_ "GenMsgCycleTime" BO_ 352 100;
BA_ "GenMsgCycleTime" BO_ 384 100;
BA_ "GenMsgCycleTime" BO_ 400 100;
BA_ "GenMsgSendType" BO_ 1795 8;
BA_ "GenMsgCycleTime" BO_ 1793 500;
BA_ "GenMsgCycleTime" BO_ 1040 500;
BA_ "GenMsgCycleTime" BO_ 1041 500;
BA_ "GenMsgCycleTime" BO_ 368 100;
BA_ "GenMsgCycleTime" BO_ 531 100;
BA_ "GenMsgCycleTime" BO_ 1568 5000;
BA_ "GenMsgCycleTime" BO_ 1554 5000;
BA_ "GenMsgCycleTime" BO_ 1657 5000;
BA_ "GenMsgCycleTime" BO_ 1616 5000;
BA_ "GenMsgSendType" BO_ 1617 8;
BA_ "GenMsgCycleTime" BO_ 1586 5000;
BA_ "GenMsgCycleTime" BO_ 1587 5000;
BA_ "GenMsgCycleTime" BO_ 1588 5000;
BA_ "GenMsgCycleTime" BO_ 1589 5000;
BA_ "GenMsgCycleTime" BO_ 787 100;
BA_ "GenMsgCycleTime" BO_ 275 100;
BA_ "GenMsgCycleTime" BO_ 1583 100;
BA_ "GenMsgCycleTime" BO_ 547 100;
BA_ "GenMsgSendType" BO_ 803 8;
BA_ "GenMsgCycleTime" BO_ 819 100;
BA_ "GenMsgCycleTime" BO_ 1565 100;
BA_ "GenMsgCycleTime" BO_ 1597 100;
BA_ "GenMsgCycleTime" BO_ 1613 100;
BA_ "GenMsgCycleTime" BO_ 1571 5000;
BA_ "GenMsgCycleTime" BO_ 323 100;
BA_ "GenMsgCycleTime" BO_ 339 100;
BA_ "GenMsgCycleTime" BO_ 347 100;
BA_ "GenMsgCycleTime" BO_ 851 100;
BA_ "GenMsgSendType" BO_ 1088 8;
BA_ "GenMsgCycleTime" BO_ 1216 200;
BA_ "GenMsgCycleTime" BO_ 1217 100;
BA_ "GenMsgSendType" BO_ 1217 8;
BA_ "GenMsgCycleTime" BO_ 99 20;
BA_ "GenMsgCycleTime" BO_ 867 200;
BA_ "GenMsgCycleTime" BO_ 420 100;
BA_ "GenMsgCycleTime" BO_ 424 100;
BA_ "GenMsgCycleTime" BO_ 423 100;
BA_ "GenMsgCycleTime" BO_ 672 100;
BA_ "GenMsgCycleTime" BO_ 421 100;
BA_ "GenMsgCycleTime" BO_ 422 100;
BA_ "GenMsgCycleTime" BO_ 425 100;
BA_ "GenMsgCycleTime" BO_ 426 100;
BA_ "GenMsgCycleTime" BO_ 1752 5000;
BA_ "GenMsgCycleTime" BO_ 1756 5000;
BA_ "GenMsgCycleTime" BO_ 1758 5000;
BA_ "GenMsgCycleTime" BO_ 1760 5000;
BA_ "GenMsgCycleTime" BO_ 1762 5000;
BA_ "GenMsgCycleTime" BO_ 1764 5000;
BA_ "GenMsgCycleTime" BO_ 20 100;
BA_ "GenMsgSendType" BO_ 1926 8;
BA_ "GenMsgSendType" BO_ 1968 8;
BA_ "GenMsgSendType" BO_ 1958 8;
BA_ "GenMsgSendType" BO_ 1972 8;
BA_ "GenMsgSendType" BO_ 1934 8;
BA_ "GenMsgSendType" BO_ 1969 8;
BA_ "GenMsgCycleTime" BO_ 1288 100;
BA_ "GenMsgCycleTime" BO_ 1636 500;
BA_ "GenMsgCycleTime" BO_ 1289 100;
BA_ "GenMsgCycleTime" BO_ 1296 100;
BA_ "GenMsgCycleTime" BO_ 1412 100;
BA_ "GenMsgCycleTime" BO_ 1413 100;
BA_ "GenMsgCycleTime" BO_ 1414 100;
BA_ "GenMsgCycleTime" BO_ 1415 100;
BA_ "GenMsgCycleTime" BO_ 1607 1000;
BA_ "GenMsgCycleTime" BO_ 1754 500;
BA_ "GenMsgCycleTime" BO_ 2000 100;
BA_ "GenMsgCycleTime" BO_ 2001 100;
BA_ "GenMsgSendType" BO_ 2002 8;
BA_ "GenMsgSendType" BO_ 2003 8;
BA_ "GenMsgSendType" BO_ 2004 8;
BA_ "GenMsgSendType" BO_ 2005 8;
BA_ "GenMsgSendType" BO_ 2006 8;
BA_ "GenMsgSendType" BO_ 2007 8;
BA_ "GenMsgSendType" BO_ 2018 8;
BA_ "GenMsgCycleTime" BO_ 2019 100;
BA_ "GenMsgSendType" BO_ 2019 8;
BA_ "GenMsgSendType" BO_ 2020 8;
BA_ "GenMsgSendType" BO_ 2021 8;
BA_ "GenMsgSendType" BO_ 2022 8;
BA_ "GenMsgSendType" BO_ 2023 8;
BA_ "GenMsgCycleTime" BO_ 1602 200;
BA_ "GenMsgCycleTime" BO_ 1601 200;
BA_ "GenMsgSendType" BO_ 1581 8;
BA_ "GenSigSendType" SG_ 1673 Multiplexer_Sig_1 7;
BA_ "GenSigSendType" SG_ 1673 VIN_Number_1to7 7;
BA_ "GenSigSendType" SG_ 1673 VIN_Number_8to14 7;
BA_ "GenSigSendType" SG_ 1673 VIN_Number_15to17 7;
BA_ "GenSigStartValue" SG_ 1642 AI_TEMP_SENSOR_MEASURE_MAX 50;
BA_ "GenSigStartValue" SG_ 1642 AI_TEMP_SENSOR_MEASURE_HBLB_MAX 50;
BA_ "GenSigStartValue" SG_ 1642 AI_DC_DC_TEMP_MEASURE_MAX 50;
BA_ "GenSigStartValue" SG_ 1563 AI_TEMP_SENSOR_MEASURE_MIN 50;
BA_ "GenSigStartValue" SG_ 1563 AI_TEMP_SENSOR_MEASURE_HBLB_MIN 50;
BA_ "GenSigStartValue" SG_ 1563 AI_DC_DC_TEMP_MEASURE_MIN 50;
BA_ "GenSigStartValue" SG_ 1566 AI_TEMP_SENSOR_MEASURE_HBLB_AVG 50;
BA_ "GenSigStartValue" SG_ 1566 AI_TEMP_SENSOR_MEASURE_AVG 50;
BA_ "GenSigStartValue" SG_ 1566 AI_DC_DC_TEMP_MEASURE_AVG 50;
BA_ "GenSigStartValue" SG_ 1645 Magnetometer_Zaxis 36000;
BA_ "GenSigStartValue" SG_ 1629 Magnetometer_Yaxis 36000;
BA_ "GenSigStartValue" SG_ 1629 Magnetometer_Xaxis 36000;
BA_ "GenSigStartValue" SG_ 1611 VCU_SOM_Temperature 500;
BA_ "GenSigStartValue" SG_ 1611 VCU_Temperature 500;
BA_ "GenSigStartValue" SG_ 277 Forced_Regen_Percentage 0;
BA_ "GenSigStartValue" SG_ 1669 Gyro_X_Theta 450;
BA_ "GenSigStartValue" SG_ 1666 Vehicle_Resultant_Inclination 1800;
BA_ "GenSigStartValue" SG_ 850 Display_SoC 0;
BA_ "GenSigStartValue" SG_ 401 Aggregated_current_Ah 0;
BA_ "GenSigStartValue" SG_ 401 Calculated_current_based_on_coulomb 0;
BA_ "GenSigSendType" SG_ 1697 Command_From_APP 7;
BA_ "GenSigSendType" SG_ 1698 Command_From_BLE 7;
BA_ "GenSigStartValue" SG_ 1740 SOM_State 2;
BA_ "GenSigSendType" SG_ 1724 Command_From_TCU 7;
BA_ "GenSigStartValue" SG_ 1650 OTA_Status_Information 3;
BA_ "GenSigSendType" SG_ 1744 Command_From_Vehicle 7;
BA_ "GenSigSendType" SG_ 2015 Functional_Diag_Request 7;
BA_ "GenSigSendType" SG_ 1971 SOM_Diag_Response 7;
BA_ "GenSigSendType" SG_ 1950 SOM_Diag_Request 7;
BA_ "GenSigStartValue" SG_ 1765 Instantaneous_Available_Energy 5000;
BA_ "GenSigStartValue" SG_ 1600 BMS00_MOSFETs_disconnection_failed_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_MOSFETs_connection_failed_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Precharge_failure_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Precharge_too_slow_Info_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Precharge_too_fast_Info_10ms 0;
BA_ "GenSigStartValue" SG_ 1600 BMS00_Short_circuit_detection_error 0;
BA_ "GenSigStartValue" SG_ 31 VCU_IGN_Status 1;
BA_ "GenSigStartValue" SG_ 151 MCU_Sleep_WakeUp_ACK 0;
BA_ "GenSigStartValue" SG_ 136 Wake_up_Req_BMS 0;
BA_ "GenSigStartValue" SG_ 3 WakeUp_Sleep_Request 2;
BA_ "GenSigStartValue" SG_ 11 Battery_Wakeup_Request 0;
BA_ "GenSigStartValue" SG_ 11 Serial_Number 0;
BA_ "GenSigStartValue" SG_ 11 Firmware_version 0;
BA_ "GenSigStartValue" SG_ 11 Hardware_version 0;
BA_ "GenSigStartValue" SG_ 11 Manufacturer_Code 0;
BA_ "GenSigStartValue" SG_ 11 Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 64 MBMS_Wake_Up_Reason 0;
BA_ "GenSigSendType" SG_ 63 VCU_Seed 7;
BA_ "GenSigStartValue" SG_ 63 VCU_Seed 0;
BA_ "GenSigSendType" SG_ 61 BMS_Key 7;
BA_ "GenSigStartValue" SG_ 61 BMS_Key 0;
BA_ "GenSigSendType" SG_ 79 Auth_Status_BMS00 7;
BA_ "GenSigStartValue" SG_ 79 Auth_Status_BMS00 0;
BA_ "GenSigSendType" SG_ 147 VCU_to_MCU_Seed 7;
BA_ "GenSigStartValue" SG_ 147 VCU_to_MCU_Seed 0;
BA_ "GenSigSendType" SG_ 155 MCU_Key 7;
BA_ "GenSigStartValue" SG_ 155 MCU_Key 0;
BA_ "GenSigSendType" SG_ 163 VCU_Auth_Response_to_MCU 7;
BA_ "GenSigStartValue" SG_ 163 VCU_Auth_Response_to_MCU 0;
BA_ "GenSigStartValue" SG_ 284 Contactor_state_00 0;
BA_ "GenSigStartValue" SG_ 284 Battery_voltage_OCV_00 0;
BA_ "GenSigStartValue" SG_ 284 Pack_SOC_00 0;
BA_ "GenSigStartValue" SG_ 284 BMS_Mode_00 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Precharge_failure_status_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_charge_inhibit_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_discharge_inhibit_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Derate_Drive_Current_Flag_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Derate_Charge_Current_Flag_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Inhibit_Regen_Fault_10ms 0;
BA_ "GenSigStartValue" SG_ 284 Battery_Permanent_Fault_10ms 0;
BA_ "GenSigStartValue" SG_ 284 BMS00_Short_circuit_detection_error_10ms 0;
BA_ "GenSigStartValue" SG_ 304 Charge_current_limit_00 0;
BA_ "GenSigStartValue" SG_ 304 Discharge_current_limit_00 40000;
BA_ "GenSigStartValue" SG_ 304 Charge_Voltage_limit_00 0;
BA_ "GenSigStartValue" SG_ 304 Charging_Mode_00 0;
BA_ "GenSigStartValue" SG_ 320 Regen_Power_Limit_00 0;
BA_ "GenSigStartValue" SG_ 320 Discharge_Power_Limit_00 50000;
BA_ "GenSigStartValue" SG_ 320 Battery_Current_00 40000;
BA_ "GenSigStartValue" SG_ 336 Bus_Voltage_00 0;
BA_ "GenSigStartValue" SG_ 336 Delta_Voltage_00 0;
BA_ "GenSigStartValue" SG_ 336 Available_Capacity_00 0;
BA_ "GenSigStartValue" SG_ 336 Pack_SoH 0;
BA_ "GenSigStartValue" SG_ 288 Overall_Battery_Current 30000;
BA_ "GenSigStartValue" SG_ 288 Overall_SOC 0;
BA_ "GenSigStartValue" SG_ 288 Overall_Charge_Voltage_limit 0;
BA_ "GenSigStartValue" SG_ 288 Charger_Mode_Request 0;
BA_ "GenSigStartValue" SG_ 528 Throttle_Deviation_Status 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Charge_current_limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Discharge_current_limit 40000;
BA_ "GenSigStartValue" SG_ 289 Overall_Regen_Power_Limit 0;
BA_ "GenSigStartValue" SG_ 289 Overall_Discharge_Power_Limit 50000;
BA_ "GenSigStartValue" SG_ 290 Battery_Precharge_failure_status_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Precharge_failure_status_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Precharge_failure_status_02 0;
BA_ "GenSigStartValue" SG_ 290 Battery_charge_inhibit_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_charge_inhibit_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_charge_inhibit_02 0;
BA_ "GenSigStartValue" SG_ 290 Battery_discharge_inhibit_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_discharge_inhibit_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_discharge_inhibit_02 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Drive_Current_Flag_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Drive_Current_Flag_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Drive_Current_Flag_02 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Charge_Current_Flag_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Charge_Current_Flag_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Derate_Charge_Current_Flag_02 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Inhibit_Regen_Fault_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Inhibit_Regen_Fault_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Inhibit_Regen_Fault_02 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Permanent_Fault_00 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Permanent_Fault_01 0;
BA_ "GenSigStartValue" SG_ 290 Battery_Permanent_Fault_02 0;
BA_ "GenSigStartValue" SG_ 352 Balancing_Status_00 0;
BA_ "GenSigStartValue" SG_ 352 Effective_Battery_Temperature_00 500;
BA_ "GenSigStartValue" SG_ 352 Battery_Temperature_Min_00 500;
BA_ "GenSigStartValue" SG_ 352 Battery_Temperature_Max_00 500;
BA_ "GenSigStartValue" SG_ 384 Charging_time_Max 0;
BA_ "GenSigStartValue" SG_ 384 DC_output_voltage_limit_parameter 0;
BA_ "GenSigStartValue" SG_ 384 Charging_Rate 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_chargeSC_80 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_chargeFC_100 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_Full_chargeSC_00 0;
BA_ "GenSigStartValue" SG_ 400 Time_to_chargeFC_80 0;
BA_ "GenSigSendType" SG_ 1795 EOL_BMS_TimeStamp 7;
BA_ "GenSigStartValue" SG_ 1795 EOL_BMS_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1793 VCU_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell1 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell2 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell3 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell4 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell5 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell6 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell7 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell8 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell9 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell10 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell11 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell12 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_sensor_failureCell13 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell1 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell2 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell3 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell4 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell5 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell6 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell7 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell8 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell9 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell10 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell11 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell12 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Voltage_out_of_range_Cell13 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature1_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature2_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature3_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature4_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature1_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature2_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature3_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Battery_pack_temperature4_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Current_sensor_failure_Open_circuit 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Current_sensor_failure_Short_circuit 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_current_charge_Warning 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_VOOR_CutOff_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Precharge_too_fast_Info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Precharge_too_slow_Info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Precharge_failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_MOSFETs_connection_failed 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_MOSFETs_disconnection_failed 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_PDU_temperature_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_PDU_temperature_error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_voltage_charge_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_voltage_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_voltage_charge_Permanent_Fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_voltage_regen_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_voltage_regen_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Under_voltage_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Under_voltage_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Under_voltage_Permanent_Fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_temperature_charge_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_temperature_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_temperature_drive_warning_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_temperature_drive_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_temperature_due_to_Cell_vent_error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_temperature_due_to_Cell_vent_Failure 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Short_circuit_detection_error_info 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Short_circuit_detection_permanent_fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Cell_failure_permanent_fault 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Over_current_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1040 BMS00_Low_temperature_during_charging_warning_info 0;
BA_ "GenSigStartValue" SG_ 1041 BMS00_Low_temperature_during_charging_Error 0;
BA_ "GenSigStartValue" SG_ 1041 BMS00_Low_temperature_during_driving_Warning 0;
BA_ "GenSigStartValue" SG_ 1041 BMS00_Low_temperature_during_driving_Error 0;
BA_ "GenSigStartValue" SG_ 1041 Over_time_to_fast_charge_Error 0;
BA_ "GenSigStartValue" SG_ 1041 BMS00_Voltage_sensor_failureCell14 0;
BA_ "GenSigStartValue" SG_ 1041 BMS00_Voltage_out_of_range_Cell14 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature5_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature6_sensor_failure 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature5_out_of_range 0;
BA_ "GenSigStartValue" SG_ 1041 Battery_pack_temperature6_out_of_range 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_1 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_2 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_3 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_4 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_5 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_6 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_7 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_8 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_9 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_10 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_11 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_12 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_13 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Balancing_Status_Battery_Cell_14 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Cell_Min_Voltage 0;
BA_ "GenSigStartValue" SG_ 368 BMS00_Cell_Max_Voltage 0;
BA_ "GenSigStartValue" SG_ 531 Charger_Status_Signal 0;
BA_ "GenSigStartValue" SG_ 531 Charger_Mode 0;
BA_ "GenSigStartValue" SG_ 531 Charging_Voltage_Available_range 0;
BA_ "GenSigStartValue" SG_ 531 Charging_Current_Available_range 0;
BA_ "GenSigStartValue" SG_ 531 Slow_Charger_Fault_Information 0;
BA_ "GenSigStartValue" SG_ 531 Charger_output_Short_circuit_error 0;
BA_ "GenSigStartValue" SG_ 531 Charger_under_temperature_error 0;
BA_ "GenSigStartValue" SG_ 531 Charger_over_temperature_error 0;
BA_ "GenSigStartValue" SG_ 531 Low_AC_voltage_error 0;
BA_ "GenSigStartValue" SG_ 531 Rectifier_hardware_error 0;
BA_ "GenSigStartValue" SG_ 531 Authentication_Error 0;
BA_ "GenSigStartValue" SG_ 531 Battery_Parameter_Time_out_error 0;
BA_ "GenSigStartValue" SG_ 531 Data_Corruption_error 0;
BA_ "GenSigStartValue" SG_ 531 Charge_control_message_timeout_error 0;
BA_ "GenSigStartValue" SG_ 1568 MBMS_Firmware_Version 0;
BA_ "GenSigStartValue" SG_ 1554 BMS00_Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 1657 VCU_Firmware_Version 0;
BA_ "GenSigStartValue" SG_ 1616 BMS_ASW_Version 0;
BA_ "GenSigSendType" SG_ 1617 Battery_Cell_Type 7;
BA_ "GenSigStartValue" SG_ 1617 Battery_Cell_Type 0;
BA_ "GenSigSendType" SG_ 1617 Battery_cell_in_Series 7;
BA_ "GenSigStartValue" SG_ 1617 Battery_cell_in_Series 0;
BA_ "GenSigSendType" SG_ 1617 Battery_cell_in_Parallel 7;
BA_ "GenSigStartValue" SG_ 1617 Battery_cell_in_Parallel 0;
BA_ "GenSigSendType" SG_ 1617 CAN_Database 7;
BA_ "GenSigStartValue" SG_ 1617 CAN_Database 0;
BA_ "GenSigStartValue" SG_ 1586 VCU_Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 1588 HU_Firmware_Version 0;
BA_ "GenSigStartValue" SG_ 1589 HU_Bootloader_version 0;
BA_ "GenSigStartValue" SG_ 787 Vehicle_speed_in_kmph 0;
BA_ "GenSigStartValue" SG_ 787 MCU_Temperature 0;
BA_ "GenSigStartValue" SG_ 787 Motor_Temprature 0;
BA_ "GenSigStartValue" SG_ 787 Motor_RPM 0;
BA_ "GenSigStartValue" SG_ 275 MCU_Power_Status 0;
BA_ "GenSigStartValue" SG_ 275 Throttle_Value1 0;
BA_ "GenSigStartValue" SG_ 275 Throttle_Value2 0;
BA_ "GenSigStartValue" SG_ 275 Regen_Status 0;
BA_ "GenSigStartValue" SG_ 275 Cruise_Control_Status 0;
BA_ "GenSigStartValue" SG_ 275 Vehicle_Discharge_Mode 0;
BA_ "GenSigStartValue" SG_ 275 Hill_Hold_Response 0;
BA_ "GenSigStartValue" SG_ 275 MCU_Voltage 0;
BA_ "GenSigStartValue" SG_ 1583 CAN_Throttle_Speed_Mode 0;
BA_ "GenSigStartValue" SG_ 1583 CAN_Throttle_Torque_Mode 0;
BA_ "GenSigStartValue" SG_ 547 MCU_DC_Current 30000;
BA_ "GenSigStartValue" SG_ 547 Motor_Current 1000;
BA_ "GenSigStartValue" SG_ 547 Motor_Torque 0;
BA_ "GenSigStartValue" SG_ 547 Distance_covered_for_current_trip 0;
BA_ "GenSigSendType" SG_ 803 Disconnect_ACK 7;
BA_ "GenSigStartValue" SG_ 803 Disconnect_ACK 0;
BA_ "GenSigStartValue" SG_ 819 Software_overcurrent_protection_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Software_overvoltage_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Drive_Protection 0;
BA_ "GenSigStartValue" SG_ 819 Failure_for_motor_parameter_tuning 0;
BA_ "GenSigStartValue" SG_ 819 Drive_Overload_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 U_phase_hall_fault 0;
BA_ "GenSigStartValue" SG_ 819 Drive_overtemperature_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Motor_overtemperature_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Encoder_disconnection_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Overvoltagebaseline_of_hardware_is_wrong_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Stalling_Fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 DC_Bus_undervoltage_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 CAN_communication_abnormal_failure 0;
BA_ "GenSigStartValue" SG_ 819 Motor_over_speed_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Motor_temperature_sensor_disconnection_fault_Grade_3 0;
BA_ "GenSigStartValue" SG_ 819 Hardware_overcurrent_fault 0;
BA_ "GenSigStartValue" SG_ 819 Hardware_overvoltage_fault 0;
BA_ "GenSigStartValue" SG_ 819 Drive_power_under_voltage_fault 0;
BA_ "GenSigStartValue" SG_ 819 The_resolver_connector_is_loose_and_abnormal 0;
BA_ "GenSigStartValue" SG_ 819 Controller_drive_abnormal_fault 0;
BA_ "GenSigStartValue" SG_ 819 Power_supplyof_drive_board_exceeds_the_upper_limit 0;
BA_ "GenSigStartValue" SG_ 819 Low_voltage_power_supply_under_voltage_fault 0;
BA_ "GenSigStartValue" SG_ 819 U_phase_Hall_disconnection_fault 0;
BA_ "GenSigStartValue" SG_ 819 V_phase_Hall_disconnection_fault 0;
BA_ "GenSigStartValue" SG_ 819 W_phase_Hall_disconnection_fault 0;
BA_ "GenSigStartValue" SG_ 819 V_phase_Hall_abnormal_fault 0;
BA_ "GenSigStartValue" SG_ 819 W_phase_Hall_abnormal_fault 0;
BA_ "GenSigStartValue" SG_ 819 Throttle_Wiper1_Fail 0;
BA_ "GenSigStartValue" SG_ 819 Throttle_Wiper2_Fail 0;
BA_ "GenSigStartValue" SG_ 1565 Accelerometer_Xaxis 2000;
BA_ "GenSigStartValue" SG_ 1565 Gyroscope_Xaxis 2000;
BA_ "GenSigStartValue" SG_ 1597 Accelerometer_Yaxis 2000;
BA_ "GenSigStartValue" SG_ 1597 Gyroscope_Yaxis 2000;
BA_ "GenSigStartValue" SG_ 1613 Accelerometer_Zaxis 2000;
BA_ "GenSigStartValue" SG_ 1613 Gyroscope_Zaxis 2000;
BA_ "GenSigStartValue" SG_ 1571 MCU_HW_Version 0;
BA_ "GenSigStartValue" SG_ 1571 MCU_Firmware_Version 0;
BA_ "GenSigStartValue" SG_ 323 Vehicle_Mode_Level_1 0;
BA_ "GenSigStartValue" SG_ 323 Vehicle_Mode_Level_2 0;
BA_ "GenSigStartValue" SG_ 323 Vehicle_Mode_Level_3 0;
BA_ "GenSigStartValue" SG_ 323 Vehicle_Mode_Level_4 0;
BA_ "GenSigStartValue" SG_ 323 Drive_Request 0;
BA_ "GenSigStartValue" SG_ 323 Odometer 0;
BA_ "GenSigStartValue" SG_ 323 Charging_status_of_AUX_battery 0;
BA_ "GenSigStartValue" SG_ 339 DCDC_Output_Voltage 0;
BA_ "GenSigStartValue" SG_ 339 SSB_Button_Status 0;
BA_ "GenSigStartValue" SG_ 347 Discharge_Mode 0;
BA_ "GenSigStartValue" SG_ 347 Front_Brake_Status 0;
BA_ "GenSigStartValue" SG_ 347 Rear_Brake_Status 0;
BA_ "GenSigStartValue" SG_ 347 Cruise_Control_Request 0;
BA_ "GenSigStartValue" SG_ 347 Gradient_Information_X_axis 360;
BA_ "GenSigStartValue" SG_ 347 Gradient_Information_Y_axis 360;
BA_ "GenSigStartValue" SG_ 347 Gradient_Information_Z_axis 360;
BA_ "GenSigStartValue" SG_ 851 Side_Stand_Status 0;
BA_ "GenSigStartValue" SG_ 851 Seat_Lock_Status 0;
BA_ "GenSigStartValue" SG_ 851 Cruise_Control_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Vehicle_Range 0;
BA_ "GenSigStartValue" SG_ 851 Right_Indicator_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Left_Indicator_TTL 0;
BA_ "GenSigStartValue" SG_ 851 High_Beam_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Service_Indicator_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Vehicle_Error_Indicator_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Charge_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Position_Lamp_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Brake_Pad_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Low_Beam_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Low_Battery_Alert_TTL 0;
BA_ "GenSigStartValue" SG_ 851 Indicator_On_Reason 0;
BA_ "GenSigStartValue" SG_ 851 Ambient_Brightness_Level 0;
BA_ "GenSigSendType" SG_ 1088 StrLockUnlockCmd 7;
BA_ "GenSigStartValue" SG_ 1088 StrLockUnlockCmd 0;
BA_ "GenSigStartValue" SG_ 1216 ESCL_Status 0;
BA_ "GenSigSendType" SG_ 1217 LockUnlockErrorIndication 7;
BA_ "GenSigStartValue" SG_ 1217 LockUnlockErrorIndication 0;
BA_ "GenSigStartValue" SG_ 99 Wakeup_Reason 0;
BA_ "GenSigStartValue" SG_ 867 Ambient_Light_Sensor_Value 0;
BA_ "GenSigStartValue" SG_ 420 BMS00_Pack_Temperature_04 500;
BA_ "GenSigStartValue" SG_ 420 BMS00_Pack_Temperature_01 500;
BA_ "GenSigStartValue" SG_ 420 BMS00_Pack_Temperature_02 500;
BA_ "GenSigStartValue" SG_ 420 BMS00_Pack_Temperature_03 500;
BA_ "GenSigStartValue" SG_ 424 BMS00_PDU_Temperature_01 400;
BA_ "GenSigStartValue" SG_ 424 BMS00_PDU_Temperature_02 400;
BA_ "GenSigStartValue" SG_ 424 BMS00_Battery_Pack_Voltage_Measured 0;
BA_ "GenSigStartValue" SG_ 424 BMS00_Cell_Balancing_Temperature 4000;
BA_ "GenSigStartValue" SG_ 423 BMS00_Battery_Temperature_Display 50;
BA_ "GenSigStartValue" SG_ 423 BMS00_PDU_Delta_Temperature 100;
BA_ "GenSigStartValue" SG_ 423 BMS00_Pack_Temperature_05 500;
BA_ "GenSigStartValue" SG_ 423 BMS00_Pack_Temperature_06 500;
BA_ "GenSigStartValue" SG_ 672 BMS_EPOCH_Time 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_1 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_2 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_3 0;
BA_ "GenSigStartValue" SG_ 421 Cell_Voltage_Cell_4 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_5 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_6 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_7 0;
BA_ "GenSigStartValue" SG_ 422 Cell_Voltage_Cell_8 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_9 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_10 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_11 0;
BA_ "GenSigStartValue" SG_ 425 Cell_Voltage_Cell_12 0;
BA_ "GenSigStartValue" SG_ 426 Cell_Voltage_Cell_13 0;
BA_ "GenSigStartValue" SG_ 426 Cell_Voltage_Cell_14 0;
BA_ "GenSigStartValue" SG_ 1752 Cycle_Number 0;
BA_ "GenSigStartValue" SG_ 1752 Kwhr_Accumulated_Current_Cycle 0;
BA_ "GenSigStartValue" SG_ 1752 Factored_Charge_Amphr 0;
BA_ "GenSigStartValue" SG_ 1752 Balancing_Temperature 80;
BA_ "GenSigStartValue" SG_ 1756 Total_Amphr_Discharged_in_the_Mode 10000;
BA_ "GenSigStartValue" SG_ 1756 Total_Kwhr_Dicharged_in_the_Mode 6500;
BA_ "GenSigStartValue" SG_ 1756 Number_of_Stops_during_Drive 0;
BA_ "GenSigStartValue" SG_ 1756 Number_of_Charge_Stops_during_charge 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Amphr_Charged_in_the_Mode 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Kwhr_Charged_in_the_Mode 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Active_Duration_of_the_Mode 0;
BA_ "GenSigStartValue" SG_ 1758 Total_Regenerative_Amphr 0;
BA_ "GenSigStartValue" SG_ 1760 Factored_Discharge_Amphr 65530;
BA_ "GenSigStartValue" SG_ 1760 Total_Kwhr_Consumption_in_30sec_Slot 650;
BA_ "GenSigStartValue" SG_ 1760 Total_Ah_Lost_in_charging_Aux_battery_in_Idle 430;
BA_ "GenSigStartValue" SG_ 1760 Total_Charge_Time 0;
BA_ "GenSigStartValue" SG_ 1762 Max_Cell_Deviation_Observed_during_Discharge 0;
BA_ "GenSigStartValue" SG_ 1762 Max_Cell_Deviation_Observed_during_Charge 0;
BA_ "GenSigStartValue" SG_ 1764 Number_of_times_balancing_started_due_to_Deviation 0;
BA_ "GenSigStartValue" SG_ 1764 Total_Amphr_Lost_during_Balancing 0;
BA_ "GenSigStartValue" SG_ 1764 Total_Balancing_Duration 0;
BA_ "GenSigStartValue" SG_ 20 Fast_Charger_Plug_Status 0;
BA_ "GenSigStartValue" SG_ 20 Fast_Charger_Contactor_Status 0;
BA_ "GenSigSendType" SG_ 1926 VCU_Diag_Request 7;
BA_ "GenSigStartValue" SG_ 1926 VCU_Diag_Request 0;
BA_ "GenSigSendType" SG_ 1968 VCU_Diag_Response 7;
BA_ "GenSigStartValue" SG_ 1968 VCU_Diag_Response 0;
BA_ "GenSigSendType" SG_ 1958 MCU_Diag_Request 7;
BA_ "GenSigStartValue" SG_ 1958 MCU_Diag_Request 0;
BA_ "GenSigSendType" SG_ 1972 MCU_Diag_Response 7;
BA_ "GenSigStartValue" SG_ 1972 MCU_Diag_Response 0;
BA_ "GenSigSendType" SG_ 1934 BMS0_Diag_Request 7;
BA_ "GenSigStartValue" SG_ 1934 BMS0_Diag_Request 0;
BA_ "GenSigSendType" SG_ 1969 BMS0_Diag_Response 7;
BA_ "GenSigStartValue" SG_ 1969 BMS0_Diag_Response 0;
BA_ "GenSigStartValue" SG_ 1288 Charging_system_error 0;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_malfunction 0;
BA_ "GenSigStartValue" SG_ 1288 EV_incompatibility 0;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_stop_control 1;
BA_ "GenSigStartValue" SG_ 1288 EV_supply_equipment_status 0;
BA_ "GenSigStartValue" SG_ 1288 Vehicle_connector_latched 0;
BA_ "GenSigStartValue" SG_ 1607 Charge_AmpHr_00 0;
BA_ "GenSigStartValue" SG_ 1607 Discharge_AmpHr_00 65530;
BA_ "GenSigStartValue" SG_ 1754 BMS00_9V_Supply_Status 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_12V_Supply_voltage_Status 0;
BA_ "GenSigStartValue" SG_ 1754 SBC_Limp_Input 0;
BA_ "GenSigStartValue" SG_ 1754 AFE_Fault_Input 0;
BA_ "GenSigStartValue" SG_ 1754 Charge_Over_Current 0;
BA_ "GenSigStartValue" SG_ 1754 Discharge_Over_current 0;
BA_ "GenSigStartValue" SG_ 1754 RTC_Interrupt 0;
BA_ "GenSigStartValue" SG_ 1754 RTC_Clock_IN 0;
BA_ "GenSigStartValue" SG_ 1754 Charger_Plug_Sense 0;
BA_ "GenSigStartValue" SG_ 1754 BMS_Status 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_12V_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_9V_Supply_Disable 0;
BA_ "GenSigStartValue" SG_ 1754 BMS00_5V_Peripheral_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Gate_Drivers_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Charge_MOSFET_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Discharge_MOSFET_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 Pre_Charge_Enable 0;
BA_ "GenSigStartValue" SG_ 1754 AFE_Reset_Command 0;
BA_ "GenSigStartValue" SG_ 1754 Overload_Clear 0;
BA_ "GenSigStartValue" SG_ 1754 Enable_3V3_Measure 0;
BA_ "GenSigStartValue" SG_ 2000 Veh_Ctrl_Cmd 0;
BA_ "GenSigStartValue" SG_ 2000 App_Cmd 0;
BA_ "GenSigStartValue" SG_ 2000 Proximity_Detect 0;
BA_ "GenSigStartValue" SG_ 2000 Seat_Lock_Gpio 0;
BA_ "GenSigStartValue" SG_ 2000 Som_Status 0;
BA_ "GenSigStartValue" SG_ 2000 HMI_Ctrl_Cmd 0;
BA_ "GenSigStartValue" SG_ 2000 Vehicle_type 0;
BA_ "GenSigStartValue" SG_ 2000 ESCL_Timeout_Error 0;
BA_ "GenSigStartValue" SG_ 2001 HBC_Left_Error_Count 0;
BA_ "GenSigStartValue" SG_ 2001 HBC_Right_Error_Count 0;
BA_ "GenSigStartValue" SG_ 2001 hbc_err_count_consecutive 0;
BA_ "GenSigStartValue" SG_ 2001 lin_mng_sts 0;
BA_ "GenSigStartValue" SG_ 2001 lin_mng_proc_sts 0;
BA_ "GenSigStartValue" SG_ 2001 lin_mng_state 0;
BA_ "GenSigStartValue" SG_ 2001 Tx_Busy 0;
BA_ "GenSigStartValue" SG_ 2001 Rx_Busy 0;
BA_ "GenSigStartValue" SG_ 2001 Bus_Busy 0;
BA_ "GenSigSendType" SG_ 2002 Register_R0 7;
BA_ "GenSigStartValue" SG_ 2002 Register_R0 0;
BA_ "GenSigSendType" SG_ 2002 Register_R1 7;
BA_ "GenSigStartValue" SG_ 2002 Register_R1 0;
BA_ "GenSigSendType" SG_ 2003 Register_R2 7;
BA_ "GenSigStartValue" SG_ 2003 Register_R2 0;
BA_ "GenSigSendType" SG_ 2003 Register_R3 7;
BA_ "GenSigStartValue" SG_ 2003 Register_R3 0;
BA_ "GenSigSendType" SG_ 2004 Register_R12 7;
BA_ "GenSigStartValue" SG_ 2004 Register_R12 0;
BA_ "GenSigSendType" SG_ 2004 Register_LR 7;
BA_ "GenSigStartValue" SG_ 2004 Register_LR 0;
BA_ "GenSigSendType" SG_ 2005 Register_PC 7;
BA_ "GenSigStartValue" SG_ 2005 Register_PC 0;
BA_ "GenSigSendType" SG_ 2005 Register_PSR 7;
BA_ "GenSigStartValue" SG_ 2005 Register_PSR 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Low_Voltage_Detect_Reset 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Low_Voltage_Detect_Reset 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Loss_of_Clock_Reset 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Loss_of_Clock_Reset 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Loss_of_Lock_Reset 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Loss_of_Lock_Reset 0;
BA_ "GenSigSendType" SG_ 2006 VCU_CMU_Loss_of_Clock_Reset 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_CMU_Loss_of_Clock_Reset 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Watchdog 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Watchdog 0;
BA_ "GenSigSendType" SG_ 2006 VCU_External_Reset_Pin 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_External_Reset_Pin 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Power_On_Reset 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Power_On_Reset 0;
BA_ "GenSigSendType" SG_ 2006 VCU_JTAG_generated_reset 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_JTAG_generated_reset 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Core_Lockup 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Core_Lockup 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Software 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Software 0;
BA_ "GenSigSendType" SG_ 2006 VCU_MDM_AP_System_Reset_Request 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_MDM_AP_System_Reset_Request 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Stop_Acknowledge_Error 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Stop_Acknowledge_Error 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Memory_Allocation_Failed 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Memory_Allocation_Failed 0;
BA_ "GenSigSendType" SG_ 2006 VCU_System_Reset_Counter 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_System_Reset_Counter 0;
BA_ "GenSigSendType" SG_ 2006 VCU_Memory_Leakage 7;
BA_ "GenSigStartValue" SG_ 2006 VCU_Memory_Leakage 0;
BA_ "GenSigSendType" SG_ 2007 Stack_Overflow_VCU 7;
BA_ "GenSigStartValue" SG_ 2007 Stack_Overflow_VCU 0;
BA_ "GenSigStartValue" SG_ 2018 AFE_Max_Temp 500;
BA_ "GenSigStartValue" SG_ 2018 AFE_Min_Temp 500;
BA_ "GenSigSendType" SG_ 2020 Register_R12 7;
BA_ "GenSigStartValue" SG_ 2020 Register_R12 0;
BA_ "GenSigSendType" SG_ 2020 Register_LR 7;
BA_ "GenSigStartValue" SG_ 2020 Register_LR 0;
BA_ "GenSigSendType" SG_ 2021 Register_PC 7;
BA_ "GenSigStartValue" SG_ 2021 Register_PC 0;
BA_ "GenSigSendType" SG_ 2021 Register_PSR 7;
BA_ "GenSigStartValue" SG_ 2021 Register_PSR 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Low_Voltage_Detect_Reset 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Low_Voltage_Detect_Reset 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Loss_of_Clock_Reset 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Loss_of_Clock_Reset 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Loss_of_Lock_Reset 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Loss_of_Lock_Reset 0;
BA_ "GenSigSendType" SG_ 2022 BMS_CMU_Loss_of_Clock_Reset 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_CMU_Loss_of_Clock_Reset 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Watchdog 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Watchdog 0;
BA_ "GenSigSendType" SG_ 2022 BMS_External_Reset_Pin 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_External_Reset_Pin 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Power_On_Reset 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Power_On_Reset 0;
BA_ "GenSigSendType" SG_ 2022 BMS_JTAG_generated_reset 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_JTAG_generated_reset 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Core_Lockup 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Core_Lockup 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Software 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Software 0;
BA_ "GenSigSendType" SG_ 2022 BMS_MDM_AP_System_Reset_Request 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_MDM_AP_System_Reset_Request 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Stop_Acknowledge_Error 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Stop_Acknowledge_Error 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Memory_Allocation_Failed 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Memory_Allocation_Failed 0;
BA_ "GenSigSendType" SG_ 2022 BMS_System_Reset_Counter 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_System_Reset_Counter 0;
BA_ "GenSigSendType" SG_ 2022 BMS_Memory_Leakage 7;
BA_ "GenSigStartValue" SG_ 2022 BMS_Memory_Leakage 0;
BA_ "GenSigSendType" SG_ 2023 Stack_Overflow 7;
BA_ "GenSigStartValue" SG_ 2023 Stack_Overflow 0;
BA_ "GenSigStartValue" SG_ 1602 Backlight_Driver_Fault_Input 0;
BA_ "GenSigStartValue" SG_ 1602 Ambient_Light_Sensor_Interrupt 0;
BA_ "GenSigStartValue" SG_ 1602 Power_Good 0;
BA_ "GenSigStartValue" SG_ 1602 WARNING_STATUS 0;
BA_ "GenSigStartValue" SG_ 1602 ERROR_STATUS 0;
BA_ "GenSigStartValue" SG_ 1602 WARNING_COUNTER 0;
BA_ "GenSigStartValue" SG_ 1602 ERROR_COUNTER 0;
BA_ "GenSigStartValue" SG_ 1602 RESET_COUNT 0;
BA_ "GenSigStartValue" SG_ 1602 LAST_RESET_REASON 0;
BA_ "GenSigStartValue" SG_ 1601 LED_NTC_ve_Input 400;
BA_ "GenSigStartValue" SG_ 1601 Temperature_Sensor_Input 400;
BA_ "GenSigStartValue" SG_ 1601 Main_Input_Supply 0;
BA_ "GenSigSendType" SG_ 1581 Charger_Connected 7;
BA_ "GenSigStartValue" SG_ 1581 Charger_Connected 0;
BA_ "GenSigSendType" SG_ 1581 Charger_Plugged_In 7;
BA_ "GenSigStartValue" SG_ 1581 Charger_Plugged_In 0;
VAL_ 1783 Move_OS_Pack_Status 4 "TRIAL_ENDING " 3 "EXPIRED" 2 "EXPIRING" 1 "ACTTIVATED" 0 "SNA" ;
VAL_ 1750 BBW_setting_from_TCU 3 "High" 2 "Medium" 1 "LOW" 0 "OFF" ;
VAL_ 1750 Regen_setting_from_som 3 "High" 2 "Default" 1 "Low" 0 "Off" ;
VAL_ 1089 Charger_Payment 5 "TRIG_PAYMENT_UNLOCKTO_CHARGE" 4 "Triggered_PAYMENT_CHARGE_BLOCKED" 3 "Triggered_PAYMENT_DUE_PAYMENT" 2 "Triggered_PAYMENT_CHARGE_STOPPED" 1 "Triggered_PAYMENT_CHARGING_START" 0 "SNA" ;
VAL_ 1081 Due_Status 3 "CONNECTION_FAILED" 2 "DUES_PAID" 1 "DUES_PENDING" 0 "SNA" ;
VAL_ 1081 Veh_Connectivity 1 "True" 0 "False" ;
VAL_ 1656 SOM_Vin_Mismatch 1 "True" 0 "False" ;
VAL_ 1745 ALL_WAKEUP 8 "Charger_Plug_In_Wakeup" 7 "SSB_WAKEUP" 6 "CHARGER_WAKEUP" 5 "VEH_CAN_WAKEUP" 4 "IMU_WAKEUP" 3 "SOM_WAKEUP" 2 "BLE_WAKEUP" 1 "RTC_WAKEUP" 0 "SNA" ;
VAL_ 1624 Custom_mode_Status 1 "Enable" 0 "Disable" ;
VAL_ 1743 Error_State 1 "Non_Error" 0 "Error" ;
VAL_ 1643 Music_operation 1 "Play" 0 "Pause" ;
VAL_ 1741 REGEN_info_sharing 2 "High" 1 "Default" 0 "Low" ;
VAL_ 1683 Control_Byte 18 "OTA Rollback failed" 17 "OTA Rollback Success" 16 "OTA Install Verification" 15 "OTA Install Not Eligible" 14 "OTA Install Eligible" 13 "INCOMING CALLS DISABLED" 12 "INCOMING CALLS ENABLED" 11 "HYPERCHARGING STOPPED" 10 "REGEN APPLY FAILED" 9 "DEFAULT  REGEN APPLIED" 8 "HIGH REGEN APPLIED" 7 "LOW REGEN APPLIED" 6 "VACATION MODE OFF" 5 "VACATION MODE ON" ;
VAL_ 1644 Contact_Details 3 "Display Number" 2 " Display Name" 1 "Display Duration" 0 "Incoming call" ;
VAL_ 1706 Passcode_Change_Request 9 "SOC_LESS_THAN_FIVE" 8 "HMI_SCOOTER_IN_DRIVE_STATE" 7 "HMI_SAME_PASSCODE" 6 "HMI_PASSCODE_USED_BY_OTHER_USER" 5 "HMI_UNKNOWN_FAILURE" 4 "HMI_SUCCESS" 3 "HMI_ACK" ;
VAL_ 1739 BLE_Connection_Status_S1X 2 "BLE_CAPP_NOT_CONNECTED" 1 "BLE_CAPP_CONNECTED" 0 "SNA" ;
VAL_ 1739 Cruise_Control_Availability_S1X 4 "SNA" 3 "NOT_AVAILABLE_SPD_OOR" 2 "NOT_AVAILABLE_MODE_UNSUITABLE

" 1 "ACTIV" 0 "AVAILABLE_NOT_ACTV" ;
VAL_ 1739 HMI_StatusBar_Stt_S1X 8 "INSTALL_DONE" 7 "Boost_Charger" 6 "FAST_CHARGER" 5 "SLOW_CHARGER" 4 "INITIALIZING_HYPERCHARGER" 3 "NOT_CHARGING" 2 "CHARGING_SLOWLY" 1 "CHARGE_COMPLETE" 0 "SNA" ;
VAL_ 1739 Driver_Mode_S1X 4 "REVERSE" 3 "HYPER" 2 "SPORTS" 1 "NORMAL" 0 "ECO" ;
VAL_ 1739 Display_Screen_S1X 12 "DISPLAY_OTA_ROLLBACK_FAIL" 11 "DISPLAY_OTA_ROLLBACK_SUCCESS" 10 "DISPLAY_OTA_VERIFYING" 9 "DISPLAY_OTA_INSTALL_FAIL" 8 "DISPLAY_OTA_INSTALL_SUCCESS" 7 "DISPLAY_OTA_INSTALLING" 6 "DISPLAY_PASSCODE_CUM_CHARGER_SCREEN" 5 "DISPLAY_BOOT_SCREEN" 4 "DISPLAY_PASSCODE_SCREEN" 3 "DISPLAY_DRIVE_SCREEN" 2 "DISPLAY_CHARGER_SCREEN" 1 "DISPLAY_PARK_SCREEN" 0 "DISPLAY_BLANK_SCREEN" ;
VAL_ 1738 Passcode_Authin_Progress 2 "Completed" 1 "In_Progress" 0 "SNA" ;
VAL_ 1711 Som_To_Bcm_PasscodeAck 2 "Incorrect_Passcode" 1 "Correct_Passcode" 0 "SNA" ;
VAL_ 1707 Navigation_Direction_Info_distance_units 2 "KMs " 1 "Meters " 0 "SNA" ;
VAL_ 1707 Navigation_Direction_Info_direction_type 12 "ARRIVING " 11 "ARRIVE " 10 "ENTER_ROUNDABOUT " 9 "DEPART " 8 "TURN_SLIGHT_LEFT " 7 "TURN_LEFT " 6 "TURN_SHARP_LEFT " 5 "U_TURN " 4 "TURN_SLIGHT_RIGHT" 3 "TURN_RIGHT" 2 "TURN_SHARP_RIGHT " 1 "CONTINUE" 0 "SNA" ;
VAL_ 1646 VIN_Variant_mismatch 1 "True" 0 "False" ;
VAL_ 1646 Variant_Dipswitch_Mismatch 1 "True" 0 "False" ;
VAL_ 1646 SOM_BMS_Mismatch 1 "True" 0 "False" ;
VAL_ 1646 MCU_SW_Variant_Mismatch 1 "True" 0 "False" ;
VAL_ 1646 Dipswitch_not_Sent_Error 1 "True" 0 "False" ;
VAL_ 1646 Cell_Chemistry_SW_Mismatch 1 "True" 0 "False" ;
VAL_ 1646 BMS_Board_Variant_SW_Mismatch 1 "True" 0 "False" ;
VAL_ 1646 BCM_SW_Version_Variant_Mismatch 1 "True" 0 "False" ;
VAL_ 1646 BCM_BMS_Mismatch 1 "True" 0 "False" ;
VAL_ 1218 ESCL_Power_Status 1 "ON" 0 "OFF" ;
VAL_ 1766 Cruise_Deactivation_Speed 1 "True" 0 "False" ;
VAL_ 1668 Deration_Motor 3 "MOTOR_ERR_PARK_SET" 2 "MOTOR_WARN_ECO_SET" 1 "MOTOR_WARN_NORMAL_SET" 0 "SNA" ;
VAL_ 1668 Deration_MCU 3 "MCU_ERR_PARK_SET" 2 "MCU_WARN_ECO_SET" 1 "MCU_WARN_NORMAL_SET" 0 "SNA" ;
VAL_ 1665 Regeneration_Command 1 "Enable" 0 "Disable" ;
VAL_ 1665 Hill_Hold_Command 1 "Activate" 0 "Not Activate" ;
VAL_ 1703 Ack_From_HMI_for_BLE 2 "Failure" 1 "Success" ;
VAL_ 1681 Data1_BCM_BLEto_SOM 8 "PROXIMITY_UNLOCKED_WAIT" 7 "CAPP_UNLOCKED" 6 "PROXIMITY_UNLOCKED" 5 "SSB_UNLOCKED" 4 "AUTO_LOCKED" 3 "PROXIMITY_LOCKED" 2 "CAPP_LOCKED" 1 "SSB_LOCKED" 0 "INVALID" ;
VAL_ 1681 Acknowledgement_BCM_BLEto_SOM 1 "NACK" 0 "ACK" ;
VAL_ 1681 CommandType_BCM_BLEto_SOM 238 "TMH_Lights_Disabled" 237 "TMH_Lights_Enable" 20 "CAPP_Deleted_Profile_ID_ACK" 19 "CAPP_Proximity_Feature_Status" 18 "BLE_Connected_Profile_ID" 236 "User_Calibration_Request" 235 "Delete_Profile" 234 "I_am_Safe" 233 "Need_Help" 232 "Vacation_Mode_OFF" 231 "Vacation_Mode_ON" 230 "Hill_Hold_Request_Disable_Selected" 229 "Hill_Hold_Request_Enable_Selected" 228 "Motor_Cutoff_Delay_Selected_Disable_Selected" 227 "Motor_Cutoff_Delay_Selected_Enable_Selected" 226 "Auto_Indicator_turn_off_feature_Disable_Selected" 225 "Auto_Indicator_turn_off_feature_Enable_Selected" 224 "Hazard_Light_Feature_Disable_Selected" 223 "Hazard_Light_Feature_Enable_Selected" 222 "Auto_Head_Light_Handling_Disable_Selected" 221 "Auto_Head_Light_Handling_Enable_Selected" 220 "Smart_DRL_Light_Disable_Selected" 219 "Smart_DRL_Light_Enable_Selected" 218 "Proximity_Lock_Unlock_Disable_Selected" 217 "Proximity_Lock_Unlock_Enable_Selected" 216 "Tow_Tamper_Detection_Disable_Selected" 215 "Tow_Tamper_Detection_Enable_Selected" 214 "Fall_Detection_Disable_Selected" 213 "Fall_Detection_Enable_Selected" 212 "Boost_Mode_Disable_Selected" 211 "Boost_Mode_Enable_Selected" 210 "Smart_Head_Light_SMART_LIGHT_EN_Selected" 209 "Smart_Head_Light_HIGH_BEAM_EN_Selected" 208 "Smart_Head_Light_LOW_BEAM_EN_Selected     " 207 "Smart_Head_Light_DEFAULT_Selected" 206 "Regen_Type_HIGHWAY_GLIDE_EN_Selected" 205 "Regen_Type_CITY_RIDE_EN_Selected" 204 "Regen_Type_Default_Selected" 203 "Hill_Hold_Request_Disable_Selected" 202 "Hill_Hold_Request_Enable_Selected" 17 "OlaMaps_ACK" 199 "Pairing Request Acknowledgement" 201 "Pairing status notify" 200 "Pairing Key notify" 16 "Party Mode" 14 "Un-Authorize Access Attemp No" 13 "Sucessful Bonded connection" 12 "Get current Phone 4 MAC" 11 "Get current Phone 3 MAC" 10 "Get current Phone 2 MAC" 9 "Get current Phone 1 MAC" 8 "Get current Phone 0 MAC" 7 "No of currently Paired Phone" 6 "Clear All Phone" 5 "Make device connectable for bonded device." 4 "Disconnect  current connected Phone" 3 "Make device Discoverable" 2 "Get proximity BLE MAC" 1 "Get proximity BLE SW version" ;
VAL_ 1680 Data0_SOM_BLEto_BCM 1 "Accept Accept" 0 "Reject Pairing" ;
VAL_ 1680 Acknowledgement_SOM_BLEto_BCM 1 "NACK" 0 "ACK" ;
VAL_ 1680 CommandType_SOM_BLEto_BCM 238 "TMH_Lights_Disabled" 237 "TMH_Lights_Enable" 22 "HMI_Logged_In_Profile_ID" 21 "Get_Current_Connected_Profile_ID" 20 "CAPP_Deleted_Profile_ID" 19 "Proximity_Feature_Status_ACK" 18 "BLE_Connected_Profile_ID_ACK" 236 "User_Calibration_Request" 235 "Delete_Profile" 234 "I_am_Safe" 233 "Need_Help" 232 "Vacation_Mode_OFF" 231 "Vacation_Mode_ON" 230 "Hill_Hold_Request_Disable_Selected" 229 "Hill_Hold _Request_Enable_Selected" 228 "Motor_Cutoff_Delay_Selected_Disable_Selected" 227 "Motor_Cutoff_Delay_Selected_Enable_Selected" 226 "Auto_Indicator_turn_off_feature_Disable_Selected" 225 "Auto_Indicator_turn_off_feature_Enable_Selected" 224 "Hazard_Light_Feature_Disable_Selected" 223 "Hazard_Light_Feature_Enable_Selected" 222 "Auto_Head_Light_Handling_Disable_Selected" 221 "Auto_Head_Light_Handling_Enable_Selected" 220 "Smart_DRL_Light_Disable_Selected" 219 "Smart_DRL_Light_Enable_Selected" 218 "Proximity_Lock_Unlock_Disable_Selected" 217 "Proximity_Lock_Unlock_Enable_Selected" 216 "Tow_Tamper_Detection_Disable_Selected" 215 "Tow_Tamper_Detection_Enable_Selected" 214 "Fall_Detection_Disable_Selected" 213 "Fall_Detection_Enable_Selected" 212 "Boost_Mode_Disable_Selected" 211 "Boost_Mode_Enable_Selected" 210 "Smart_Head_Light_SMART_LIGHT_EN_Selected" 209 "Smart_Head_Light_HIGH_BEAM_EN_Selected" 208 "Smart_Head_Light_LOW_BEAM_EN_Selected     " 207 "Smart_Head_Light_DEFAULT_Selected" 206 "Regen_Type_HIGHWAY_GLIDE_EN_Selected" 205 "Regen_Type_CITY_RIDE_EN_Selected" 204 "Regen_Type_Default_Selected" 203 "Hill_Hold_Request_Disable_Selected" 202 "Hill_Hold_Request_Enable_Selected" 17 "OlaMaps_ACK" 199 "Pairing Request Acknowledgement" 201 "Pairing status notify" 200 "Pairing Key notify" 16 "Party Mode" 14 "Un-Authorize Access Attemp No" 13 "Sucessful Bonded connection" 12 "Get current Phone 4 MAC" 11 "Get current Phone 3 MAC" 10 "Get current Phone 2 MAC" 9 "Get current Phone 1 MAC" 8 "Get current Phone 0 MAC" 7 "No of currently Paired Phone" 6 "Clear All Phone" 5 "Make device connectable for bonded device." 4 "Disconnect  current connected Phone" 3 "Make device Discoverable" 2 "Get proximity BLE MAC" 1 "Get proximity BLE SW version" ;
VAL_ 1667 Hill_Hold_Availability 6 "Hill_Hold_Availability" 5 "ABT_TO_ACTIVATE" 4 "ABT_TO_DEACTIVATE" 3 "NOT_ACTIVE" 2 "ACTIVE" 1 "UNAVAILABLE" 0 "SNA" ;
VAL_ 1667 Regen_Availability 3 "RESERVED" 2 "REGEN_NOT_AVAILABLE_THROTTLE" 1 "REGEN_AVAILABLE" 0 "REGEN_NOT_AVAILABLE" ;
VAL_ 1667 Tow_Theft_Detection 6 "TImeOut" 5 "Recovered" 4 "Ignored" 3 "Detected_L2_Phase" 2 "Detected_L1_Phase" 1 "Not_Detected" 0 "Reserved" ;
VAL_ 1667 Fall_Detection_State 6 "FALL_TIMEOUT" 5 "FALL_IGNORED" 4 "FALL_RECOVERED" 2 "FALL_ACTIVE_L1" 3 "FALL_ACTIVE_L2" 1 "INACTIVE" 0 "RESERVED" ;
VAL_ 1666 Accident_Severity_Sts 6 "Severe HIGH SPD" 5 "Severe LOW SPD" 4 "Major LOW SPD" 3 "Major ZERO SPD" 2 "Minor AWAKE" 1 "Minor PARK" 0 "No Accident " ;
VAL_ 1666 Adaptive_Power_Boost_Request 1 "Active" 0 "Not Active" ;
VAL_ 1666 Right_Turn_Completion_Status 1 "Active" 0 "Not Active" ;
VAL_ 1666 Left_Turn_Completion_Status 1 "Active" 0 "Not Active" ;
VAL_ 1666 Hard_Reboot_Disable 1 "Disable" 0 "Enable" ;
VAL_ 1666 Fall_Detected_L2_Phase 1 "True" 0 "False" ;
VAL_ 1666 Fall_Detected_L1_Phase 1 "True" 0 "False" ;
VAL_ 1666 Motor_Cutoff_Status_of_Left_Brake 1 "Active" 0 "Not Active" ;
VAL_ 1666 Fall_Set_Reset_TimerFlag 1 "True" 0 "False" ;
VAL_ 1666 Motor_Cutoff_Status_of_Right_Brake 1 "Active" 0 "Not Active" ;
VAL_ 1666 Hill_Hold_Request 1 "Activate" 0 "Not Activate" ;
VAL_ 1651 BMS_Bool_Debug4 1 "True" 0 "False" ;
VAL_ 1651 BMS_Bool_Debug3 1 "True" 0 "False" ;
VAL_ 1632 Vacation_Mode 1 "Enable" 0 "Disable" ;
VAL_ 1632 Motor_Cutoff_Delay 1 "Enable" 0 "Disable" ;
VAL_ 1632 Auto_Indicator_Turnoff_Feature 1 "Enable" 0 "Disable" ;
VAL_ 1632 Hazard_Light_Feature_Enable 1 "Enable" 0 "Disable" ;
VAL_ 1632 Auto_Head_Light_Handling 1 "Enable" 0 "Disable" ;
VAL_ 1632 Smart_DRL_Light 1 "Enable" 0 "Disable" ;
VAL_ 1632 Smart_Head_Light 1 "Enable" 0 "Disable" ;
VAL_ 1632 Hill_Hold_Feature_Enable 1 "Enable" 0 "Disable" ;
VAL_ 1632 Proximity_Lock_Unlock 1 "Enable" 0 "Disable" ;
VAL_ 1632 Tow_Tamper_Detection 1 "Enable" 0 "Disable" ;
VAL_ 1632 Fall_Detection 1 "Enable" 0 "Disable" ;
VAL_ 1632 Boost_Mode 1 "Enable" 0 "Disable" ;
VAL_ 1632 Take_me_home_lights_API 1 "Enable" 0 "Disable" ;
VAL_ 1632 Tow_protection_API 1 "Enable" 0 "Disable" ;
VAL_ 1632 HIL_HOLD_FEATURE_ENABLE 1 "Enable" 0 "Disable" ;
VAL_ 1632 Early_Beta_Access 1 "Enable" 0 "Disable" ;
VAL_ 1632 New_Upgrades 1 "Enable" 0 "Disable" ;
VAL_ 1632 Mode_Fencing 1 "Enable" 0 "Disable" ;
VAL_ 1632 Time_Fencing 1 "Enable" 0 "Disable" ;
VAL_ 1632 Geo_Fencing 1 "Enable" 0 "Disable" ;
VAL_ 1632 Proximity_L_U 1 "Enable" 0 "Disable" ;
VAL_ 1632 Energy_Insights 1 "Enable" 0 "Disable" ;
VAL_ 1632 TP_HyperCharge 1 "Enable" 0 "Disable" ;
VAL_ 1632 TP_FasterCharge 1 "Enable" 0 "Disable" ;
VAL_ 1632 Advanced_Regen 1 "Enable" 0 "Disable" ;
VAL_ 1591 VCU_Bootloader_Info 12 "BOOT_STATUS_FW_FAIL" 11 "BOOT_STATUS_ROLL_BACK" 10 "BOOT_STATUS_RELOAD_EFLASH" 9 "BOOT_STATUS_CRC_FAIL_VERIFY" 8 "BOOT_STATUS_COPY_FAIL_EF_TO_IF" 7 "BOOT_STATUS_EFS_INIT_FAIL" 6 "BOOT_STATUS_IFS_READ_FAIL" 5 "BOOT_STATUS_IFS_INIT_FAIL" 4 "BOOT_STATUS_WDOG_INIT_FAIL" 3 "BOOT_STAUS_LOAD_EXTERNAL_WDG" 2 "BOOT_STATUS_SUCCESS" 1 "BOOT_STATUS_LOAD" 0 "BOOT_STATUS_IDLE" ;
VAL_ 1590 BMS00_Bootloader_Info 12 "BOOT_STATUS_FW_FAIL" 11 "BOOT_STATUS_ROLL_BACK" 10 "BOOT_STATUS_RELOAD_EFLASH" 9 "BOOT_STATUS_CRC_FAIL_VERIFY" 8 "BOOT_STATUS_COPY_FAIL_EF_TO_IF" 7 "BOOT_STATUS_EFS_INIT_FAIL" 6 "BOOT_STATUS_IFS_READ_FAIL" 5 "BOOT_STATUS_IFS_INIT_FAIL" 4 "BOOT_STATUS_WDOG_INIT_FAIL" 3 "BOOT_STAUS_LOAD_EXTERNAL_WDG" 2 "BOOT_STATUS_SUCCESS" 1 "BOOT_STATUS_LOAD" 0 "BOOT_STATUS_IDLE" ;
VAL_ 1699 BLE_Status 67 "UART_INVALID_FOOTER_ERROR" 66 "UART_INVALID_HEADER_ERROR" 65 "UART_PARSING_ERROR" 64 "UART_PID_NOT_FOUND_ERROR" 40 "LE_NOTIFY_ENABLE" 39 "UNKNOWN_ERR_DISCONNECT" 38 "PROXIMITY_UNLOCK_SSB_CMD" 37 "REQSEEDCMD" 36 "SCOOTER_DISCONNECT_TIMEOUT" 35 "DEVICE_DISCONNECTED" 34 "SCOOTER_DISCOVERY_FAIL" 33 "CRC_FAIL" 32 "UNLOCK_CMD" 31 "LOCK_CMD" 30 "PROXIMITY_ENABLED_CMD" 29 "PROXIMTY_DISABLED_CMD" 28 "SCOOTER_NOT_BONDED" 27 "UNKNOWN_ERR" 26 "CAPP_KILLED" 25 "PAIR_ENC_FAILED" 24 "PAIR_FAILED_ENC_LTK_MISSING" 23 "PAIR_FAILED_KEY_REJECT" 22 "PAIR_FAILED_KEY_NOT_ACCEPT_TIMEOUT" 21 "PHONE_OUTOF_RANGE" 20 "SCOOTER_DISCONNECT" 19 "REMOTE_USER_DISCONNECT" 18 "PAIR_KEY_ACK_TIMOUT" 17 "AES_ENC_FAILED" 16 "SEED_KEY_MISMATCHED" 15 "SEED_KEY_FAILED" 14 "SCOOTER_BONDED" 13 "PHONE_RECONNECTION_FAILURE" 12 "PHONE_RECONNECTION_SUCCESS" 11 "PHONE_AUTH_FAIL" 10 "PROXIMITY_UNLOCK_CMD" 9 "PROXIMITY_LOCK_CMD" 8 "PHONE_CONNECTION_SUCCESS_PAIR_INIT" 7 "PHONE_PAIRING_FAIL" 6 "PHONE_PAIRING_SUCCESS" 5 "PAIRING_REQ_ACK_HMI_FAIL" 4 "PAIRING_REQ_ACK_HMI_SUCCESS" 3 "PAIRING_KEY_SENT_20_TIMES" 2 "PHONE_PAIR_INITIATED" 1 "BLE_ADVERTISE_SUCCESS" ;
VAL_ 853 Regen_Availability_Flag 1 "ON" 0 "OFF" ;
VAL_ 1610 Scooter_Toppled 1 "Active" 0 "Inactive" ;
VAL_ 1610 Trunk_Unlock_Failure 1 "Error" 0 "No_Error" ;
VAL_ 1610 Veh_Low_Power_Management_State 4 "VEH_LOW_PWR_MGMT_STATE_VACATION" 3 "Deep_Sleep" 2 "Sleep" 1 "Normal" 0 "SNA" ;
VAL_ 100 HBC_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 100 BLE_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 100 BCM_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 100 ESCL_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 100 MCU_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 100 HU_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 100 BMS_Node_Absent_Status 1 "Fault" 0 "No Fault" ;
VAL_ 324 Vehicle_state_BCM_ULock 4 "Vehicle Lock Fail" 3 "Vehicle Locked" 2 "Vehicle Unlock Fail" 1 "Vehicle Unlocked" 0 "SNA" ;
VAL_ 548 Vehicle_Dir_flag 1 "Forward" 0 "Reverse" ;
VAL_ 820 PWMMissingFault 1 "Fault" 0 "No Fault" ;
VAL_ 820 ZIPulse_Missing_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 820 AB_Pulse_Missing_Num 0 "Default" ;
VAL_ 820 AB_Pulse_Mism_ZIPulse_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 820 Pos_Cnt_Mism_PWM_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 401 Aggregated_current_Ah 0 "Default" ;
VAL_ 401 Calculated_current_based_on_coulomb 0 "Default" ;
VAL_ 1682 Speaker_Lock_Failure_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Swipe_Lock_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Lock_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Alarm_Sts 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Find_My_Bike_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Unlock_Failure_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Unlock_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Seat_Unlock_Sound 1 "ON" 0 "OFF" ;
VAL_ 1682 Speaker_Reverse_Parking_Sound 1 "ON" 0 "OFF" ;
VAL_ 1697 Command_From_APP 12 "Find my Bike Light Turn OFF Request" 11 "Find my Bike Light Turn ON Request" 10 "Diagnostic Request" 9 "Reserved" 8 "Lock Without Steering" 7 "Vehicle Remote Immobilize OFF" 6 "Vehicle Remote Immobilize ON" 5 "Trunk Open" 4 "Vehicle Headlamp OFF" 3 "Vehicle Headlamp ON" 2 "Vehicle LOCK" 1 "Vehicle UNLOCK" 0 "No Command" ;
VAL_ 1698 Command_From_BLE 5 "Find my Bike Horn Enable" 4 "Find my Bike Light Enable" 3 "Trunk Open" 2 "Unlock" 1 "Lock" 0 "None" ;
VAL_ 1740 Navigation_Status 1 "ON" 0 "OFF" ;
VAL_ 1740 SOM_State 6 "Keyon" 5 "Keyoff" 4 "Fastboot" 3 "Standby" 2 "Sleep" 1 "Booting" 0 "OFF" ;
VAL_ 1740 Passcode_Auth_Status 1 "Success" 0 "Failed" ;
VAL_ 1724 Wifi_Connection_Status 3 "WIFI_CONNECTION_FAILURE" 2 "WIFI_CONNECTING" 1 "WIFI_CONNECTION_SUCCESSFUL" 0 "SNA" ;
VAL_ 1724 OTA_Installation_Message 8 "Rollback fail" 7 "Rollback success" 6 "Install Verification" 5 "Install fail" 4 "Install Success" 3 "Installing" 2 "Install not eligible" 1 "Install Eligible" 0 "SNA" ;
VAL_ 1724 Command_From_TCU 102 "BD_Drv_Prk_Ack" 101 "SOS_Disable" 100 "SOS_Enable" 99 "Restrict_Drive" 98 "HMI_Wakeup_pass" 97 "OTA_MCU_Reset" 89 "Drive_acess_revoked_disabled" 88 "Drive_acess_revoked_enable" 87 "Alarm_Unlock_Now" 86 "TMH_Lights_Disabled" 85 "TMH_Lights_Enable" 84 "Lock_Now" 83 "Ride_Now" 82 "Smart_Head_Light_SMART_LIGHT_EN_Deselected" 81 "Version_Mismatched" 80 "Exit_Diagnostic_Mode" 79 "HMI_Alarm_ignore_False" 78 "Tow_Theft_High_Sensitivity" 77 "Tow_Theft_Low_Sensitivity" 76 "HMI_Alarm_ignore_True" 75 "Hazard_Light_Turn_OFF" 74 "Hazard_Light_Turn_ON" 73 "User_Calibration_Request" 72 "Delete_Profile" 71 "I_am_Safe" 70 "Need_Help" 69 "Vacation_Mode_OFF" 68 "Vacation_Mode_ON" 67 "Hill_Hold_Request_Disable_Selected" 66 "Hill_Hold_Request_Enable_Selected" 65 "Motor_Cutoff_Delay_Selected_Disable_Selected" 64 "Motor_Cutoff_Delay_Selected_Enable_Selected" 63 "Auto_Indicator_turn_off_feature_Disable_Selected" 62 "Auto_Indicator_turn_off_feature_Enable_Selected" 61 "Hazard_Light_Feature_Disable_Selected" 60 "Hazard_Light_Feature_Enable_Selected" 59 "Auto_Head_Light_Handling_Disable_Selected" 58 "Auto_Head_Light_Handling_Enable_Selected" 57 "Smart_DRL_Light_Disable_Selected" 56 "Smart_DRL_Light_Enable_Selected" 55 "Proximity_Lock_Unlock_Disable_Selected" 54 "Proximity_Lock_Unlock_Enable_Selected" 53 "Tow_Tamper_Detection_Disable_Selected" 52 "Tow_Tamper_Detection_Enable_Selected" 51 "Accident_Detection_Disable_Selected" 50 "Accident_Detection_Enable_Selected" 49 "Boost_Mode_Disable_Selected" 48 "Boost_Mode_Enable_Selected" 47 "Smart_Head_Light_SMART_LIGHT_EN_Selected" 46 "Smart_Head_Light_HIGH_BEAM_EN_Selected" 45 "Smart_Head_Light_LOW_BEAM_EN_Selected     " 44 "Smart_Head_Light_DEFAULT_Selected" 43 "Regen_Type_HIGHWAY_GLIDE_EN_Selected" 42 "Regen_Type_CITY_RIDE_EN_Selected" 41 "Regen_Type_Default_Selected" 40 "Auto_Head_Light_RESERVED" 39 "Auto_Head_Light_SUN_SET_EN" 38 "Auto_Head_Light_SUN_RISE_EN" 37 "Front_DRL_OFF" 36 "Front_DRL_ON" 35 "Right_Indicator_OFF" 34 "Right_Indicator_ON" 33 "Left_Indicator_OFF" 32 "Left_Indicator_ON" 31 "Rear_Light_OFF" 30 "Rear_Light_ON" 29 "Low_Beam_OFF" 28 "Low_Beam_ON" 27 "High_Beam_OFF" 26 "High_Beam_ON" 25 "PASSCODE_AUTH" 24 "CTA Ignore" 23 "CTA Unlock" 22 "CTA Lock" 21 "Trip Reset Succesfull " 20 "Passcode ACK" 19 "HU Reboot" 18 "MCU Reboot" 17 "OTA NONE" 16 "OTA Installation in Progress" 15 "OTA Installation Started" 14 "Scooter Reboot" 13 "Stop Charge" 12 "Rejected Get Home Request" 11 "Accepted Get Home Request" 10 "Headlamp Turn Off reuest" 9 "ALARM Turn Off Request" 8 "Vehicle Shutdown is rejected by User" 7 "ECO Mode accepted by User" 6 "ECO Mode rejected by User" 5 "Headlight turn ON request" 4 "Reverse Mode Activated" 3 "Seat Unlock Cmd" 2 "Lock without Steering" 1 "Lock with Steering" 0 "No Command" ;
VAL_ 1650 OTA_Status_Information 3 "SNA" 2 "Completed" 1 "In Progress" 0 "Start (Diag Tool Connected)" ;
VAL_ 1649 Charger_Retrigger 1 "True" 0 "False" ;
VAL_ 1649 Charger_Sleep_Request 1 "True" 0 "False" ;
VAL_ 1648 ASW_Log_Level_Set 7 "Reserved" 6 "LOG_LEVEL_DUMP" 5 "LOG_LEVEL_INFO" 4 "LOG_LEVEL_FUNCTION_FLOW" 3 "LOG_LEVEL_WARNING" 2 "LOG_LEVEL_ERROR" 1 "LOG_LEVL_FATAL_ERROR" 0 "LOG_LEVEL_DEFAULT" ;
VAL_ 1648 BMS_Over_temp_drive_error_BSW_signal 1 "Error" 0 "No Error" ;
VAL_ 1648 BMS_Over_temp_drive_warning_BSW_signal 1 "Error" 0 "No Error" ;
VAL_ 1648 BMS_under_voltage_warning_BSW_signal 1 "Error" 0 "No Error" ;
VAL_ 1648 AFE_Fault_Status_BSW_signal 1 "Error" 0 "No Error" ;
VAL_ 1648 PDU_Warning_BSW_Signal 1 "Error" 0 "No Error" ;
VAL_ 1648 PDU_Error_BSW_Signal 1 "Error" 0 "No Error" ;
VAL_ 1648 VCU_Limp_Home_Fault 1 "Active" 0 "Not Active" ;
VAL_ 1648 VCU_Drive_Stop_Fault 1 "Active" 0 "Not Active" ;
VAL_ 1605 CAPP_Activity_Status 1 "Active" 0 "Not Active" ;
VAL_ 1605 TMHL_Status 1 "Enabled" 0 "Disabled" ;
VAL_ 1605 App_Auto_Indicator_Feature 1 "Enabled" 0 "Disabled" ;
VAL_ 1605 Proximity_Unlock_via_Passcode_Pin_Status 1 "Enabled" 0 "Disabled" ;
VAL_ 1605 Auto_Lock_Feature_Status 1 "Enabled" 0 "Disabled" ;
VAL_ 1605 Tow_Tamper_Detection_Status 1 "Enabled" 0 "Disabled" ;
VAL_ 1603 Touch_State 1 "Activated" 0 "Not Activated" ;
VAL_ 1744 OTA_Install_Start 1 "True" 0 "False" ;
VAL_ 1744 Display_Icon 18 "Arriving" 17 "Arrive" 16 "Enter_RoundAbout" 15 "Depart" 14 "Turn_Slight_Left" 13 "Turn_Left" 12 "Turn_Sharp_Left" 11 "U_Turn" 10 "Turn_Slight_Right" 9 "Turn_Right" 8 "Turn_Sharp_Right" 7 "Continue" 6 "Music" 5 "Calling" 4 "Reverse" 3 "Charging" 2 "Unlock" 1 "Lock" 0 "SNA" ;
VAL_ 1744 Hazard_Icon 2 "ON" 1 "OFF" 0 "SNA" ;
VAL_ 1744 Command_From_Vehicle 226 "SOC_DERATION_UNLOCK" 225 "SOC_DERATION_DRIVE_TO_PARK" 224 "PARTS_MISMATCHED_EXHAUSTED" 223 "PARTS_MISMATCHED" 222 " IN_SERVICE" 221 " NOT_DELIVERED_EXHAUSTED_RANGE" 220 " SCOOTER_NOT_DELIVERED" 219 "IN_SERVICE_RANGE_EXHAUSTED" 218 "REGEN_OFF " 217 "MOVEOS_PACK_TRIAL_ENDING" 216 "MOVEOS_PACK_EXPIRED" 215 "MOVEOS_PACK_EXPIRING" 214 "MOVEOS_PACK_ACTIVATED" 213 "CAN_DISCONNECT_ECO_START" 212 "TIME_FENCE_BREACHED" 211 "GEO_FENCE_BREACHED" 210 "UPGRADE_FOR_HYPERCHARGER" 209 "UPGRADE_FOR_BOOSTCHARGER" 208 "FC_AMOUNT_RUPEES" 207 "USE_REGEN_NUDGE" 206 "USE_ECO_NUDGE" 205 "HC_PAYMENT_RESET" 204 "BATT_WARN_ECO_CRUISE_FAILURE" 203 "BATT_WARN_ECO" 202 "BATT_WARN_ECO_START" 201 "HC_PAYMENT_UNLOCK_TO_CHARGE" 200 "RELEASE_THROTTLE_TO_DRIVE" 183 "CAN_COM_ERR_PRK" 199 "HC_PAYMENT_CHARGE_BLOCKED" 198 "HC_PAYMENT_DUE_PAYMENT" 197 "HC_PAYMENT_CHARGE_STOPPED" 196 "HC_PAYMENT_CHARGING_START" 195 "PDU_WARN_ECO_START" 194 "PDU_WARN_ECO_CRUISE_FAILURE" 193 "PDU_WARN_ECO" 192 "THROTTLE_OFFSET_DRV_ERROR" 185 "CAN_COM_ERROR_NORMAL" 181 "CAN_COM_FAILURE_GRADE3_PRK" 180 "CAN_COM_FAILURE" 179 "CAN_FAILURE_CANT_ENABLE_CRUISE" 182 "CHARGING_PAYMENT_DUE" 178 "SideStandDownWarn" 191 "DCDC_LOW_THRESH_REACHED " 190 "GEOFEN_ACS_REVOKED" 189 "PRNTCNTRL_SPORT_ACS_REVOKED" 188 "PRNTCNTRL_HYPER_ACS_REVOKED" 187 "PRNTCNTRL_ACS_RESET" 186 "CANNOT_ENABLE_CRUISE" 175 "SOM_POWER_FAULT" 174 "SOM_STUCK_FAULT" 173 "WIFI_CONNECTION_FAILURE" 172 "WIFI_CONNECTING " 171 "WIFI_CONNECTION_SUCCESSFUL " 170 "ESCL_UNLOCK_FAIL_RETRY" 169 "OPEN_SIDE_STAND_TO_REBOOT" 168 "ENABLE_RIDE_TO_CHANGE_MODE" 167 "ENABLE_RIDE_TO_REVERSE" 166 "HALT_TO_TURN_OFF_SC" 165 "CRUISE_DEACT_BATT_CURR_SPIKE" 164 "MUSIC_EXIT" 163 "CALL_EXIT" 162 "NAVIGATION_EXIT" 161 "INCOMING CALLS DISABLED" 17 "Description for the value '0x11'" 160 "INCOMING CALLS ENABLED" 159 "HYPERCHARGING_STOPPED" 158 "REGEN_APPLY_FAILED" 157 "DEFAULT_REGEN_APPLIED" 156 "HIGH_REGEN_APPLIED" 155 "LOW_REGEN_APPLIED" 154 "VACATION_MODE_OFF" 153 "VACATION_MODE_ON" 152 " MUSIC_AND_NAVIGATION" 151 "CALL_AND_NAVIGATION" 150 "OTA_ENABLE" 149 "Navigation" 148 "Music" 147 "Call" 146 "Pairing" 144 "DISABLED_SPORTS_MODE_TEMP" 143 "DISABLED_SPORTS_MODE_SOC" 142 "DISABLED_SPORTS_MODE_FAULT" 141 "Thermal_Runaway" 140 "VERSION_MISMATCH" 139 "CONFIG_MISMATCH" 138 "ACCS_RVKED_CONTACT_OWNER" 137 "Please_Release_Emergency_Button_To_Start_Charging" 136 "EXITING_VACATION_MODE" 135 "ENTERING_VACATION_MODE" 134 "HILL_HOLD_USR_DEACT_REQ" 133 "UNPLUG_HYPER_CHGR_FAULT" 132 "INITIALIZING_HYPERCHARGER" 131 "SAFE_UNPLUG_HYPER_CHARGER" 130 "UNPLUG_CHGR_TO_RIDE" 25 "ENTERING_INTO_DEEP_SLEEP" 24 "TRUNK_CLOSE_NOTIFCATION" 23 "TRUNK_OPEN_NOTIFICATION" 129 "HILL_HOLD_EXIT_NUDGE" 128 "DCHRG_INHBT_ERROR" 127 "DERATE_DRV_FLT" 126 "DERATE_THROT_ERROR" 125 "RIDE_ACCS_RVKED_AFTER_PARK" 124 "CHGR_LATCH_NOT_CONED" 123 "CHGR_PWR_OFF" 122 "CHGR_TEMPORARY_FAULT" 121 "CHGR_PERMANENT_FAULT" 120 "LOCK_FAIL_CTA" 119 "UNLOCK_FAIL_CTA" 118 "USER_CALIBRATION_FAIL" 117 "USER_CALIBRATION_SUCCESS" 116 "VEHICLE_FALL_ALARM_IGNORED" 115 "VEHICLE_FALL_RECOVERED" 114 "VEHICLE_FALL_DETECTED" 113 "NORMAL_MODE_AVAILABLE" 112 "STOP_CHRG_OVER_TEMP_ERR" 111 "CHRG_CMPLT_UNPLG_CHRGR" 110 "LOCK_IN_PRGS" 109 "OVR_CHRG_PROTECTION" 108 "CNTCR_TOGGLING_ISSUE" 107 "BMS_GRADE4_CUTOFF" 106 "CAN_FAIL_WAIT_CONTACT_CS" 105 "DISABLED_TEMP_MODE_RISE_SWITCH" 104 "DISABLED_SPIKE_MODE_SWITCH" 103 "DISABLED_CELLDIP_MODE_SWITCH" 102 "CHRG_TEMP_RISE_GRADE2" 101 "DRV_TEMP_RISE_GRADE2" 100 "RERATE_HIGHER_MODE_SLP_OT" 99 "SLP_TEMP_RISE_GRADE2" 98 "RERATING_REVIVAL_CAN" 97 "RERATE_HIGHER_MODE_SPIK_ERR" 96 "RERATE_HIGHER_MODE_CELL_DIP" 95 "RERATE_HIGHER_MODE_CHRG_TEMP" 94 "RERATE_HIGHER_MODE_DRV_TEMP" 93 "CAN_FAIL" 92 "SPIKE_ERR_GRADE2" 91 "CELL_DIP_ERR_GRADE2" 90 "Initiate Navigation Ping Test" 89 "Initiate Tripometer Reset" 88 "MCU_GRADE2_DISABLED_HIGHER" 87 "PDU_WARN_CHRG" 86 "DERATE_LOW_TEMP_ERR_START" 85 "DERATE_LOW_TEMP_WARN_START" 84 "CHARGE_BATT_LOWTEMP_WARN" 83 "BATT_TEMP_SNSR_FAIL_ALL_CHRG" 82 "BATT_TEMP_SNSR_FAIL_CHRG" 81 "VLT_SNSR_FAIL_ALL_CHRG" 80 "VLT_SNSR_FAIL_CHRG" 79 "BATT_TEMP_OOR_ALL_CHRG" 78 "BATT_TEMP_OOR_CHRG" 77 "BATT_SNSR_FAIL_CONTACT_CS" 76 "VIN_MISMATCH" 75 "MCU_GRADE3_RESTART" 74 "MCU_GRADE3_PARK" 73 "MCU_GRADE2_CONTACT_CS" 72 "MCU_GRADE2_RESTART" 71 "MCU_GRADE2_ECO" 70 "OVER_CURR_CHRG_WARN" 69 "BATT_TEMP_SNSR_FAIL_ALL" 68 "BATT_TEMP_SNSR_FAIL" 67 "VLT_SNSR_FAIL_ALL" 66 "VLT_SNSR_FAIL" 65 "CURR_SNSR_FAIL_OC_SC" 64 "BATT_TEMP_OOR_ALL" 63 "BATT_TEMP_OOR" 62 "HOLD_BRK_SSB_TO_DRV" 61 "APPLY_SIDE_STAND_TO_LOCK" 60 "SIDE_STAND_OPEN" 59 "CLOSE_SIDE_STAND_TO_DRIVE" 58 "DERATE_CHRG_OVER_TEMP_WARN" 57 "RERATING_REVIVAL_MCU" 56 "RERATING_REVIVAL_MOT" 55 "DERATE_MCU_CHILL_WARN" 54 "DERATE_MOTE_CHILL_WARN" 53 "RERATING_HIGHER_MODE_MCU" 52 "RERATING_HIGHER_MODE_MOT" 51 "RERATING_HIGHER_MODE_OT" 50 "DERATE_MCU_TEMP_WARN_START" 49 "DERATE_MOT_TEMP_WARN_START" 48 "DISABLED_MCU_MODE_SWITCH" 47 "DISABLED_MOT_MODE_SWITCH" 46 "DERATE_MCU_COOLDOWN_WAIT" 45 "DERATE_MOT_COOLDOWN_WAIT" 44 "DERATE_MCU_TEMP_ERROR_START" 43 "DERATE_MOT_TEMP_ERROR_START" 42 "CHARGE_NOT_HAPPENING" 41 "RERATING_REVIVAL_OT" 40 "DISABLED_OT_MODE_SWITCH" 39 "DERATE_TEMP_COOLDOWN_WAIT" 38 "DERATE_OVER_TEMP_ERR_INPGRS" 37 "DERATE_OVER_TEMP_ERR_START" 36 "DERATE_OVER_TEMP_WARN_INPGRS" 35 "DERATE_OVER_TEMP_WARN_START" 34 "GENERAL_LOW_BATTERY" 33 "DERATE_SOC_20" 32 "DERATE_SOC_20_WARNING" 31 "DERATE_SOC_15" 16 "DERATE_SOC_15_WARNING" 15 "RERATING_HIGHER_MODE" 14 "DERATE_PDU_TEMP_WARN_INPGRS" 13 "DERATE_PDU_TEMP_WARN_START" 12 "DERATE_SOC_3" 11 "DERATE_SOC_3_WARNING" 10 "DERATE_PDU_TEMP_ERROR_INPGRS" 9 "DERATE_PDU_TEMP_ERROR_START" 8 "RERATING_REVIVAL_PDU" 7 "DERATE_AFE_FAULT" 6 "DISABLED_HIGHER_MODE" 5 "DISABLED_PDU_MODE_SWITCH" 4 "DERATE_CHARGE_TO_RIDE" 3 "DERATE_PDU_COOLDOWN_WAIT" 2 "DERATE_AFE_FAULT_CCS" 1 "Activate Passcode Window" 0 "No Command" ;
VAL_ 534 Reserved_Config_3 1 "Present" 0 "Not Present" ;
VAL_ 534 TPMS 1 "Present" 0 "Not Present" ;
VAL_ 534 Rain_Mode 1 "Present" 0 "Not Present" ;
VAL_ 534 Child_Profile 1 "Present" 0 "Not Present" ;
VAL_ 534 Tow_Protection 1 "Present" 0 "Not Present" ;
VAL_ 534 Revive_Mode 1 "Present" 0 "Not Present" ;
VAL_ 533 Eco_Mode 1 "Present" 0 "Not Present" ;
VAL_ 533 Auto_Indicator_Off 1 "Present" 0 "Not Present" ;
VAL_ 533 Sound_Chimes 1 "Present" 0 "Not Present" ;
VAL_ 533 Voice_Assistance 1 "Present" 0 "Not Present" ;
VAL_ 533 Predictive_Maintenance 1 "Present" 0 "Not Present" ;
VAL_ 533 Roadside_Assistance_on_CApp 1 "Present" 0 "Not Present" ;
VAL_ 533 Track_Your_Vehicle 1 "Present" 0 "Not Present" ;
VAL_ 533 Service_Book_Appointment_on_CApp 1 "Present" 0 "Not Present" ;
VAL_ 532 HMI_Mode_Vintage 1 "Present" 0 "Not Present" ;
VAL_ 532 Sounds_Mode_Power 1 "Present" 0 "Not Present" ;
VAL_ 532 Sounds_Mode_Hyper 1 "Present" 0 "Not Present" ;
VAL_ 532 Sounds_Mode_SciFi 1 "Present" 0 "Not Present" ;
VAL_ 532 Sounds_Mode_Normal 1 "Present" 0 "Not Present" ;
VAL_ 532 HMI_Mode_Wonder 1 "Present" 0 "Not Present" ;
VAL_ 532 HMI_Mode_Rush 1 "Present" 0 "Not Present" ;
VAL_ 532 HMI_Mode_Care 1 "Present" 0 "Not Present" ;
VAL_ 532 HMI_Mode_Boot 1 "Present" 0 "Not Present" ;
VAL_ 532 HMI_Mode_Default 1 "Present" 0 "Not Present" ;
VAL_ 532 Screen_Brightness_Adjuster_on_HMI 1 "Present" 0 "Not Present" ;
VAL_ 532 Manual_SOS 1 "Present" 0 "Not Present" ;
VAL_ 532 OTA_Updates_Forced_Updates 1 "Present" 0 "Not Present" ;
VAL_ 532 Welcome_Screen 1 "Present" 0 "Not Present" ;
VAL_ 532 ESCL_Lock 1 "Present" 0 "Not Present" ;
VAL_ 532 Find_My_Scooter 1 "Present" 0 "Not Present" ;
VAL_ 530 Take_Me_Home_Lights 1 "Present" 0 "Not Present" ;
VAL_ 530 Cruise_Control 1 "Present" 0 "Not Present" ;
VAL_ 530 Get_Home_Mode 1 "Present" 0 "Not Present" ;
VAL_ 530 Hill_Hold 1 "Present" 0 "Not Present" ;
VAL_ 530 Reverse_Mode 1 "Present" 0 "Not Present" ;
VAL_ 530 Limp_Home_Mode 1 "Present" 0 "Not Present" ;
VAL_ 530 Onboard_Navigation_2D 1 "Present" 0 "Not Present" ;
VAL_ 530 Connectivity 7 "WiFi Bluetooth Proximity" 6 "Bluetooth and Proximity" 5 "WiFi and GPS" 4 "GPS" 3 "WiFi and Bluetooth" 2 "Bluetooth" 1 "WiFi" 0 "Not Present" ;
VAL_ 529 Geofencing 1 "Present" 0 "Not Present" ;
VAL_ 529 Anti_Theft 1 "Present" 0 "Not Present" ;
VAL_ 529 Side_Stand_Alert 1 "Present" 0 "Not Present" ;
VAL_ 529 Infotainment_Control 1 "Present" 0 "Not Present" ;
VAL_ 529 Bluetooth_Functions 1 "Present" 0 "Not Present" ;
VAL_ 529 Boot_Unlock 7 "App HMI Voice" 6 "HMI and Voice" 5 "App and Voice" 4 "Voice Assist" 3 "App and HMI" 2 "HMI" 1 "App" 0 "Not Present" ;
VAL_ 529 Vehicle_Lock_and_Unlock 7 "App HMI Proximity" 6 "HMI and Proximity" 5 "App and Proximity" 4 "Proximity" 3 "App and HMI" 2 "HMI" 1 "App" 0 "Not Present" ;
VAL_ 529 Vehicle_Variant 26 "S1_Pro+_Gen3_4KWh" 25 "S1_Pro_Gen3_4KWh" 24 "S1_Pro_Gen3_3KWh" 23 "S1X+_Gen3_4KWh" 22 "S1X_Gen3_4KWh" 21 "S1X_Gen3_3KWh" 20 "S1X_Gen3_2KWh" 19 "M3X_4.5KWh" 18 "M3X_3.5KWh" 17 "M3X_2.5KWh" 16 "S1X_4KWh    " 15 "S1X 3KWh      " 14 "S1X 2KWh" 13 "S1X+ Phase2 4KwH" 12 "S1X+ Phase1 4KwH " 11 "Reserved" 10 "S1X+ Phase2" 9 "S1 Pro Gen2 W/O Upgrade" 8 "S1 Air Fleet" 7 "S1X+" 6 "S1 Pro Gen2 With Upgrade" 5 "S1 Air Customer" 4 "S1_2KW" 3 "S1Pro (with upgrade)" 2 " S1Pro (without upgrade)" 1 "S1" 0 "Reserved" ;
VAL_ 1600 Short_Circuit_Permanent_Precharge_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1600 Reserved_ 1 "Fault" 0 "No Fault" ;
VAL_ 1600 Discharge_Switch_Demand_00 1 "Enable" 0 "Disable" ;
VAL_ 1600 Charge_Switch_Demand_00 1 "Enable" 0 "Disable" ;
VAL_ 1600 Precharge_Switch_Demand_00 1 "Enable" 0 "Disable" ;
VAL_ 1600 BMS00_MOSFETs_disconnection_failed_10ms 1 " Fault" 0 "No fault" ;
VAL_ 1600 BMS00_MOSFETs_connection_failed_10ms 1 " Fault" 0 "No fault" ;
VAL_ 1600 BMS00_Precharge_failure_10ms 1 " Fault" 0 "No fault" ;
VAL_ 1600 BMS00_Precharge_too_slow_Info_10ms 1 " Fault" 0 "No fault" ;
VAL_ 1600 BMS00_Precharge_too_fast_Info_10ms 1 " Fault" 0 "No fault" ;
VAL_ 1600 BMS00_Short_circuit_detection_error 1 " Fault" 0 "No fault" ;
VAL_ 1727 MCU_12V_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Spare2_Current_source_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Status_LED_2 1 "ON" 0 "OFF" ;
VAL_ 1727 Status_LED_1 1 "ON" 0 "OFF" ;
VAL_ 1727 BLE_Output 1 "ON" 0 "OFF" ;
VAL_ 1727 BLE_Module_Reset 1 "ON" 0 "OFF" ;
VAL_ 1727 Sig_3V3_Enable 1 "ON" 0 "OFF" ;
VAL_ 1727 Sig_3_8V_output_DC_DC_enable 1 "ON" 0 "OFF" ;
VAL_ 1727 LIN_Driver_Enable 1 "ON" 0 "OFF" ;
VAL_ 1727 Level_Shifter_Enable 1 "ON" 0 "OFF" ;
VAL_ 1727 SOM_Power_Boot 1 "ON" 0 "OFF" ;
VAL_ 1727 SOM_Power_Key 1 "ON" 0 "OFF" ;
VAL_ 1727 SOM_Volume_Up 1 "ON" 0 "OFF" ;
VAL_ 1727 SOM_Volume_down 1 "ON" 0 "OFF" ;
VAL_ 1727 Drivers_Fault_Reset 1 "ON" 0 "OFF" ;
VAL_ 1727 Driver_Diagnostic_Select_2 1 "ON" 0 "OFF" ;
VAL_ 1727 Driver_Diagnostic_Select_1 1 "ON" 0 "OFF" ;
VAL_ 1727 Driver_Diagnostic_Enable 1 "ON" 0 "OFF" ;
VAL_ 1727 Control_Pilot_Wire_CPW 1 "ON" 0 "OFF" ;
VAL_ 1727 SPARE2_Supply_12V_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Horn_12V_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 FC_contactor_Negative_supply_command 1 "ON" 0 "OFF" ;
VAL_ 1727 FC_contactor_Positive_supply_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Spare_1_12V_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Boot_Light_12V_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Rear_Light_POS_12V_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Rear_Brake_Light_POS_12V_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Left_Indicators_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Right_Indicators_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Front_Light_High_Beam_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Front_Light_Low_Beam_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Front_DRL_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Charger_USB_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Head_Unit_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Seat_Lock_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Alarm_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Steering_Lock_12V_Supply_Control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Speaker_Supply_Enable 1 "ON" 0 "OFF" ;
VAL_ 1727 HBC_12V_supply_Control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Spare1_Current_source_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Left_Brake_pad_wear_supply_control_Command 1 "ON" 0 "OFF" ;
VAL_ 1727 Right_Brake_pad_wear_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 SSB_switch_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Seat_Occupant_switch_supply_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Side_Stand_switch_supply_control_command 1 "ON" 0 "OFF" ;
VAL_ 1727 BRAKE_PAD_Suppy_Enable 1 "ON" 0 "OFF" ;
VAL_ 1727 HU_CAN_TRX_Standby_command 1 "ON" 0 "OFF" ;
VAL_ 1727 FC_CAN_TRX_Standby_command 1 "ON" 0 "OFF" ;
VAL_ 1727 Vehicle_CAN_TRX_Standby_command 1 "ON" 0 "OFF" ;
VAL_ 1727 MCU_digital_Output_2_to_MCU 1 "ON" 0 "OFF" ;
VAL_ 1727 MCU_digital_Output_1_to_MCU 1 "ON" 0 "OFF" ;
VAL_ 1726 RTC_Clock_Input 1 "ON" 0 "OFF" ;
VAL_ 1726 RTC_INTERRUPT1 1 "ON" 0 "OFF" ;
VAL_ 1726 IMU_INTERRUPT_2 1 "ON" 0 "OFF" ;
VAL_ 1726 IMU_INTERRUPT_1 1 "ON" 0 "OFF" ;
VAL_ 1726 CHARGER_PLUG_SENSE_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1726 BLE_OUTPUT_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1726 SOM_INPUT2_MCU 1 "ON" 0 "OFF" ;
VAL_ 1726 SOM_INPUT1_MCU 1 "ON" 0 "OFF" ;
VAL_ 1726 V3_LDO_PGOOD_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1726 AUX_BATTERY_CHARGING_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1726 Sig_5V_Dc_Dc_PGOOD_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1726 Sig_3_8V_DC_DC_PGOOD_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1329 Fast_Charger_Alive 1 "FC Alive" 0 "SNA" ;
VAL_ 1709 Play_Pause_double_press 1 "ON" 0 "OFF" ;
VAL_ 1709 Accept_Conf_double_press 1 "ON" 0 "OFF" ;
VAL_ 1709 Reject_Cancel_double_press 1 "ON" 0 "OFF" ;
VAL_ 1709 Play_Pause_Long_Press_Voice_Assist 1 "ON" 0 "OFF" ;
VAL_ 1709 Accept_Conf_Long_Press_Vol_Up 1 "ON" 0 "OFF" ;
VAL_ 1709 Reject_Cancel_Long_Press_Vol_Down 1 "ON" 0 "OFF" ;
VAL_ 1709 Drive_Mode_Long_Press 1 "ON" 0 "OFF" ;
VAL_ 1709 Play_Pause_Short_Press 1 "ON" 0 "OFF" ;
VAL_ 1709 Accept_Conf_Short_Press 1 "ON" 0 "OFF" ;
VAL_ 1709 Reject_Cancel_Short_Press 1 "ON" 0 "OFF" ;
VAL_ 1709 Drive_Mode 1 "ON" 0 "OFF" ;
VAL_ 1708 Cruise_Mode_Short_Press 1 "ON" 0 "OFF" ;
VAL_ 1708 Long_press_of_Right_Indicator 1 "Active" 0 "Not Active" ;
VAL_ 1708 Long_press_of_Left_Indicator 1 "Active" 0 "Not Active" ;
VAL_ 1708 Cruise_Mode_Long_Press 1 "ON" 0 "OFF" ;
VAL_ 1708 High_Low_Beam_Long_Press_DRL 1 "ON" 0 "OFF" ;
VAL_ 1708 Horn 1 "ON" 0 "OFF" ;
VAL_ 1708 Right_Indicator 1 "ON" 0 "OFF" ;
VAL_ 1708 Left_Indicator 1 "ON" 0 "OFF" ;
VAL_ 1708 Cruise_Mode 1 "ON" 0 "OFF" ;
VAL_ 1708 High_Low_Beam_Short_Press 1 "ON" 0 "OFF" ;
VAL_ 31 VCU_IGN_Status 4 "EOL IGN ON" 3 "Invalid" 2 "ON" 1 "OFF" 0 " SNA" ;
VAL_ 151 MCU_Sleep_WakeUp_ACK 2 " Invalid" 1 " Positive ACK" 0 " Negative ACK" ;
VAL_ 136 External_SOC_reset 1 "SOC Reset Wakeup" 0 "Sleep " ;
VAL_ 136 Wake_up_Req_BMS 2 "RTC Wakeup" 1 "Wakeup" 0 "Sleep (Default)" ;
VAL_ 3 WakeUp_Sleep_Request 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Reserved" 2 " All Sleep" 1 "All WakeUp" 0 " SNA" ;
VAL_ 11 Battery_Wakeup_Request 1 " Wakeup" 0 " Sleep" ;
VAL_ 64 MBMS_Wake_Up_Reason 7 "EOL WakeUp" 6 "RTC Wakeup" 5 " SoC Push Button Wakeup " 4 " IMU Wakeup " 3 " Charger CAN Wakeup " 2 " Reserved" 1 " Ready to Sleep" 0 " SNA " ;
VAL_ 79 Auth_Status_BMS00 1 "Successful" 0 "Unsuccessful" ;
VAL_ 163 VCU_Auth_Response_to_MCU 1 "Successful" 0 "Unsuccessful" ;
VAL_ 284 Contactor_state_00 9 " BC_SECONDARY_CONNECT_CHG" 8 " BC_SECONDARY_CONNECT_DSG" 7 "DISCONNECT_PENDING" 6 "CONNECTED" 5 "CONNECT_PENDING" 4 "CONNECT_PRECHARGEMAIN" 3 "CONNECT_PRECHARGE" 2 "DISCONNECTED" 1 " NOT_AVAILABLE" 0 "NOT_APPLICABLE" ;
VAL_ 284 BMS_Mode_00 8 " Invalid" 7 " Fault / Error " 6 " Sleep " 5 " Discharge " 4 " Charge " 3 " Standby " 2 " Reserved" 1 " Sleep Pending" 0 " Initialise " ;
VAL_ 284 Battery_Precharge_failure_status_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_charge_inhibit_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_discharge_inhibit_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Derate_Drive_Current_Flag_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Derate_Charge_Current_Flag_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Inhibit_Regen_Fault_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 Battery_Permanent_Fault_10ms 1 "Fault" 0 "No fault" ;
VAL_ 284 BMS00_Short_circuit_detection_error_10ms 1 " Fault" 0 "No fault" ;
VAL_ 304 Charging_Mode_00 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 288 Charger_Mode_Request 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 528 Derate_Warning 1 "True" 0 "False" ;
VAL_ 528 Throttle_Deviation_Status 1 "Fault" 0 "No Fault" ;
VAL_ 290 Battery_Voltage_Deviation_Error 1 "Error" 0 "Error Free" ;
VAL_ 290 Battery_Precharge_failure_status_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Precharge_failure_status_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Precharge_failure_status_02 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_charge_inhibit_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_charge_inhibit_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_charge_inhibit_02 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_discharge_inhibit_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_discharge_inhibit_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_discharge_inhibit_02 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Drive_Current_Flag_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Drive_Current_Flag_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Drive_Current_Flag_02 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Charge_Current_Flag_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Charge_Current_Flag_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Derate_Charge_Current_Flag_02 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Inhibit_Regen_Fault_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Inhibit_Regen_Fault_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Inhibit_Regen_Fault_02 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Permanent_Fault_00 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Permanent_Fault_01 1 " Fault" 0 "No fault" ;
VAL_ 290 Battery_Permanent_Fault_02 1 " Fault" 0 "No fault" ;
VAL_ 352 Reset_SOC_from_OCV 1 "Enable" 0 "Disable" ;
VAL_ 352 Balancing_Status_00 1 "ON" 0 "OFF" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell1 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell2 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell3 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell4 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell5 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell6 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell7 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell8 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell9 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell10 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell11 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell12 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_sensor_failureCell13 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell1 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell2 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell3 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell4 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell5 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell6 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell7 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell8 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell9 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell10 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell11 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell12 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Voltage_out_of_range_Cell13 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature1_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature2_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature3_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature4_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature1_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature2_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature3_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Battery_pack_temperature4_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Current_sensor_failure_Open_circuit 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Current_sensor_failure_Short_circuit 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_current_charge_Warning 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_VOOR_CutOff_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Precharge_too_fast_Info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Precharge_too_slow_Info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Precharge_failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_MOSFETs_connection_failed 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_MOSFETs_disconnection_failed 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_PDU_temperature_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_PDU_temperature_error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_voltage_charge_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_voltage_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_voltage_charge_Permanent_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_voltage_regen_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_voltage_regen_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Under_voltage_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Under_voltage_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Under_voltage_Permanent_Fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_temperature_charge_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_temperature_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_temperature_drive_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_temperature_drive_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_temperature_due_to_Cell_vent_error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_temperature_due_to_Cell_vent_Failure 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Short_circuit_detection_error_info 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Short_circuit_detection_permanent_fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Cell_failure_permanent_fault 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Over_current_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1040 BMS00_Low_temperature_during_charging_warning_info 1 " Fault" 0 "No fault" ;
VAL_ 1041 Batt_Derate_Eco 1 "Fault" 0 "No fault" ;
VAL_ 1041 PDU_Derate_Eco 1 "Fault" 0 "No fault" ;
VAL_ 1041 thermal_runaway_shadow_fault 1 "Fault" 0 "No Fault" ;
VAL_ 1041 thermal_runaway_fault 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Cell_Voltage_Rise_Deration_in_HC 1 " Fault" 0 "No fault" ;
VAL_ 1041 ChargerReAuthWarning 1 "Fault" 0 "No Fault" ;
VAL_ 1041 ChargerReAuthError 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Overcharge_Protection 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Battery_Temperature_Blanket_Cut_off_Grade4 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Battery_Pack_Temperature_Error_Sleep 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_Pack_Temperature_Rise_Sleep 1 "Fault" 0 "No Fault" ;
VAL_ 1041 PDU_Temperature_Error_during_Sleep 1 "Fault" 0 "No Fault" ;
VAL_ 1041 PDU_Temperature_Rise_Detection 1 "Fault" 0 "No Fault" ;
VAL_ 1041 PDU_Temperature_Rise_Detection_Sleep 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Battery_Temp_Increase_flt_sleep 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Battery_Temp_Fault_during_Slp 1 "Fault" 0 "No Fault" ;
VAL_ 1041 BMS_3_3V_not_good_fault 1 "Fault" 0 "No Fault" ;
VAL_ 1041 External_Flash_COM_Lost 1 "Fault" 0 "No Fault" ;
VAL_ 1041 AFE_COM_LOST 1 "Fault" 0 "No Fault" ;
VAL_ 1041 Supply_9V_Not_Good 1 "Fault" 0 "No Fault" ;
VAL_ 1041 SUPPLY_12V_NOT_GOOD 1 "Fault" 0 "No Fault" ;
VAL_ 1041 SBC_FAULT_ACTIVE 1 "Fault" 0 "No Fault" ;
VAL_ 1041 PDUerrorCutOff_60 1 "Fault" 0 "No Fault" ;
VAL_ 1041 TempRiseChrgDtctn_60 1 "Fault" 0 "No Fault" ;
VAL_ 1041 TempRiseDriveDtctn_60 1 "Fault" 0 "No Fault" ;
VAL_ 1041 SOCRegenWarn 1 "True" 0 "False" ;
VAL_ 1041 TempRegen_Warning 1 "True" 0 "False" ;
VAL_ 1041 CompSpikeDtctn_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 1041 CompSpike_Error 1 "Fault" 0 "No Fault" ;
VAL_ 1041 CellDip_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 1041 CellDip_Error 1 "Fault" 0 "No Fault" ;
VAL_ 1041 BattTempAbNormal_Fault 1 "Fault" 0 "No Fault" ;
VAL_ 1041 BMS00_Low_temperature_during_charging_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS00_Low_temperature_during_driving_Warning 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS00_Low_temperature_during_driving_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 Over_time_to_fast_charge_Error 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS00_Voltage_sensor_failureCell14 1 " Fault" 0 "No fault" ;
VAL_ 1041 BMS00_Voltage_out_of_range_Cell14 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature5_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature6_sensor_failure 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature5_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 1041 Battery_pack_temperature6_out_of_range 1 " Fault" 0 "No fault" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_1 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_2 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_3 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_4 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_5 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_6 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_7 1 "O" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_8 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_9 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_10 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_11 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_12 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_13 1 "ON" 0 "OFF" ;
VAL_ 368 BMS00_Balancing_Status_Battery_Cell_14 1 "ON" 0 "OFF" ;
VAL_ 531 BoostCharger_billing_status 1 "billing required" 0 "billing not required" ;
VAL_ 531 Charger_Status_Signal 1 "Ready" 0 "Not Ready" ;
VAL_ 531 Charger_Mode 7 "Standby" 6 "Balancing" 5 "CV End" 4 "CC End" 3 "Charge Complete" 2 "CV" 1 "CC" 0 "Charge Disabled" ;
VAL_ 531 Slow_Charger_Fault_Information 3 "Reserved" 2 "Reserved" 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_output_Short_circuit_error 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_under_temperature_error 1 "Error " 0 " No Error" ;
VAL_ 531 Charger_over_temperature_error 1 "Error " 0 " No Error" ;
VAL_ 531 Low_AC_voltage_error 1 "Error " 0 " No Error" ;
VAL_ 531 Rectifier_hardware_error 1 "Error " 0 " No Error" ;
VAL_ 531 Authentication_Error 1 "Error " 0 " No Error" ;
VAL_ 531 Battery_Parameter_Time_out_error 1 "Error " 0 " No Error" ;
VAL_ 531 Data_Corruption_error 1 "Error " 0 " No Error" ;
VAL_ 531 Charge_control_message_timeout_error 1 "Error " 0 " No Error" ;
VAL_ 1617 Battery_Cell_Type 7 "Reserved3" 6 "Reserved2" 5 "Reserved1" 4 "PTLLFP" 3 "StoreDot" 2 "BAK" 1 "LG" 0 "Samsung" ;
VAL_ 275 MCU_Power_Status 3 " Error" 2 " Sleep" 1 " Wakeup" 0 " SNA" ;
VAL_ 275 Regen_Status 4 "Coast_Forced_Brake_Regen" 3 "Coast_Brake_Regen" 2 "Coast_Forced_Regen" 1 "Coast_Regen" 0 "Not Active" ;
VAL_ 275 Cruise_Control_Status 1 " Active" 0 "Not Active" ;
VAL_ 275 Vehicle_Discharge_Mode 11 "BOOST2" 10 "GH3" 9 "GH2" 8 "GH1" 7 "Hyper" 6 "LIMPHOME" 5 "REVERSE" 4 "BOOST" 3 "SPORT" 2 "NORMAL" 1 "ECO" 0 "PARK" ;
VAL_ 275 Hill_Hold_Response 3 "Going to Deactivate" 2 "Hill Hold Failure to Activate" 1 "Active" 0 "Not Active" ;
VAL_ 803 Disconnect_ACK 1 "Positive ACK" 0 "Negative ACK" ;
VAL_ 819 Vehicle_Wrong_Direction_Flag 1 "Error" 0 "No Error" ;
VAL_ 819 Motor_Temperature_Derating_Flag 1 "Enable" 0 "Disable" ;
VAL_ 819 MCU_Temperature_Derating_Flag 1 "Enable" 0 "Disable" ;
VAL_ 819 MCU_Calibration_Status 3 "Done" 2 "InProgress" 1 "Pending" 0 "Not Active" ;
VAL_ 819 Software_overcurrent_protection_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Software_overvoltage_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_Protection 1 " Error" 0 " No Error" ;
VAL_ 819 Failure_for_motor_parameter_tuning 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_Overload_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 U_phase_hall_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_overtemperature_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Motor_overtemperature_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Encoder_disconnection_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Overvoltagebaseline_of_hardware_is_wrong_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Stalling_Fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 DC_Bus_undervoltage_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 CAN_communication_abnormal_failure 1 " Error" 0 " No Error" ;
VAL_ 819 Motor_over_speed_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Motor_temperature_sensor_disconnection_fault_Grade_3 1 " Error" 0 " No Error" ;
VAL_ 819 Hardware_overcurrent_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Hardware_overvoltage_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Drive_power_under_voltage_fault 1 " Error" 0 " No Error" ;
VAL_ 819 The_resolver_connector_is_loose_and_abnormal 1 " Error" 0 " No Error" ;
VAL_ 819 Controller_drive_abnormal_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Power_supplyof_drive_board_exceeds_the_upper_limit 1 " Error" 0 " No Error" ;
VAL_ 819 Low_voltage_power_supply_under_voltage_fault 1 " Error" 0 " No Error" ;
VAL_ 819 U_phase_Hall_disconnection_fault 1 " Error" 0 " No Error" ;
VAL_ 819 V_phase_Hall_disconnection_fault 1 " Error" 0 " No Error" ;
VAL_ 819 W_phase_Hall_disconnection_fault 1 " Error" 0 " No Error" ;
VAL_ 819 V_phase_Hall_abnormal_fault 1 " Error" 0 " No Error" ;
VAL_ 819 W_phase_Hall_abnormal_fault 1 " Error" 0 " No Error" ;
VAL_ 819 Throttle_Wiper1_Fail 1 " Error" 0 " No Error" ;
VAL_ 819 Throttle_Wiper2_Fail 1 " Error" 0 " No Error" ;
VAL_ 323 About_Scooter_Event 6 "SCROLL_DOWN" 5 "SCROLL_UP" 4 "CLEAR_STORAGE_REJECT" 3 "CLEAR_STORAGE_ACCEPT" 2 "CLEAR_STORAGE" 1 "CHECK_VERSION" 0 "SNA" ;
VAL_ 323 HMI_StatusBar_Stt 6 "FAST_CHARGER" 5 "SLOW_CHARGER" 4 " INITIALIZING_HYPERCHARGER" 3 "NOT_CHARGING" 2 "CHARGING_SLOWLY" 1 "CHARGE_COMPLETE" 0 "SNA" ;
VAL_ 323 Cruise_Control_Availability 3 "NOT_AVAILABLE_SPD_OOR" 4 "SNA" 2 "NOT_AVAILABLE_MODE_UNSUITABLE" 1 "ACTIVE" 0 "AVAILABLE_NOT_ACTIVE" ;
VAL_ 323 Available_Driving_Modes 5 "Reserve" 4 "ALL_MODES_AVAILABLE_EXPECT_HYPER" 3 "NO MODES AVAILABLE" 2 "NORM SPO HYP NOT AVAILABLE" 1 "SPO HYP NOT AVAILABLE / Default" 0 "ALL MODES AVAILABLE" ;
VAL_ 323 Vehicle_Mode_Level_1 6 "Boost Charge" 5 "Discharge" 4 "Fast Charge (FC)" 3 "Slow Charge (SC)" 2 "Safe" 1 "Idle" 0 "SNA" ;
VAL_ 323 Vehicle_Mode_Level_2 15 "BC_CHARGE_FAULT" 14 "BC_CHARGE_IDLE" 13 "ALARM" 12 "DRIVE" 11 "PARK" 10 "FC_CHARGE ACTIVE" 9 "FC_CHARGE FAULT" 8 "FC_CHARGE IDLE" 7 "SC_CHARGE ACTIVE" 6 "SC_CHARGE FAULT" 5 "SC_CHARGE IDLE" 4 "VEHICLE IDLE" 3 "SLEEP" 2 "VACATION" 1 "BC_CHARGE_ACTIVE" 0 "SNA" ;
VAL_ 323 Vehicle_Mode_Level_3 10 "ABOUT_SCOOTER" 9 "DYNO" 8 "SERVICE" 7 "ECOS" 6 "DRIVE FAULT" 5 "LIMPHOME" 4 "DRIVE NORMAL" 3 "AUX. BATT CHARGE" 2 "DIAGNOSTIC" 1 "ECU POWERUP" 0 "SNA" ;
VAL_ 323 Vehicle_Mode_Level_4 10 "BOOST2" 4 "BOOST" 1 "ECO" 9 "GH3" 8 "GH2" 7 "GH1" 6 "HYPER" 5 "REVERSE" 3 "SPORT" 2 "NORMAL" 0 "SNA" ;
VAL_ 323 Drive_Request 3 "Reserved" 2 " Drive Enable" 1 "Drive Disable" 0 "SNA" ;
VAL_ 323 Charging_status_of_AUX_battery 1 " ON" 0 " OFF" ;
VAL_ 339 SSB_Button_Status 3 "Reserved" 2 "Long Press" 1 "Normal Press" 0 " Not Pressed" ;
VAL_ 347 Front_Brake_Press 1 "Active" 0 "Not Active" ;
VAL_ 347 Rear_Brake_Press 1 "Active" 0 "Not Active" ;
VAL_ 347 Discharge_Mode 11 "BOOST2" 10 "GH3" 9 "GH2" 8 "GH1" 7 "Hyper" 6 "LIMPHOME" 5 "REVERSE" 4 "BOOST" 3 "SPORT" 2 "NORMAL" 1 "ECO" 0 "PARK" ;
VAL_ 347 Front_Brake_Status 1 "Applied" 0 "Not Applied" ;
VAL_ 347 Rear_Brake_Status 1 "Applied" 0 "Not Applied" ;
VAL_ 347 Cruise_Control_Request 3 "Reserved" 2 "Activated Confirmed" 1 "Activated Set" 0 "Not Activated" ;
VAL_ 851 Side_Stand_Status 1 "Applied" 0 "Removed" ;
VAL_ 851 Seat_Lock_Status 1 "Lock" 0 "Unlock" ;
VAL_ 851 Cruise_Control_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Right_Indicator_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Left_Indicator_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 High_Beam_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Service_Indicator_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Vehicle_Error_Indicator_TTL 2 "ON_Red" 1 "ON_Orange" 0 "OFF" ;
VAL_ 851 Charge_TTL 3 "ON_InProgress(Yellow)" 2 "ON_Complete(Green)" 1 "ON_Error(Red)" 0 "OFF" ;
VAL_ 851 Position_Lamp_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Brake_Pad_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Low_Beam_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Low_Battery_Alert_TTL 1 "ON" 0 "OFF" ;
VAL_ 851 Indicator_On_Reason 11 "Hazard_Fault" 10 "Security" 9 " Indicator Error" 8 " Hazard" 7 " FindMyBike" 6 " Reverse Mode" 5 " Alarm" 4 " Lock_Ind" 3 " Unlock_Ind" 2 " Turn_Right_Indicator" 1 " Turn_Left_Indicator" 0 " None" ;
VAL_ 1088 StrLockUnlockCmd 2 " Unlock " 1 " Lock " 0 "Dont Care" ;
VAL_ 1216 ESCL_Status 3 " Bolt Stuck" 2 " Unlock " 1 " Lock " 0 "Dont Care" ;
VAL_ 1217 Motor_shrt_Flt 1 "Motor_shrt_Flt" 0 "No Fault" ;
VAL_ 1217 Motor_Opn_Flt 1 "Motor_Opn_Flt" 0 "No Fault" ;
VAL_ 1217 Motor_temp_Shut 1 "Motor_temp_Shut" 0 "No Fault" ;
VAL_ 1217 plngr_stk_sts 1 "Plunger_stuck" 0 "No Fault" ;
VAL_ 1217 Hall_2_sts 1 "Lock_Not_Sensed" 0 "Lock_Sensed" ;
VAL_ 1217 Hall_1_sts 1 "Lock" 0 "Unlock" ;
VAL_ 1217 ESCL_Sleep_Ack 170 "Sleep_Ack" 0 "Don't Care" ;
VAL_ 1217 LockUnlockErrorIndication 0 " Invalid" 44510 " Error" ;
VAL_ 99 Wakeup_Reason 1 " CAN WakeUp" 0 " No Request (Default Value)" ;
VAL_ 20 Fast_Charger_Plug_Status 1 "Connected" 0 "Not connected" ;
VAL_ 20 Fast_Charger_Contactor_Status 1 "Closed" 0 "Not Closed" ;
VAL_ 20 Fast_Charge_Terminate_Request 1 "Terminated" 0 "Not Terminated" ;
VAL_ 1288 Charging_system_error 1 " Error" 0 "Error Free" ;
VAL_ 1288 EV_supply_equipment_malfunction 1 " Error" 0 "Error Free" ;
VAL_ 1288 EV_incompatibility 1 " Incompatible" 0 "compatible" ;
VAL_ 1288 EV_supply_equipment_stop_control 1 " Shutdown or stop charging" 0 "Operating" ;
VAL_ 1288 EV_supply_equipment_status 1 " charging" 0 "Standby" ;
VAL_ 1288 Vehicle_connector_latched 1 " Latched" 0 "Unlatched" ;
VAL_ 1288 EV_supply_equipment_ready 1 "Ready" 0 "Not Ready" ;
VAL_ 1288 Waiting_state_before_charging_start 1 "Energy Transfer" 0 "Waiting" ;
VAL_ 1288 EVSE_Emergency_Stop 1 "True" 0 "False" ;
VAL_ 1289 Control_protocol_number 0 " IEC61851" ;
VAL_ 1296 Voltage_Control_Option 1 "Voltage control Enabled" 0 "No voltage control " ;
VAL_ 1754 BMS_NVM_Failure_Status 1 "YES" 0 "NO" ;
VAL_ 1754 BMS00_9V_Supply_Status 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_12V_Supply_voltage_Status 1 "ON" 0 "OFF" ;
VAL_ 1754 SBC_Limp_Input 1 "ON" 0 "OFF" ;
VAL_ 1754 AFE_Fault_Input 1 "ON" 0 "OFF" ;
VAL_ 1754 Charge_Over_Current 1 "ON" 0 "OFF" ;
VAL_ 1754 Discharge_Over_current 1 "ON" 0 "OFF" ;
VAL_ 1754 RTC_Interrupt 1 "ON" 0 "OFF" ;
VAL_ 1754 RTC_Clock_IN 1 "ON" 0 "OFF" ;
VAL_ 1754 Charger_Plug_Sense 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS_Status 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_12V_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_9V_Supply_Disable 1 "ON" 0 "OFF" ;
VAL_ 1754 BMS00_5V_Peripheral_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Gate_Drivers_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Charge_MOSFET_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Discharge_MOSFET_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 Pre_Charge_Enable 1 "ON" 0 "OFF" ;
VAL_ 1754 AFE_Reset_Command 1 "ON" 0 "OFF" ;
VAL_ 1754 Overload_Clear 1 "ON" 0 "OFF" ;
VAL_ 1754 Enable_3V3_Measure 1 "ON" 0 "OFF" ;
VAL_ 2000 Vehicle_Type_Extended 27 "M3X+_4.5KWh" 26 "S1_Pro+_Gen3_4KWh" 25 "S1_Pro_Gen3_4KWh" 24 "S1_Pro_Gen3_3KWh" 23 "S1X+_Gen3_4KWh" 22 "S1X_Gen3_4KWh" 21 "S1X_Gen3_3KWh" 20 "S1X_Gen3_2KWh" 19 "M3X_4.5KWh" 18 "M3X_3.5KWh" 17 "M3X_2.5KWh" 16 "S1X_4KWh     " 15 "S1X 3KWh      " 14 "S1X 2KWh" 13 "S1X+ Phase2 4KwH" 12 "S1X+ Phase1 4KwH " 11 "Reserved" 10 "S1X+ Phase2" 9 "S1 Pro Gen2 W/O Upgrade" 8 "S1 Air Fleet" 7 "S1X+" 6 "S1 Pro Gen2 With Upgrade" 5 "S1 Air Customer" 4 "S1 2KW" 3 "S1 Pro with upgrade" 2 "S1 Pro wo upgrade" 1 "S1" ;
VAL_ 2000 App_Cmd 19 "Alarm_Ignore" 18 " Find My Bike Sound Turn ON Request " 17 " Find My Bike Light Turn ON Request" 16 " Diagnostic Request" 9 "  Reserved." 8 " Reserved." 7 " Vehicle Remote Immobilize OFF " 6 " Vehicle Remote Immobilize ON " 5 " Trunk Open " 4 " Vehicle HeadLamps OFF" 3 "  Vehicle HeadLamp ON" 2 " Vehicle UNLOCK " 1 " Vehicle LOCK" 0 "  No Command (By Default) " ;
VAL_ 2000 Proximity_Detect 3 "CAPP_PROXIMITY_INVALID" 2 "CAPP_PROXIMITY_SILENT_UNLOCK " 1 "CAPP_PROXIMITY_UNLOCK " 0 "CAPP_PROXIMITY_LOCK" ;
VAL_ 2000 Seat_Lock_Gpio 1 " Seat Open" 0 " Seat Close" ;
VAL_ 2000 Som_Status 7 "Not_Defined" 6 "KeyON" 5 "KeyOFF" 4 "FastBoot" 3 "StandBy" 2 "Sleep" 1 "Booting" 0 " OFF" ;
VAL_ 2000 HMI_Ctrl_Cmd 124 "HILL_HOLD_EXIT_NUDGE" 123 "DCHRG_INHBT_ERROR" 122 "DERATE_DRV_FLT" 121 "DERATE_THROT_ERROR" 120 "RID_ACCS_RVKED_PARK_IN_30S" 119 "CHGR_LATCH_NOT_CONED" 118 "CHGR_PWR_OFF" 117 "CHGR_TEMPORARY_FAULT" 116 "CHGR_PERMANENT_FAULT" 115 "LOCK_FAIL_CTA" 114 "UNLOCK_FAIL_CTA" 113 "NORMAL_MODE_AVAILABLE" 112 "STOP_CHRG_OVER_TEMP_ERR" 111 "CHRG_CMPLT_UNPLG_CHRGR" 110 "LOCK_IN_PRGS" 109 "OVR_CHRG_PROTECTION" 108 "CNTCR_TOGGLING_ISSUE" 107 "BMS_GRADE4_CUTOFF" 106 "CAN_FAIL_WAIT_CONTACT_CS" 105 "DISABLED_TEMP_MODE_RISE_SWITCH" 104 "DISABLED_SPIKE_MODE_SWITCH" 103 "DISABLED_CELLDIP_MODE_SWITCH" 102 "CHRG_TEMP_RISE_GRADE2" 101 "DRV_TEMP_RISE_GRADE2" 100 "RERATE_HIGHER_MODE_SLP_OT" 99 "SLP_TEMP_RISE_GRADE2" 98 "RERATING_REVIVAL_CAN" 97 "RERATE_HIGHER_MODE_SPIK_ERR" 96 "RERATE_HIGHER_MODE_CELL_DIP" 95 "RERATE_HIGHER_MODE_CHRG_TEMP" 94 "RERATE_HIGHER_MODE_DRV_TEMP" 93 "CAN_FAIL" 92 "SPIKE_ERR_GRADE2" 91 "CELL_DIP_ERR_GRADE2" 90 "Initiate Navigation Ping Test" 89 "Initiate Tripometer Reset" 88 "MCU_GRADE2_DISABLED_HIGHER" 87 "PDU_WARN_CHRG" 86 "DERATE_LOW_TEMP_ERR_START" 85 "DERATE_LOW_TEMP_WARN_START" 84 "CHARGE_BATT_LOWTEMP_WARN" 83 "BATT_TEMP_SNSR_FAIL_ALL_CHRG" 82 "BATT_TEMP_SNSR_FAIL_CHRG" 81 "VLT_SNSR_FAIL_ALL_CHRG" 80 "VLT_SNSR_FAIL_CHRG" 79 "BATT_TEMP_OOR_ALL_CHRG" 78 "BATT_TEMP_OOR_CHRG" 77 "BATT_SNSR_FAIL_CONTACT_CS" 76 "MCU_GRADE3_CONTACT_CS" 75 "MCU_GRADE3_RESTART" 74 "MCU_GRADE3_PARK" 73 "MCU_GRADE2_CONTACT_CS" 72 "MCU_GRADE2_RESTART" 71 "MCU_GRADE2_ECO" 70 "OVER_CURR_CHRG_WARN" 69 "BATT_TEMP_SNSR_FAIL_ALL" 68 "BATT_TEMP_SNSR_FAIL" 67 "VLT_SNSR_FAIL_ALL" 66 "VLT_SNSR_FAIL" 65 "CURR_SNSR_FAIL_OC_SC" 64 "BATT_TEMP_OOR_ALL" 63 "BATT_TEMP_OOR" 62 "HOLD_BRK_SSB_TO_DRV" 61 "APPLY_SIDE_STAND_TO_LOCK" 60 "SIDE_STAND_OPEN" 59 "CLOSE_SIDE_STAND_TO_DRIVE" 58 "DERATE_CHRG_OVER_TEMP_WARN" 57 "RERATING_REVIVAL_MCU" 56 "RERATING_REVIVAL_MOT" 55 "DERATE_MCU_CHILL_WARN" 54 "DERATE_MOTE_CHILL_WARN" 53 "RERATING_HIGHER_MODE_MCU" 52 "RERATING_HIGHER_MODE_MOT" 51 "RERATING_HIGHER_MODE_OT" 50 "DERATE_MCU_TEMP_WARN_START" 49 "DERATE_MOT_TEMP_WARN_START" 48 "DISABLED_MCU_MODE_SWITCH" 47 "DISABLED_MOT_MODE_SWITCH" 46 "DERATE_MCU_COOLDOWN_WAIT" 45 "DERATE_MOT_COOLDOWN_WAIT" 44 "DERATE_MCU_TEMP_ERROR_START" 43 "DERATE_MOT_TEMP_ERROR_START" 42 "CHARGE_NOT_HAPPENING" 41 "RERATING_REVIVAL_OT" 40 "DISABLED_OT_MODE_SWITCH" 39 "DERATE_TEMP_COOLDOWN_WAIT" 38 "DERATE_OVER_TEMP_ERR_INPGRS" 37 "DERATE_OVER_TEMP_ERR_START" 36 "DERATE_OVER_TEMP_WARN_INPGRS" 35 "DERATE_OVER_TEMP_WARN_START" 34 "GENERAL_LOW_BATTERY" 33 "DERATE_SOC_20" 32 "DERATE_SOC_20_WARNING" 31 "DERATE_SOC_15" 25 "ENTERING_INTO_DEEP_SLEEP" 24 "TRUNK_CLOSE_NOTIFICATION" 23 "TRUNK_OPEN_NOTIFICATION" 22 "TYPE1_ERROR_CLEAR" 21 "TYPE1_ERROR_SET" 20 "TYPE2_ERROR_CLEAR" 19 "TYPE2_ERROR_SET" 18 "TYPE3_ERROR_CLEAR" 17 "TYPE3_ERROR_SET" 16 "DERATE_SOC_15_WARNING" 15 "RERATING_HIGHER_MODE" 14 "DERATE_PDU_TEMP_WARN_INPGRS" 13 "DERATE_PDU_TEMP_WARN_START" 12 "DERATE_SOC_3" 11 "DERATE_SOC_3_WARNING" 10 "DERATE_PDU_TEMP_ERROR_INPGRS" 9 "DERATE_PDU_TEMP_ERROR_START" 8 "RERATING_REVIVAL_PDU" 7 "DERATE_AFE_FAULT" 6 "DISABLED_HIGHER_MODE" 5 "DISABLED_PDU_MODE_SWITCH" 4 "DERATE_CHARGE_TO_RIDE" 3 "DERATE_PDU_COOLDOWN_WAIT" 2 "DERATE_AFE_FAULT_CCS" 1 "Activate Passcode Window" 0 "No Command" ;
VAL_ 2000 Vehicle_type 0 "Refer_Vehcile_Type_Extended" 3 "S1 with upgrade" 2 "S1 w/o upgrade" 1 "S1" ;
VAL_ 2000 ESCL_Timeout_Error 1 " Error" 0 " No Error" ;
VAL_ 2001 lin_mng_sts 5 "CMN_MODULE_STS_STOPPING" 4 "CMN_MODULE_STS_STARTED" 3 "CMN_MODULE_STS_STARTING" 2 "CMN_MODULE_STS_STOPPED" 1 "CMN_MODULE_STS_CREATING" 0 "CMN_MODULE_STS_IDLE" ;
VAL_ 2001 lin_mng_proc_sts 2 "LIN_MNG_PROC_ERR" 1 "LIN_MNG_PROC_BUSY" 0 "LIN_MNG_PROC_IDLE " ;
VAL_ 2001 lin_mng_state 2 " LIN_MNG_STATE_WAIT_WAKEUP" 1 " LIN_MNG_STATE_WAKEUP" 0 "LIN_MNG_STATE_SLEEP" ;
VAL_ 2006 VCU_Low_Voltage_Detect_Reset 1 "True" 0 "False" ;
VAL_ 2006 VCU_Loss_of_Clock_Reset 1 "True" 0 "False" ;
VAL_ 2006 VCU_Loss_of_Lock_Reset 1 "True" 0 "False" ;
VAL_ 2006 VCU_CMU_Loss_of_Clock_Reset 1 "True" 0 "False" ;
VAL_ 2006 VCU_Watchdog 1 "True" 0 "False" ;
VAL_ 2006 VCU_External_Reset_Pin 1 "True" 0 "False" ;
VAL_ 2006 VCU_Power_On_Reset 1 "True" 0 "False" ;
VAL_ 2006 VCU_JTAG_generated_reset 1 "True" 0 "False" ;
VAL_ 2006 VCU_Core_Lockup 1 "True" 0 "False" ;
VAL_ 2006 VCU_Software 1 "True" 0 "False" ;
VAL_ 2006 VCU_MDM_AP_System_Reset_Request 1 "True" 0 "False" ;
VAL_ 2006 VCU_Stop_Acknowledge_Error 1 "True" 0 "False" ;
VAL_ 2006 VCU_Memory_Allocation_Failed 1 "True" 0 "False" ;
VAL_ 2022 BMS_Low_Voltage_Detect_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Loss_of_Clock_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Loss_of_Lock_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_CMU_Loss_of_Clock_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Watchdog 1 "True" 0 "False" ;
VAL_ 2022 BMS_External_Reset_Pin 1 "True" 0 "False" ;
VAL_ 2022 BMS_Power_On_Reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_JTAG_generated_reset 1 "True" 0 "False" ;
VAL_ 2022 BMS_Core_Lockup 1 "True" 0 "False" ;
VAL_ 2022 BMS_Software 1 "True" 0 "False" ;
VAL_ 2022 BMS_MDM_AP_System_Reset_Request 1 "True" 0 "False" ;
VAL_ 2022 BMS_Stop_Acknowledge_Error 1 "True" 0 "False" ;
VAL_ 1602 Backlight_Driver_Fault_Input 1 "Fault" 0 "No Fault" ;
VAL_ 1602 Ambient_Light_Sensor_Interrupt 1 "ON" 0 "OFF" ;
VAL_ 1602 Power_Good 1 "ON" 0 "OFF" ;
VAL_ 1602 WARNING_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1602 ERROR_STATUS 1 "ON" 0 "OFF" ;
VAL_ 1602 LAST_RESET_REASON 10 " JTAG Reset" 9 " MDM DAP Reset" 8 " Lock Up Reset" 7 " SW Reset" 6 " Stop Mode ACK Error" 5 " LOL" 4 " LOC" 3 " Watchdog reset" 2 " LVD" 1 " PIN" 0 " POR" ;
VAL_ 1581 Charger_Connected 3 "Boost_Charger" 2 " Slow Charger" 1 " Fast Charger" 0 "None (By Default)" ;
VAL_ 1581 Charger_Plugged_In 1 " ON" 0 " OFF (By Default)" ;
VAL_ 1636 Reserved_TP5 1 "Enable" 0 "Disable" ;
VAL_ 1636 CALLING 1 "Enable" 0 "Disable" ;
VAL_ 1636 CRUISE_CONTROL 1 "Enable" 0 "Disable" ;
VAL_ 1636 ADVANCED_REGEN_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 FAST_CHARGING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 HYPER_CHARGING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 HILL_HOLD_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP4 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP3 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP2 1 "Enable" 0 "Disable" ;
VAL_ 1636 Reserved_TP1 1 "Enable" 0 "Disable" ;
VAL_ 1636 FREE_ROAM_VIEW 1 "Enable" 0 "Disable" ;
VAL_ 1636 INTER_CITY_TRIP_PLANNER 1 "Enable" 0 "Disable" ;
VAL_ 1636 ROAD_TRIP 1 "Enable" 0 "Disable" ;
VAL_ 1636 PUSH_LOCATION_TO_SCOOTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 MAPS_ON_HMI 1 "Enable" 0 "Disable" ;
VAL_ 1636 MOODS 1 "Enable" 0 "Disable" ;
VAL_ 1636 CONCERT_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 PLAY_MUSIC 1 "Enable" 0 "Disable" ;
VAL_ 1636 PARTY_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 CUSTOM_MOTOR_SOUNDS 1 "Enable" 0 "Disable" ;
VAL_ 1636 MEGAPHONE 1 "Enable" 0 "Disable" ;
VAL_ 1636 SCOOTER_WIDGETS 1 "Enable" 0 "Disable" ;
VAL_ 1636 NOTIFICATION_CENTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 FALL_DETECTION 1 "Enable" 0 "Disable" ;
VAL_ 1636 LIVE_LOCATION_SHARING 1 "Enable" 0 "Disable" ;
VAL_ 1636 HAZARD_LIGHTS 1 "Enable" 0 "Disable" ;
VAL_ 1636 EMERGENCY_SOS 1 "Enable" 0 "Disable" ;
VAL_ 1636 HARDWARE_FAILURE_PREDICTION 1 "Enable" 0 "Disable" ;
VAL_ 1636 RIDE_JOURNAL 1 "Enable" 0 "Disable" ;
VAL_ 1636 RIDE_STATS 1 "Enable" 0 "Disable" ;
VAL_ 1636 COMMUNITY 1 "Enable" 0 "Disable" ;
VAL_ 1636 FIND_MY_SCOOTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 PROFILES 1 "Enable" 0 "Disable" ;
VAL_ 1636 DOCS_ON_SCOOTER 1 "Enable" 0 "Disable" ;
VAL_ 1636 FASTER_CHARGING 1 "Enable" 0 "Disable" ;
VAL_ 1636 SLOW_CHARGING 1 "Enable" 0 "Disable" ;
VAL_ 1636 BRAKE_BY_WIRE_SETTINGS 1 "Enable" 0 "Disable" ;
VAL_ 1636 BRAKE_BY_WIRE 1 "Enable" 0 "Disable" ;
VAL_ 1636 AUTO_INDICATOR 1 "Enable" 0 "Disable" ;
VAL_ 1636 TRACTION_CONTROL 1 "Enable" 0 "Disable" ;
VAL_ 1636 FORCED_REGEN 1 "Enable" 0 "Disable" ;
VAL_ 1636 COAST_REGEN 1 "Enable" 0 "Disable" ;
VAL_ 1636 SPEED_LIMIT_ALERT 1 "Enable" 0 "Disable" ;
VAL_ 1636 RANGE_PREDICTION 1 "Enable" 0 "Disable" ;
VAL_ 1636 SMART_PARK 1 "Enable" 0 "Disable" ;
VAL_ 1636 DIY_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 REVERSE_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 ECO_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 NORMAL_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 SPORT_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 CAPP_LOCK_UNLOCK 1 "Enable" 0 "Disable" ;
VAL_ 1636 HYPER_MODE 1 "Enable" 0 "Disable" ;
VAL_ 1636 CAPP_VEHICLE_STATE 1 "Enable" 0 "Disable" ;
VAL_ 1636 PASSCODE_UNLOCK_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 EARLY_ACCESS_BETA_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 NEW_UPGRADES_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 PROXIMITY_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 ENERGY_INSIGHTS_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 MODE_FENCING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 TIME_FENCING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 GEO_FENCING_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 TOW_TAMPER_V2 1 "Enable" 0 "Disable" ;
VAL_ 1636 TAKE_ME_HOME_LIGHTS_V2 1 "Enable" 0 "Disable" ;

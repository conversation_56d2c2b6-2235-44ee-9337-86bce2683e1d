'-----------------------------------------------------------------------------
' converts the filenames which are passed via startup parameter to CSV files with the help of CANoe
'-----------------------------------------------------------------------------
Dim src_file, dst_file, pos

Set App = CreateObject("CANoe.Application")
Set args = WScript.Arguments

' read first command line parameter
arg0 = args.Item(0)
' configPath = args.Item(2)
' WScript.Echo configPath
configPath = "C:\Users\<USER>\Documents\Vector\CANoe\Sample Configurations 15.2.41\Configuration 20_06.cfg"
' COMPLETED folder path in the script directory
' 
blf_directory = args.Item(1)
' WScript.Echo "BLF Directory: " & blf_directory
Set fso = CreateObject("Scripting.FileSystemObject")
Set blf_folder = fso.GetFolder(blf_directory)

' Get the total count of BLF files
totalFiles = 0
For Each blf_file In blf_folder.Files
    If LCase(fso.GetExtensionName(blf_file.Name)) = "blf" Then
        totalFiles = totalFiles + 1
    End If
Next

' Initialize a counter for progress tracking
fileCount = 0
' WScript.Echo "Config Path: " & configPath
App.Open(configPath)
' create blf_directory & "\COMPLETED if it does not exist
If Not fso.FolderExists(blf_directory & "\COMPLETED") Then
    fso.CreateFolder(blf_directory & "\COMPLETED")
End If
' create blf_directory & "\CSV if it does not exist
If Not fso.FolderExists(blf_directory & "\CSV") Then
    fso.CreateFolder(blf_directory & "\CSV")
End If

For Each blf_file In blf_folder.Files
    If LCase(fso.GetExtensionName(blf_file.Name)) = "blf" Then
        src_file = blf_directory & "\" & blf_file.Name
        dst_file = blf_directory & "\CSV\" & fso.GetBaseName(blf_file.Name) & ".csv"

        If App.Measurement.Running Then
            App.Measurement.Stop
        End If

        WScript.Sleep 500

        Set Logging = App.Configuration.OnlineSetup.LoggingCollection(1)
        Set Exporter = Logging.Exporter

        With Exporter.Sources
            .Clear
            .Add(src_file)
        End With

        ' Load file
        Exporter.Load

        With Exporter.Destinations
            .Clear
            .Add(dst_file)
        End With
        Exporter.Save True
        ' Increment the counter and display progress
        fileCount = fileCount + 1
        ' WScript.Echo "Progress: " & fileCount & " out of " & totalFiles & " files converted."
        ' Move blf file to COMPLETED folder in the script directory
        fso.MoveFile src_file, blf_directory & "\COMPLETED\" & blf_file.Name
    End If
Next

' Quit the CANoe application after converting all files
' App.Quit
' Set App = Nothing
Set args = Nothing
Set fso = Nothing
Set blf_folder = Nothing

'breakpoint to stop the script
WScript.Echo "Press any key to continue..."

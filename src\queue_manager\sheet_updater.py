import time
import logging
from .core import QueueManager
from .interfaces.sheets import authenticate_sheets, update_sheet

logging.basicConfig(level=logging.INFO)

UPDATE_INTERVAL = 15 * 60  # 15 minutes


def format_queue_for_sheet(queue_rows):
    # Expected columns by the sheet (after modifications):
    # File Name, BLF Link, Converted Link, Queue #, Status, Last Updated (Sheet)
    # 'file_name' in the old code likely corresponds to 'drive_filename' from the DB.
    formatted_rows = []
    for row in queue_rows:
        formatted_rows.append([
            row.get('drive_filename', ''),  # Assuming 'file_name' was 'drive_filename'
            row.get('blf_link', ''),
            row.get('converted_link', ''),
            str(row.get('queue_number', '')),
            row.get('queue_status', ''),
            ''  # Placeholder for "Last Updated (Sheet)" for full sync.
                  # This timestamp is primarily set by individual updates.
                  # If a DB timestamp for this exists, it could be row.get('db_sheet_update_ts', '')
        ])
    return formatted_rows

def update_sheet_periodically():
    queue = QueueManager()
    sheets_service = authenticate_sheets()
    while True:
        all_rows = queue.get_all()
        data = format_queue_for_sheet(all_rows)
        update_sheet(sheets_service, data)
        logging.info("Sheet updated with current queue status.")
        time.sleep(UPDATE_INTERVAL)

if __name__ == '__main__':
    update_sheet_periodically() 
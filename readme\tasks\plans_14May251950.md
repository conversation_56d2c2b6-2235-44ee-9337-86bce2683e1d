# Plan: Robust BLF Queue and Download Management (14May251950)

## Objective
- Maintain a queueing database that tracks all BLF files in all subfolders of the Drive `BLF_FILES` folder.
- Track download status for each file (downloaded or not) and ensure files are downloaded to the correct local subfolder.
- Handle filename differences between Google Drive and Windows (e.g., replacing `:` with `_`).
- Use the database as the single source of truth for download and conversion initiation.

## Steps
1. **Database Schema Update**
    - Add fields: `drive_filename`, `local_filename`, `downloaded` (boolean), `subfolder`, `file_id`, etc.
2. **Polling Logic**
    - Recursively scan all subfolders in `BLF_FILES` on Drive.
    - For each file, update or insert a record in the queue DB with the correct subfolder and drive filename.
    - If the file is new, set `downloaded = False`.
3. **Download Logic**
    - For each record with `downloaded = False`, download the file to the correct local subfolder under `blf/`.
    - Replace illegal Windows filename characters (e.g., `:` → `_`) for `local_filename`.
    - After successful download, set `downloaded = True` in the DB.
4. **Conversion/Processing**
    - Only process files where `downloaded = True`.
    - Use `local_filename` and subfolder for local path resolution.
5. **Sync/Resilience**
    - If a file is deleted from Drive, mark as removed in DB or handle accordingly.
    - If a file is re-uploaded/changed, update the DB and reset `downloaded` as needed.
6. **Testing & Validation**
    - Test with files containing illegal characters.
    - Test with nested subfolders and multiple new files.
7. **Documentation**
    - Update README and developer docs to reflect new DB schema and workflow. 